"use client";

import React from "react";
import Link from "next/link";
import { Search, Clock, TrendingUp, ChevronRight } from "lucide-react";

// Mock data for search suggestions
const recentSearches = [
  "wireless headphones",
  "smartphone",
  "laptop",
  "smart watch"
];

const popularSearches = [
  "bluetooth speakers",
  "gaming accessories",
  "home office desk",
  "fitness tracker"
];

export function SearchSuggestions({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) {
  if (!isOpen) return null;

  return (
    <div className="absolute top-full left-0 right-0 mt-1 bg-card rounded-lg shadow-lg border border-gray-100 z-50 overflow-hidden">
      <div className="p-3 sm:p-4 grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
        <div>
          <div className="flex items-center gap-2 mb-3">
            <Clock className="h-4 w-4 text-gray-400" />
            <h3 className="font-medium text-sm text-gray-700">Recent Searches</h3>
          </div>
          <ul className="space-y-2">
            {recentSearches.map((search, index) => (
              <li key={index}>
                <button
                  className="flex items-center gap-2 w-full text-left px-3 py-2 rounded-lg hover:bg-gray-50 text-gray-700 hover:text-primary transition-colors"
                  onClick={() => {
                    // Handle search
                    onClose();
                  }}
                >
                  <Search className="h-3.5 w-3.5 text-gray-400" />
                  <span className="text-xs">{search}</span>
                </button>
              </li>
            ))}
          </ul>
        </div>

        <div>
          <div className="flex items-center gap-2 mb-3">
            <TrendingUp className="h-4 w-4 text-gray-400" />
            <h3 className="font-medium text-sm text-gray-700">Popular Searches</h3>
          </div>
          <ul className="space-y-2">
            {popularSearches.map((search, index) => (
              <li key={index}>
                <button
                  className="flex items-center gap-2 w-full text-left px-3 py-2 rounded-lg hover:bg-gray-50 text-gray-700 hover:text-primary transition-colors"
                  onClick={() => {
                    // Handle search
                    onClose();
                  }}
                >
                  <Search className="h-3.5 w-3.5 text-gray-400" />
                  <span className="text-xs">{search}</span>
                </button>
              </li>
            ))}
          </ul>
        </div>
      </div>

      <div className="p-3 border-t border-gray-100 bg-muted">
        <Link
          href="/search"
          className="flex items-center justify-center gap-1 text-primary hover:underline text-xs font-medium"
          onClick={onClose}
        >
          <span>Advanced Search</span>
          <ChevronRight className="h-3 w-3" />
        </Link>
      </div>
    </div>
  );
}
