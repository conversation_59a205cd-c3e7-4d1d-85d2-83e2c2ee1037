"use client";

import React from "react";
import { ProductGrid } from "./product-grid";
import { Trophy } from "lucide-react";
import { ProductBorderGrid } from "@/components/sections/product-border-grid";

// Best seller products data
const bestSellerProductsData = [
  { name: "Smart TV 55\"", category: "TV & Video", price: 699.99, discount: true, oldPrice: 899.99, rating: 4.7 },
  { name: "Harman Kardon Onyx Studio B1", category: "Audio Speakers", price: 1215.00, discount: false, rating: 4.8 },
  { name: "DSLR Camera", category: "Camera & Photo", price: 799.99, discount: true, oldPrice: 999.99, rating: 4.8 },
  { name: "Smartphone Pro", category: "Cell Phones", price: 899.99, discount: true, oldPrice: 999.99, rating: 4.9 },
  { name: "Noise Cancelling Headphones", category: "Headphones", price: 249.99, discount: true, oldPrice: 299.99, rating: 4.8 },
  { name: "4K Projector", category: "TV & Video", price: 549.99, discount: false, rating: 4.5 },
  { name: "Wireless Earbuds", category: "Headphones", price: 129.99, discount: false, rating: 4.7 },
  { name: "Wireless Charger", category: "Accessories", price: 49.99, discount: false, rating: 4.6 },
  { name: "Gaming Console Pro", category: "Gaming", price: 499.99, discount: true, oldPrice: 549.99, rating: 4.9 },
  { name: "Ultra HD Action Camera", category: "Camera & Photo", price: 349.99, discount: false, rating: 4.7 }
];

type BestSellerSectionProps = {
  title?: string;
  products?: typeof bestSellerProductsData;
  shopMoreLink?: string;
  sectionType?: "recommended" | "featured" | "bestseller";
  backgroundColor?: string;
};

export const BestSellerSection = ({
  title = "Best Sellers",
  products = bestSellerProductsData,
  shopMoreLink = "/best-sellers",
  sectionType = "bestseller",
  backgroundColor = "bg-[#F7F7F9]"
}: BestSellerSectionProps) => {
  return (
    <ProductGrid
      title={title}
      products={products}
      shopMoreLink={shopMoreLink}
      sectionType={sectionType}
      icon={<Trophy size={20} />}
      backgroundColor={backgroundColor}
    />
  );
};
