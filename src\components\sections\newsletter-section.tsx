import { Container } from "@/components/ui/container";
import { Mail } from "lucide-react";
import { Button } from "../ui/button";
import { RoundedInput } from "../ui/rounded-input";

type NewsletterSectionProps = {
  backgroundColor?: string;
};

export const NewsletterSection = ({ backgroundColor = "bg-white" }: NewsletterSectionProps) => {
  return (
    <section className={`py-12 ${backgroundColor} dark:bg-primary/10`}>
      <Container>
        <div className="flex flex-col md:flex-row items-center justify-between">
          <div className="mb-4 md:mb-0">
            <h3 className="text-xl font-bold dark:text-gray-100">Join Our Newsletter</h3>
            <p className="text-sm dark:text-gray-300">Subscribe to get information about products and coupons</p>
          </div>
          <form className="flex w-full md:w-auto">
            <div className="relative flex w-full md:w-auto items-center">
              <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none z-10">
                <Mail className="h-5 w-5 text-secondary" />
              </div>
              <RoundedInput
                type="email"
                placeholder="Enter your Email Address"
                className="w-full md:w-64 pl-10 pr-4 text-foreground bg-white dark:bg-card border border-r-0 dark:border-border rounded-r-none h-10"
                required
              />
              <Button
                type="submit"
                size="lg"
                className="rounded-l-none rounded-r-full hover:shadow-md bg-gradient-to-r from-primary to-secondary text-white px-6 uppercase text-sm font-medium transition-all duration-300 border-0 shadow-sm h-10"
              >
                SUBSCRIBE
              </Button>
            </div>
          </form>
        </div>
      </Container>
    </section>
  );
};

