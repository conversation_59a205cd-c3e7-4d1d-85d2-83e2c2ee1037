"use client";

import React from "react";
import { ArrowUpDown, Check, X } from "lucide-react";

type SortOption = {
  label: string;
  value: string;
};

const sortOptions: SortOption[] = [
  { label: "Popularity", value: "popularity" },
  { label: "Price: Low to High", value: "price_asc" },
  { label: "Price: High to Low", value: "price_desc" },
  { label: "Newest First", value: "newest" },
  { label: "Discount", value: "discount" }
];

// Define the active filter type
export type ActiveFilter = {
  type: string;
  value: string;
  label: string;
};

type ProductSortingProps = {
  totalProducts: number;
  activeSortOption: string;
  onSortChange: (value: string) => void;
  activeFilters?: ActiveFilter[];
  onRemoveFilter?: (filter: ActiveFilter) => void;
  onClearAllFilters?: () => void;
};

export function ProductSorting({
  totalProducts,
  activeSortOption,
  onSortChange,
  activeFilters = [],
  onRemoveFilter,
  onClearAllFilters
}: ProductSortingProps) {
  const [isOpen, setIsOpen] = React.useState(false);

  const handleSortChange = (value: string) => {
    onSortChange(value);
    setIsOpen(false);
  };

  const activeOption = sortOptions.find(option => option.value === activeSortOption) || sortOptions[0];

  return (
    <div className="flex flex-col mb-4 gap-2">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
        <div className="text-sm text-gray-600 dark:text-gray-400">
          Showing <span className="font-medium dark:text-gray-300">{totalProducts}</span> products
        </div>

        <div className="relative">
          <button
            className="flex items-center gap-2 px-3 py-1.5 border border-gray-200 dark:border-border rounded-md text-sm hover:border-primary/50 transition-colors dark:text-gray-300"
            onClick={() => setIsOpen(!isOpen)}
          >
            <ArrowUpDown size={14} />
            <span>Sort by: <span className="font-medium dark:text-gray-200">{activeOption.label}</span></span>
          </button>

          {isOpen && (
            <div className="absolute right-0 top-full mt-1 w-48 bg-white dark:bg-card border border-gray-200 dark:border-border rounded-md shadow-lg z-10">
              <ul className="py-1">
                {sortOptions.map((option) => (
                  <li key={option.value}>
                    <button
                      className="flex items-center justify-between w-full px-4 py-2 text-sm text-left hover:bg-gray-50 dark:hover:bg-gray-800 dark:text-gray-300"
                      onClick={() => handleSortChange(option.value)}
                    >
                      <span>{option.label}</span>
                      {option.value === activeSortOption && (
                        <Check size={14} className="text-primary" />
                      )}
                    </button>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>

      {/* Active Filters Display */}
      {activeFilters.length > 0 && (
        <div className="flex flex-wrap items-center gap-2 mt-1">
          <span className="text-sm text-gray-600 dark:text-gray-400 mr-1">Filtered By:</span>
          {activeFilters.map((filter, index) => (
            <div
              key={`${filter.type}-${filter.value}-${index}`}
              className="flex items-center gap-1 px-3 py-1 bg-white dark:bg-card border border-gray-200 dark:border-border rounded-md text-sm dark:text-gray-300"
            >
              <span>{filter.label}</span>
              {onRemoveFilter && (
                <button
                  onClick={() => onRemoveFilter(filter)}
                  className="text-gray-500 dark:text-gray-400 hover:text-primary ml-1"
                >
                  <X size={14} />
                </button>
              )}
            </div>
          ))}
          {onClearAllFilters && (
            <button
              onClick={onClearAllFilters}
              className="text-sm text-primary hover:underline ml-1"
            >
              Clear All
            </button>
          )}
        </div>
      )}
    </div>
  );
}
