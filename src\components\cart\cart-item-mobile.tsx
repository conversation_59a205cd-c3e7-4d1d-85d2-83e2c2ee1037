import React from "react";
import Image from "next/image";
import { Heart, Minus, Plus, Trash2 } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";

interface CartItemMobileProps {
  item: {
    id: number;
    name: string;
    price: number;
    originalPrice: number;
    quantity: number;
    image: string;
    vendor: string;
    color: string;
  };
  updateQuantity: (id: number, quantity: number) => void;
  removeItem: (id: number) => void;
}

export function CartItemMobile({ item, updateQuantity, removeItem }: CartItemMobileProps) {
  return (
    <Card className="bg-card rounded-lg border border-border mb-3 shadow-none p-0">
      <CardContent className="p-4">
        <div className="flex gap-3">
          {/* Product Image */}
          <div className="w-20 h-20 bg-muted rounded-lg overflow-hidden relative flex-shrink-0">
            <Image
              src={item.image}
              alt={item.name}
              fill
              className="object-cover"
            />
            {item.originalPrice > item.price && (
              <div className="absolute top-0 right-0 bg-accent text-white text-[10px] px-1.5 py-0.5 font-medium">
                SALE
              </div>
            )}
          </div>

          {/* Product Details */}
          <div className="flex-1 min-w-0">
            <h4 className="text-sm font-medium text-card-foreground truncate">{item.name}</h4>
            <p className="text-xs text-muted-foreground mt-0.5">
              {item.vendor} • {item.color}
            </p>

            {/* Price */}
            <div className="flex items-center mt-1">
              <span className="text-sm font-medium text-card-foreground">${item.price.toFixed(2)}</span>
              {item.originalPrice > item.price && (
                <span className="text-xs text-muted-foreground line-through ml-2">${item.originalPrice.toFixed(2)}</span>
              )}
            </div>
          </div>
        </div>

        {/* Quantity and Actions */}
        <div className="flex items-center justify-between mt-4 pt-3 border-t border-border">
          {/* Quantity Controls */}
          <div className="flex items-center border border-border rounded-md">
            <button
              onClick={() => updateQuantity(item.id, item.quantity - 1)}
              className="h-8 w-8 flex items-center justify-center text-muted-foreground hover:text-foreground hover:bg-muted transition-colors"
              aria-label="Decrease quantity"
            >
              <Minus className="h-3 w-3" />
            </button>
            <span className="h-8 min-w-[32px] flex items-center justify-center text-sm font-medium">
              {item.quantity}
            </span>
            <button
              onClick={() => updateQuantity(item.id, item.quantity + 1)}
              className="h-8 w-8 flex items-center justify-center text-muted-foreground hover:text-foreground hover:bg-muted transition-colors"
              aria-label="Increase quantity"
            >
              <Plus className="h-3 w-3" />
            </button>
          </div>

          {/* Total and Actions */}
          <div className="flex items-center gap-3">
            <span className="text-sm font-semibold text-card-foreground">
              ${(item.price * item.quantity).toFixed(2)}
            </span>
            <div className="flex items-center">
              <button
                onClick={() => removeItem(item.id)}
                className="text-muted-foreground hover:text-red-500 transition-colors p-1"
                aria-label="Remove item"
              >
                <Trash2 className="h-4 w-4" />
              </button>
              <button
                className="text-muted-foreground hover:text-pink-500 transition-colors p-1"
                aria-label="Save for later"
              >
                <Heart className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
