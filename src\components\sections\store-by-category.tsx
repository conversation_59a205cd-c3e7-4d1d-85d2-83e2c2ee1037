import { Container } from "@/components/ui/container";
import { Card, CardContent } from "@/components/ui/card";
import {
  ArrowRight,
  Store,
  Palette,
  Car,
  BookOpen,
  Cpu,
  Shirt,
  Smartphone,
  Gamepad,
  Gift,
  Apple,
  Stethoscope,
  Home,
  Laptop,
  TabletSmartphone,
  Music,
  Watch,
  Dumbbell,
  <PERSON>Tool,
  Wrench
} from "lucide-react";
import Link from "next/link";

// Define the category types
const storeCategories = [
  {
    name: "Art, Crafts and Handmades",
    slug: "art-crafts-handmades",
    image: "/images/categories/art-crafts.png",
    icon: Palette
  },
  {
    name: "Automotive",
    slug: "automotive",
    image: "/images/categories/automotive.png",
    icon: Car
  },
  {
    name: "Books and Readables",
    slug: "books-readables",
    image: "/images/categories/books.png",
    icon: BookOpen
  },
  {
    name: "Electronics",
    slug: "electronics",
    image: "/images/categories/electronics.png",
    icon: Cpu
  },
  {
    name: "Fashion",
    slug: "fashion",
    image: "/images/categories/fashion.png",
    icon: Shirt
  },
  {
    name: "Gadgets",
    slug: "gadgets",
    image: "/images/categories/gadgets.png",
    icon: Smartphone
  },
  {
    name: "Games And Softwares",
    slug: "games-softwares",
    image: "/images/categories/games.png",
    icon: Gamepad
  },
  {
    name: "Gifts and Presents",
    slug: "gifts-presents",
    image: "/images/categories/gifts.png",
    icon: Gift
  },
  {
    name: "Grocery and Vegetables",
    slug: "grocery-vegetables",
    image: "/images/categories/grocery.png",
    icon: Apple
  },
  {
    name: "Health And Beauty",
    slug: "health-beauty",
    image: "/images/categories/health.png",
    icon: Stethoscope
  },
  {
    name: "Home And Kitchen",
    slug: "home-kitchen",
    image: "/images/categories/kitchen.png",
    icon: Home
  },
  {
    name: "Laptop And Computer",
    slug: "laptop-computer",
    image: "/images/categories/laptop.png",
    icon: Laptop
  },
  {
    name: "Mobile Phones And Tablets",
    slug: "mobile-tablets",
    image: "/images/categories/mobile.png",
    icon: TabletSmartphone
  },
  {
    name: "Music and Others",
    slug: "music-others",
    image: "/images/categories/music.png",
    icon: Music
  },
  {
    name: "Smart Watches And Wearable",
    slug: "smart-watches-wearable",
    image: "/images/categories/smartwatch.png",
    icon: Watch
  },
  {
    name: "Sports And Outdoors",
    slug: "sports-outdoors",
    image: "/images/categories/sports.png",
    icon: Dumbbell
  },
  {
    name: "Stationary And Office Supplies",
    slug: "stationary-office",
    image: "/images/categories/stationary.png",
    icon: PenTool
  },
  {
    name: "Tools And Home Improvement",
    slug: "tools-home-improvement",
    image: "/images/categories/tools.png",
    icon: Wrench
  },
];

// Categories are defined above

type StoreByCategoryProps = {
  backgroundColor?: string;
};

export const StoreByCategory = ({ backgroundColor = "bg-white" }: StoreByCategoryProps) => {
  return (
    <div className={`${backgroundColor} dark:bg-card py-12`}>
      <Container>
        <div className="grid grid-cols-1 md:grid-cols-10 gap-6">
        {/* Popular Categories - Full width on tablet (md) and iPad Pro */}
        <Card className="md:col-span-10 xl:col-span-3 p-4 border border-gray-200 dark:border-border shadow-none">
          {/* Title and Shop more link with connecting line - Desktop */}
          <div className="hidden sm:block mb-6">
            <div className="flex items-center w-full relative">
              {/* Horizontal line that spans the entire width */}
              <div className="absolute h-px bg-gray-200 dark:bg-gray-700 w-full"></div>

              {/* Title with icon - positioned on top of the line */}
              <div className={`flex-shrink-0 flex items-center ${backgroundColor} dark:bg-card rounded-full p-1 z-10 mr-4`}>
                <div className="flex items-center gap-2 px-3">
                  <div className="w-6 h-6 flex items-center justify-center text-primary dark:text-accent">
                    <Palette size={20} />
                  </div>
                  <h2 className="text-lg font-bold text-primary dark:text-accent">Popular Categories</h2>
                </div>
              </div>

              {/* Empty flex-grow div to push the Shop More button to the right */}
              <div className="flex-grow"></div>

              {/* Shop More button - positioned on top of the line - Hide on sm, show on md and up */}
              <div className="flex-shrink-0 z-10 hidden md:block">
                <Link
                  href="/categories"
                  className={`flex ${backgroundColor} dark:bg-card hover:bg-gradient-to-r hover:from-primary/90 hover:to-secondary/90 hover:text-white text-primary px-3 sm:px-4 py-2 rounded-full items-center gap-1 whitespace-nowrap transition-all text-xs sm:text-sm border border-gray-200 dark:border-border dark:text-accent`}
                >
                  View More <ArrowRight className="h-3 w-3 sm:h-4 sm:w-4" />
                </Link>
              </div>
            </div>
          </div>

          {/* Mobile: Title and Shop more link with connecting line */}
          <div className="sm:hidden mb-4">
            <div className="flex items-center w-full relative overflow-hidden">
              {/* Horizontal line that spans the entire width */}
              <div className="absolute h-px bg-gray-200 dark:bg-gray-700 w-full"></div>

              {/* Title with icon - positioned on top of the line */}
              <div className={`flex-shrink-0 flex items-center ${backgroundColor} dark:bg-card rounded-full p-1 z-10 mr-4`}>
                <div className="flex items-center gap-2 px-2">
                  <div className="w-5 h-5 flex items-center justify-center text-primary dark:text-accent">
                    <Palette size={16} />
                  </div>
                  <h2 className="text-base font-bold text-primary dark:text-accent">Popular Categories</h2>
                </div>
              </div>

              {/* Empty flex-grow div to push the Shop More button to the right */}
              <div className="flex-grow"></div>

              {/* Shop More button - positioned on top of the line */}
              <div className="flex-shrink-0 z-10">
                <Link
                  href="/categories"
                  className={`flex ${backgroundColor} dark:bg-card hover:bg-gradient-to-r hover:from-primary/90 hover:to-secondary/90 hover:text-white text-primary px-3 py-2 rounded-full items-center gap-1 whitespace-nowrap transition-all text-xs border border-gray-200 dark:border-border dark:text-accent`}
                >
                  Shop More <ArrowRight className="h-3 w-3" />
                </Link>
              </div>
            </div>
          </div>

          {/* Grid layout - 3 columns on xs/sm, single row of 6 columns on md/lg (iPad Pro), 3 columns on xl */}
          <CardContent className="grid grid-cols-3 md:grid-cols-6 xl:grid-cols-3 gap-3 p-0">
            {storeCategories.slice(0, 9).map((category) => (
              <Link href={`/category/${category.slug}`} key={category.slug} className="block">
                <Card className="border border-gray-200 dark:border-border rounded-lg p-2 hover:border-gray-300 dark:hover:border-gray-600 transition-all text-center shadow-none">
                  <div className="flex justify-center mb-1">
                    <div className="w-12 h-12 flex items-center justify-center">
                      <div className="w-10 h-10 bg-gradient-to-r from-primary/20 to-primary/10 dark:from-primary/30 dark:to-primary/20 rounded-full flex items-center justify-center">
                        <category.icon className="h-5 w-5 text-primary dark:text-accent" strokeWidth={2} />
                      </div>
                    </div>
                  </div>
                  <h3 className="text-[10px] font-medium leading-tight dark:text-gray-200">{category.name}</h3>
                </Card>
              </Link>
            ))}
          </CardContent>
        </Card>

        {/* Stores By Category - Right Column (slightly narrower) */}
        <Card className="md:col-span-10 xl:col-span-7 p-4 border border-gray-200 dark:border-border shadow-none">
          {/* Title and Shop more link with connecting line - Desktop */}
          <div className="hidden sm:block mb-6">
            <div className="flex items-center w-full relative">
              {/* Horizontal line that spans the entire width */}
              <div className="absolute h-px bg-gray-200 dark:bg-gray-700 w-full"></div>

              {/* Title with icon - positioned on top of the line */}
              <div className={`flex-shrink-0 flex items-center ${backgroundColor} dark:bg-card rounded-full p-1 z-10 mr-4`}>
                <div className="flex items-center gap-2 px-3">
                  <div className="w-6 h-6 flex items-center justify-center text-primary dark:text-accent">
                    <Store size={20} />
                  </div>
                  <h2 className="text-lg font-bold text-primary dark:text-accent">Stores By Category</h2>
                </div>
              </div>

              {/* Empty flex-grow div to push the Shop More button to the right */}
              <div className="flex-grow"></div>

              {/* Shop More button - positioned on top of the line */}
              <div className="flex-shrink-0 z-10">
                <Link
                  href="/categories"
                  className={`flex ${backgroundColor} dark:bg-card hover:bg-gradient-to-r hover:from-primary/90 hover:to-secondary/90 hover:text-white text-primary px-3 sm:px-4 py-2 rounded-full items-center gap-1 whitespace-nowrap transition-all text-xs sm:text-sm border border-gray-200 dark:border-border dark:text-accent`}
                >
                  Shop More <ArrowRight className="h-3 w-3 sm:h-4 sm:w-4" />
                </Link>
              </div>
            </div>
          </div>

          {/* Mobile: Title and Shop more link with connecting line */}
          <div className="sm:hidden mb-4">
            <div className="flex items-center w-full relative overflow-hidden">
              {/* Horizontal line that spans the entire width */}
              <div className="absolute h-px bg-gray-200 dark:bg-gray-700 w-full"></div>

              {/* Title with icon - positioned on top of the line */}
              <div className={`flex-shrink-0 flex items-center ${backgroundColor} dark:bg-card rounded-full p-1 z-10 mr-4`}>
                <div className="flex items-center gap-2 px-2">
                  <div className="w-5 h-5 flex items-center justify-center text-primary">
                    <Store size={16} />
                  </div>
                  <h2 className="text-base font-bold text-primary dark:text-accent">Stores By Category</h2>
                </div>
              </div>

              {/* Empty flex-grow div to push the Shop More button to the right */}
              <div className="flex-grow"></div>

              {/* Shop More button - positioned on top of the line */}
              <div className="flex-shrink-0 z-10">
                <Link
                  href="/categories"
                  className={`flex ${backgroundColor} dark:bg-card hover:bg-gradient-to-r hover:from-primary/90 hover:to-secondary/90 hover:text-white text-primary px-3 py-2 rounded-full items-center gap-1 whitespace-nowrap transition-all text-xs border border-gray-200 dark:border-border dark:text-accent`}
                >
                  Shop More <ArrowRight className="h-3 w-3" />
                </Link>
              </div>
            </div>
          </div>

          <CardContent className="grid grid-cols-3 sm:grid-cols-6 gap-3 p-0">
            {storeCategories.map((category) => (
              <Link
                href={`/category/${category.slug}`}
                key={category.slug}
                className="block"
              >
                <Card className="border border-gray-200 dark:border-border rounded-lg p-2 hover:border-gray-300 dark:hover:border-gray-600 transition-all text-center shadow-none">
                  <div className="flex justify-center mb-1">
                    <div className="w-12 h-12 flex items-center justify-center">
                      <div className="w-10 h-10 bg-gradient-to-r from-primary/20 to-primary/10 dark:from-primary/30 dark:to-primary/20 rounded-full flex items-center justify-center">
                        <category.icon className="h-5 w-5 text-primary dark:text-accent" strokeWidth={2} />
                      </div>
                    </div>
                  </div>
                  <h3 className="text-[10px] font-medium leading-tight dark:text-gray-200">{category.name}</h3>
                </Card>
              </Link>
            ))}
          </CardContent>
        </Card>
      </div>
    </Container>
    </div>
  );
};
