"use client";

import React, { useState } from "react";
import Image from "next/image";
import {
  <PERSON>,
  CardContent,
  CardHeader,
} from "@/components/ui/card";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import { RoundedInput } from "@/components/ui/rounded-input";

export default function CheckoutPage() {
  // State for current step
  const [currentStep, setCurrentStep] = useState(0);

  // State for form data
  const [formData, setFormData] = useState({
    // Shipping address
    firstName: "",
    lastName: "",
    name: "",
    address1: "",
    address2: "",
    address: "",
    city: "",
    state: "",
    zipCode: "",
    zip: "",
    email: "",
    phone: "",
    mobile: "",

    // Shipping method
    shippingMethod: "standard", // 'standard', 'express', 'overnight'

    // Payment
    paymentMethod: "online", // 'online', 'cod', 'pos'
    cardNumber: "",
    cardName: "",
    cardExpiry: "",
    cardCvv: "",

    // Schedule delivery
    scheduleDelivery: false,
    deliveryDates: "",
    note: "",
  });

  const [cartItems] = useState([
    {
      id: 1,
      name: "Wireless Bluetooth Headphones",
      price: 79.99,
      quantity: 1,
      image: "/images/products/headphones.jpg",
    },
    {
      id: 2,
      name: "Smart Watch Series 5",
      price: 199.99,
      quantity: 1,
      image: "/images/products/smartwatch.jpg",
    },
    {
      id: 3,
      name: "Laptop Sleeve Case",
      price: 29.99,
      quantity: 1,
      image: "/images/products/laptop-sleeve.jpg",
    },
  ]);

  // Calculate totals
  const subtotal = cartItems.reduce((sum, item) => sum + item.price * item.quantity, 0);
  const shipping = 0;
  const total = subtotal + shipping;

  // Handlers
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    if (type === "checkbox") {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData((prev) => ({
        ...prev,
        [name]: checked,
      }));
      return;
    }
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handlePaymentMethodChange = (method: string) => {
    setFormData((prev) => ({ ...prev, paymentMethod: method }));
  };

  const handleShippingMethodChange = (method: string) => {
    setFormData((prev) => ({ ...prev, shippingMethod: method }));
  };

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Progress bar steps
  const steps = [
    { key: "shipping", label: "Shipping address" },
    { key: "method", label: "Shipping method" },
    { key: "payment", label: "Payment" },
    { key: "review", label: "Review & place order" },
  ];

  return (
    <div className="bg-background min-h-screen py-6 sm:py-10 px-3 sm:px-4">
      <div className="max-w-6xl mx-auto mb-6 sm:mb-8">
        <h1 className="text-2xl sm:text-3xl font-bold text-primary mb-2">Checkout</h1>
        <p className="text-sm sm:text-base text-muted-foreground">Complete your purchase by filling out the information below</p>
      </div>

      <div className="max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-12 gap-6">
        {/* Left: Form Sections */}
        <div className="md:col-span-7 lg:col-span-8 space-y-6">
          <div className="flex flex-row">

            {/* Form Sections */}
            <div className="w-full space-y-6">
              {/* Form Content */}
              <div className="space-y-6">
                <form onSubmit={(e) => {
            e.preventDefault();
            alert("Order confirmed!");
          }} className="space-y-6">

          {/* Accordion for all steps */}
          <Accordion
            type="single"
            collapsible
            defaultValue={`step-${currentStep}`}
            className="w-full"
            value={`step-${currentStep}`}
            onValueChange={(value) => {
              if (value) {
                const stepIndex = parseInt(value.split('-')[1]);
                setCurrentStep(stepIndex);
              }
            }}
          >
            {/* Step 0: Shipping Address */}
            <AccordionItem value="step-0" className="border-b-0">
              <AccordionTrigger className="py-3 sm:py-4 px-4 sm:px-6 bg-card rounded-t-lg border border-b-0 border-border hover:no-underline">
                <div className="flex items-center">
                  <Button
                    variant={currentStep >= 0 ? "default" : "outline"}
                    size="icon"
                    className={`
                      w-5 h-5 sm:w-6 sm:h-6 rounded-full flex items-center justify-center mr-2 sm:mr-3 p-0 min-w-0
                      ${currentStep > 0
                        ? 'bg-gradient-to-r from-primary to-secondary text-primary-foreground border-0'
                        : currentStep === 0
                          ? 'bg-gradient-to-r from-primary to-secondary text-primary-foreground border-0'
                          : 'bg-card border-2 border-border text-muted-foreground'
                      }
                    `}
                    onClick={(e) => {
                      e.stopPropagation();
                      setCurrentStep(0);
                    }}
                  >
                    {currentStep > 0 ? (
                      <svg className="w-2.5 h-2.5 sm:w-3 sm:h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
                      </svg>
                    ) : (
                      <span className="text-xs font-bold">1</span>
                    )}
                  </Button>
                  <span className="font-semibold text-sm sm:text-base text-card-foreground">Shipping address</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="bg-card rounded-b-lg border border-t-0 border-border px-4 sm:px-6 py-4">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-xs sm:text-sm font-medium mb-1 text-card-foreground">First Name *</label>
                    <RoundedInput name="firstName" value={formData.firstName} onChange={handleInputChange} required />
                  </div>
                  <div>
                    <label className="block text-xs sm:text-sm font-medium mb-1 text-card-foreground">Last Name *</label>
                    <RoundedInput name="lastName" value={formData.lastName} onChange={handleInputChange} required />
                  </div>
                  <div className="sm:col-span-2">
                    <label className="block text-xs sm:text-sm font-medium mb-1 text-card-foreground">Address 1 - Street or P.O. Box *</label>
                    <RoundedInput name="address1" value={formData.address1} onChange={handleInputChange} required />
                  </div>
                  <div className="sm:col-span-2">
                    <label className="block text-xs sm:text-sm font-medium mb-1 text-card-foreground">Address 2 - Apt, Suite, Floor</label>
                    <RoundedInput name="address2" value={formData.address2} onChange={handleInputChange} placeholder="Leave blank if N/A, Box or Address 1" />
                  </div>
                  <div>
                    <label className="block text-xs sm:text-sm font-medium mb-1 text-card-foreground">ZIP Code *</label>
                    <RoundedInput name="zipCode" value={formData.zipCode} onChange={handleInputChange} required />
                  </div>
                  <div>
                    <label className="block text-xs sm:text-sm font-medium mb-1 text-card-foreground">City *</label>
                    <RoundedInput name="city" value={formData.city} onChange={handleInputChange} required />
                  </div>
                  <div>
                    <label className="block text-xs sm:text-sm font-medium mb-1 text-card-foreground">State *</label>
                    <select
                      name="state"
                      value={formData.state}
                      onChange={handleInputChange}
                      className="w-full px-4 py-2 text-sm rounded-full border border-input bg-transparent text-foreground focus:border-ring focus:ring-ring/50 focus:ring-[3px] hover:border-primary/50 transition-all duration-300 outline-none h-10"
                      required
                    >
                      <option value="">Select...</option>
                      <option value="AL">Alabama</option>
                      <option value="AK">Alaska</option>
                      <option value="AZ">Arizona</option>
                      {/* Add more states as needed */}
                    </select>
                  </div>
                  <div>
                    <label className="block text-xs sm:text-sm font-medium mb-1 text-card-foreground">Email *</label>
                    <RoundedInput name="email" value={formData.email} onChange={handleInputChange} required />
                  </div>
                </div>
                <div className="mt-6">
                  <Button
                    type="button"
                    onClick={nextStep}
                    className="w-full py-3 sm:py-4 bg-gradient-to-r from-primary to-secondary text-primary-foreground rounded-full font-semibold text-base sm:text-lg hover:opacity-90 hover:shadow-md transition-all"
                    size="lg"
                  >
                    Continue to shipping method
                  </Button>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Step 1: Shipping Method */}
            <AccordionItem value="step-1" className="border-b-0 mt-4">
              <AccordionTrigger className="py-3 sm:py-4 px-4 sm:px-6 bg-card rounded-t-lg border border-b-0 border-border hover:no-underline">
                <div className="flex items-center">
                  <Button
                    variant={currentStep >= 1 ? "default" : "outline"}
                    size="icon"
                    className={`
                      w-5 h-5 sm:w-6 sm:h-6 rounded-full flex items-center justify-center mr-2 sm:mr-3 p-0 min-w-0
                      ${currentStep > 1
                        ? 'bg-gradient-to-r from-primary to-secondary text-primary-foreground border-0'
                        : currentStep === 1
                          ? 'bg-gradient-to-r from-primary to-secondary text-primary-foreground border-0'
                          : 'bg-card border-2 border-border text-muted-foreground'
                      }
                    `}
                    onClick={(e) => {
                      e.stopPropagation();
                      setCurrentStep(1);
                    }}
                  >
                    {currentStep > 1 ? (
                      <svg className="w-2.5 h-2.5 sm:w-3 sm:h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
                      </svg>
                    ) : (
                      <span className="text-xs font-bold">2</span>
                    )}
                  </Button>
                  <span className="font-semibold text-sm sm:text-base text-card-foreground">Shipping method</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="bg-card rounded-b-lg border border-t-0 border-border px-4 sm:px-6 py-4">
                <div className="grid grid-cols-1 gap-4">
                  <label
                    className={`flex items-center p-3 sm:p-4 rounded-lg border cursor-pointer transition hover:shadow-sm ${formData.shippingMethod === "standard" ? "border-primary bg-primary/5 dark:bg-primary/10" : "border-border"}`}
                    onClick={() => handleShippingMethodChange("standard")}
                  >
                    <input
                      type="radio"
                      name="shippingMethod"
                      value="standard"
                      checked={formData.shippingMethod === "standard"}
                      onChange={() => handleShippingMethodChange("standard")}
                      className="mr-2 sm:mr-3 h-4 w-4 text-primary accent-primary"
                    />
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <span className="font-medium text-sm sm:text-base text-card-foreground">Standard Shipping</span>
                        <span className="font-semibold text-sm sm:text-base text-primary">Free</span>
                      </div>
                      <p className="text-xs sm:text-sm text-muted-foreground mt-1">Delivery in 5-7 business days</p>
                    </div>
                  </label>

                  <label
                    className={`flex items-center p-3 sm:p-4 rounded-lg border cursor-pointer transition hover:shadow-sm ${formData.shippingMethod === "express" ? "border-primary bg-primary/5 dark:bg-primary/10" : "border-border"}`}
                    onClick={() => handleShippingMethodChange("express")}
                  >
                    <input
                      type="radio"
                      name="shippingMethod"
                      value="express"
                      checked={formData.shippingMethod === "express"}
                      onChange={() => handleShippingMethodChange("express")}
                      className="mr-2 sm:mr-3 h-4 w-4 text-primary accent-primary"
                    />
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <span className="font-medium text-sm sm:text-base text-card-foreground">Express Shipping</span>
                        <span className="font-semibold text-sm sm:text-base text-primary">$9.99</span>
                      </div>
                      <p className="text-xs sm:text-sm text-muted-foreground mt-1">Delivery in 2-3 business days</p>
                    </div>
                  </label>

                  <label
                    className={`flex items-center p-3 sm:p-4 rounded-lg border cursor-pointer transition hover:shadow-sm ${formData.shippingMethod === "overnight" ? "border-primary bg-primary/5 dark:bg-primary/10" : "border-border"}`}
                    onClick={() => handleShippingMethodChange("overnight")}
                  >
                    <input
                      type="radio"
                      name="shippingMethod"
                      value="overnight"
                      checked={formData.shippingMethod === "overnight"}
                      onChange={() => handleShippingMethodChange("overnight")}
                      className="mr-2 sm:mr-3 h-4 w-4 text-primary accent-primary"
                    />
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <span className="font-medium text-sm sm:text-base text-card-foreground">Overnight Shipping</span>
                        <span className="font-semibold text-sm sm:text-base text-primary">$19.99</span>
                      </div>
                      <p className="text-xs sm:text-sm text-muted-foreground mt-1">Delivery on the next business day</p>
                    </div>
                  </label>
                </div>
                <div className="mt-6 flex gap-4">
                  <Button
                    type="button"
                    onClick={prevStep}
                    variant="outline"
                    className="w-1/3 py-3 sm:py-4 text-foreground rounded-full font-semibold hover:bg-muted hover:border-border transition-all"
                    size="lg"
                  >
                    Back
                  </Button>
                  <Button
                    type="button"
                    onClick={nextStep}
                    className="w-2/3 py-3 sm:py-4 bg-gradient-to-r from-primary to-secondary text-primary-foreground rounded-full font-semibold text-base sm:text-lg hover:opacity-90 hover:shadow-md transition-all"
                    size="lg"
                  >
                    Continue to payment
                  </Button>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Step 2: Payment Method */}
            <AccordionItem value="step-2" className="border-b-0 mt-4">
              <AccordionTrigger className="py-3 sm:py-4 px-4 sm:px-6 bg-card rounded-t-lg border border-b-0 border-border hover:no-underline">
                <div className="flex items-center">
                  <Button
                    variant={currentStep >= 2 ? "default" : "outline"}
                    size="icon"
                    className={`
                      w-5 h-5 sm:w-6 sm:h-6 rounded-full flex items-center justify-center mr-2 sm:mr-3 p-0 min-w-0
                      ${currentStep > 2
                        ? 'bg-gradient-to-r from-primary to-secondary text-primary-foreground border-0'
                        : currentStep === 2
                          ? 'bg-gradient-to-r from-primary to-secondary text-primary-foreground border-0'
                          : 'bg-card border-2 border-border text-muted-foreground'
                      }
                    `}
                    onClick={(e) => {
                      e.stopPropagation();
                      setCurrentStep(2);
                    }}
                  >
                    {currentStep > 2 ? (
                      <svg className="w-2.5 h-2.5 sm:w-3 sm:h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
                      </svg>
                    ) : (
                      <span className="text-xs font-bold">3</span>
                    )}
                  </Button>
                  <span className="font-semibold text-sm sm:text-base text-card-foreground">Payment</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="bg-card rounded-b-lg border border-t-0 border-border px-4 sm:px-6 py-4">
                <div className="grid grid-cols-1 gap-4">
                  <label
                    className={`flex items-center p-3 sm:p-4 rounded-lg border cursor-pointer transition hover:shadow-sm ${formData.paymentMethod === "online" ? "border-primary bg-primary/5 dark:bg-primary/10" : "border-border"}`}
                    onClick={() => handlePaymentMethodChange("online")}
                  >
                    <input
                      type="radio"
                      name="paymentMethod"
                      value="online"
                      checked={formData.paymentMethod === "online"}
                      onChange={() => handlePaymentMethodChange("online")}
                      className="mr-2 sm:mr-3 h-4 w-4 text-primary accent-primary"
                    />
                    <div className="flex-1">
                      <span className="font-medium text-sm sm:text-base text-card-foreground">Credit Card / Debit Card</span>
                    </div>
                  </label>

                  <label
                    className={`flex items-center p-3 sm:p-4 rounded-lg border cursor-pointer transition hover:shadow-sm ${formData.paymentMethod === "cod" ? "border-primary bg-primary/5 dark:bg-primary/10" : "border-border"}`}
                    onClick={() => handlePaymentMethodChange("cod")}
                  >
                    <input
                      type="radio"
                      name="paymentMethod"
                      value="cod"
                      checked={formData.paymentMethod === "cod"}
                      onChange={() => handlePaymentMethodChange("cod")}
                      className="mr-2 sm:mr-3 h-4 w-4 text-primary accent-primary"
                    />
                    <div className="flex-1">
                      <span className="font-medium text-sm sm:text-base text-card-foreground">Cash on Delivery</span>
                    </div>
                  </label>

                  <label
                    className={`flex items-center p-3 sm:p-4 rounded-lg border cursor-pointer transition hover:shadow-sm ${formData.paymentMethod === "pos" ? "border-primary bg-primary/5 dark:bg-primary/10" : "border-border"}`}
                    onClick={() => handlePaymentMethodChange("pos")}
                  >
                    <input
                      type="radio"
                      name="paymentMethod"
                      value="pos"
                      checked={formData.paymentMethod === "pos"}
                      onChange={() => handlePaymentMethodChange("pos")}
                      className="mr-2 sm:mr-3 h-4 w-4 text-primary accent-primary"
                    />
                    <div className="flex-1">
                      <span className="font-medium text-sm sm:text-base text-card-foreground">POS on Delivery</span>
                    </div>
                  </label>
                </div>
                <div className="mt-6 flex gap-4">
                  <Button
                    type="button"
                    onClick={prevStep}
                    variant="outline"
                    className="w-1/3 py-3 sm:py-4 text-foreground rounded-full font-semibold hover:bg-muted hover:border-border transition-all"
                    size="lg"
                  >
                    Back
                  </Button>
                  <Button
                    type="button"
                    onClick={nextStep}
                    className="w-2/3 py-3 sm:py-4 bg-gradient-to-r from-primary to-secondary text-primary-foreground rounded-full font-semibold text-base sm:text-lg hover:opacity-90 hover:shadow-md transition-all"
                    size="lg"
                  >
                    Continue to review
                  </Button>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Step 3: Review & Place Order */}
            <AccordionItem value="step-3" className="border-b-0 mt-4">
              <AccordionTrigger className="py-3 sm:py-4 px-4 sm:px-6 bg-card rounded-t-lg border border-b-0 border-border hover:no-underline">
                <div className="flex items-center">
                  <Button
                    variant={currentStep >= 3 ? "default" : "outline"}
                    size="icon"
                    className={`
                      w-5 h-5 sm:w-6 sm:h-6 rounded-full flex items-center justify-center mr-2 sm:mr-3 p-0 min-w-0
                      ${currentStep === 3
                        ? 'bg-gradient-to-r from-primary to-secondary text-primary-foreground border-0'
                        : 'bg-card border-2 border-border text-muted-foreground'
                      }
                    `}
                    onClick={(e) => {
                      e.stopPropagation();
                      setCurrentStep(3);
                    }}
                  >
                    <span className="text-xs font-bold">4</span>
                  </Button>
                  <span className="font-semibold text-sm sm:text-base text-card-foreground">Review & place order</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="bg-card rounded-b-lg border border-t-0 border-border px-4 sm:px-6 py-4">
                <div className="space-y-4">
                  <p className="text-sm sm:text-base text-card-foreground">Please review your order details before placing your order.</p>
                  <div className="mt-6 flex gap-4">
                    <Button
                      type="button"
                      onClick={prevStep}
                      variant="outline"
                      className="w-1/3 py-3 sm:py-4 text-foreground rounded-full font-semibold hover:bg-muted hover:border-border transition-all"
                      size="lg"
                    >
                      Back
                    </Button>
                    <Button
                      type="submit"
                      className="w-2/3 py-3 sm:py-4 bg-gradient-to-r from-primary to-secondary text-primary-foreground rounded-full font-semibold text-base sm:text-lg hover:opacity-90 hover:shadow-md transition-all"
                      size="lg"
                    >
                      Place order
                    </Button>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
                </form>
              </div>
            </div>
          </div>
        </div>

        {/* Right: Order Summary Card */}
        <Card className="md:col-span-5 lg:col-span-4 md:sticky md:top-8 h-fit shadow-sm rounded-lg border border-border bg-card">
          <CardHeader className="px-4 py-4 sm:px-6">
            <h2 className="text-lg sm:text-xl font-bold text-card-foreground">Order Summary</h2>
          </CardHeader>
          <CardContent className="px-4 sm:px-6">
            <div className="space-y-4 mb-6">
              {cartItems.map((item) => (
                <div key={item.id} className="flex items-center gap-3 sm:gap-4 border-b border-border pb-4 last:border-b-0 last:pb-0">
                  <div className="w-14 h-14 sm:w-16 sm:h-16 bg-muted rounded-lg flex items-center justify-center overflow-hidden">
                    <Image src={item.image} alt={item.name} width={64} height={64} className="object-cover w-full h-full" />
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-sm sm:text-base text-card-foreground">{item.name}</div>
                    <div className="text-xs sm:text-sm text-muted-foreground">${item.price.toFixed(2)}</div>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="w-8 text-center font-semibold text-card-foreground">{item.quantity.toString().padStart(2, "0")}</span>
                  </div>
                </div>
              ))}
            </div>
            <div className="space-y-3 border-t border-border pt-4">
              <div className="flex justify-between text-sm sm:text-base text-muted-foreground">
                <span>Subtotal</span>
                <span>${subtotal.toFixed(2)}</span>
              </div>
              <div className="flex justify-between text-sm sm:text-base text-muted-foreground">
                <span>Shipping</span>
                <span>--</span>
              </div>
              <div className="flex justify-between font-bold text-base sm:text-lg border-t border-border pt-3 mt-3">
                <span>Total</span>
                <span>${total.toFixed(2)}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
