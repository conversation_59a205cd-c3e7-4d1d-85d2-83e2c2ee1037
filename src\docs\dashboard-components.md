# Dashboard Components

This document provides an overview of the dashboard components used in the multi-tenant ecommerce application.

## Overview

The application has separate dashboard layouts for different user types:
- **SuperAdmin**: Main tenant dashboard
- **Vendor**: Store owner dashboard

Each dashboard uses a shared component structure but maintains its own navigation items and styling.

## Component Structure

### Base Components

1. **DashboardLayout** (`src/components/dashboard/layout.tsx`)
   - Base layout component used by both SuperAdmin and Vendor layouts
   - Handles the sidebar, header, and main content areas
   - Uses the SidebarProvider from shadcn UI
   - Implements the sidebar-07 component pattern from shadcn UI
   - Supports collapsible sidebar with icon-only mode
   - Provides responsive layout for all screen sizes

2. **DashboardSidebar** (`src/components/dashboard/sidebar.tsx`)
   - Shared sidebar component with customizable navigation
   - Takes navigation data, logo, and user information as props
   - Renders navigation items with proper collapsible sections
   - Supports nested navigation with expandable sub-items
   - Includes user profile section in the sidebar footer
   - Implements proper dark mode support

### User-Specific Components

1. **SuperAdmin**
   - Layout: `src/components/layout/superadmin/layout.tsx`
   - Sidebar: `src/components/layout/superadmin/sidebar.tsx`
   - Header: `src/components/layout/superadmin/header.tsx`
   - Navigation data: `src/data/superadmin-nav.ts`

2. **Vendor**
   - Layout: `src/components/layout/vendor/layout.tsx`
   - Sidebar: `src/components/layout/vendor/sidebar.tsx`
   - Header: `src/components/layout/vendor/header.tsx`
   - Navigation data: `src/data/vendor-nav.ts`

## Usage

### Using the DashboardLayout

The `DashboardLayout` component is the base layout for all dashboards. It takes three props:
- `sidebar`: The sidebar component to display
- `header`: The header component to display
- `children`: The main content of the dashboard
- `defaultSidebarOpen`: Whether the sidebar should be open by default (optional, defaults to true)

```tsx
import { DashboardLayout } from "@/components/dashboard/layout";
import { MySidebar } from "./sidebar";
import { MyHeader } from "./header";

export function MyLayout({ children }) {
  return (
    <DashboardLayout
      sidebar={<MySidebar />}
      header={<MyHeader />}
      defaultSidebarOpen={true}
    >
      {children}
    </DashboardLayout>
  );
}
```

### Using the DashboardSidebar

The `DashboardSidebar` component is the base sidebar for all dashboards. It takes the following props:
- `navData`: The navigation data to display in the sidebar
- `logoText`: The text to display as the logo text
- `logoInitials`: The initials to display in the logo
- `userInfo`: The user information to display in the sidebar footer

```tsx
import { DashboardSidebar } from "@/components/dashboard/sidebar";
import { myNavData } from "@/data/my-nav";
import { DashboardUserInfo } from "@/types/navigation";

export function MySidebar() {
  const userInfo: DashboardUserInfo = {
    displayName: "User Name",
    email: "<EMAIL>",
    avatarSrc: "/avatars/user.jpg",
    avatarFallback: "UN",
    logoutUrl: "/logout"
  };

  return (
    <DashboardSidebar
      navData={myNavData}
      logoText="My App"
      logoInitials="MA"
      userInfo={userInfo}
    />
  );
}
```

### Creating Navigation Data

Navigation data is defined in separate files for each user type. The navigation data follows a consistent structure:

```tsx
import { LayoutDashboard, Settings } from "lucide-react";
import { DashboardNavData } from "@/types/navigation";

export const myNavData: DashboardNavData = {
  groupLabel: "My Navigation",
  mainNavItems: [
    {
      title: "Dashboard",
      url: "/dashboard",
      icon: LayoutDashboard,
      isActive: true,
    },
    {
      title: "Settings",
      url: "/settings",
      icon: Settings,
      items: [
        {
          title: "General",
          url: "/settings/general",
        },
        {
          title: "Security",
          url: "/settings/security",
        },
      ],
    },
  ],
};
```

## Styling

The dashboard components use the application's color scheme:
- Primary: #052E7F
- Secondary: #2A928F
- Accent: #D9F7F5
- Background: #FFFFFF

Dark mode is supported through the theme provider and CSS variables.

## Best Practices

1. **Keep Navigation Data Separate**
   - Define navigation data in separate files for each user type
   - Follow the existing pattern for items with or without sub-items

2. **Use TypeScript Types**
   - Use the provided TypeScript types for navigation data and user information
   - Define proper types for all components and functions

3. **Follow Component Structure**
   - Use the base components for all dashboards
   - Extend the base components with user-specific components

4. **Maintain Consistent Styling**
   - Use the application's color scheme
   - Ensure proper dark mode support

5. **Ensure Accessibility**
   - Use proper ARIA attributes
   - Ensure keyboard navigation works correctly
   - Provide proper labels for all interactive elements
