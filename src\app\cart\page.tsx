"use client";

import React, { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { Container } from "@/components/ui/container";
import {
  Plus, Minus, ChevronRight,
  Trash2, ArrowRight, ShoppingBag, Heart, Check
} from "lucide-react";
import { CustomBreadcrumb } from "@/components/ui/custom-breadcrumb";
import { CartItemMobile } from "@/components/cart/cart-item-mobile";
import { ProductGrid } from "@/components/sections/product-grid";
import {
  Card,
  CardContent,
  CardHeader,
  CardFooter
} from "@/components/ui/card";

// Mock cart data - reusing the same data from slide-in cart
const cartItems = [
  {
    id: 1,
    name: "Wireless Bluetooth Headphones",
    price: 79.99,
    originalPrice: 99.99,
    quantity: 1,
    image: "/images/products/headphones.jpg",
    vendor: "AudioTech",
    color: "Black",
  },
  {
    id: 2,
    name: "Smart Watch Series 5",
    price: 199.99,
    originalPrice: 199.99,
    quantity: 1,
    image: "/images/products/smartwatch.jpg",
    vendor: "TechGear",
    color: "Silver",
  },
  {
    id: 3,
    name: "Laptop Sleeve Case",
    price: 29.99,
    originalPrice: 39.99,
    quantity: 1,
    image: "/images/products/laptop-sleeve.jpg",
    vendor: "BagWorld",
    color: "Navy Blue",
  },
];

export default function CartPage() {
  // State for cart items
  const [items, setItems] = useState(cartItems);

  // Calculate cart totals
  const subtotal = items.reduce((total, item) => total + item.price * item.quantity, 0);
  const shipping = subtotal > 100 ? 0 : 4.99;
  const tax = subtotal * 0.08;
  const total = subtotal + shipping + tax;
  const savings = items.reduce((total, item) => total + (item.originalPrice - item.price) * item.quantity, 0);
  const freeShippingThreshold = 100;
  const amountToFreeShipping = freeShippingThreshold - subtotal;

  // Handle quantity changes
  const updateQuantity = (id: number, newQuantity: number) => {
    if (newQuantity < 1) return;

    setItems(items.map(item =>
      item.id === id ? { ...item, quantity: newQuantity } : item
    ));
  };

  // Handle item removal
  const removeItem = (id: number) => {
    setItems(items.filter(item => item.id !== id));
  };

  return (
    <div className="bg-background min-h-screen pb-12 lg:pb-12">
      {/* Breadcrumb */}
      <CustomBreadcrumb
        items={[
          { label: "Home", href: "/" },
          { label: "Shopping Cart" }
        ]}
        backgroundColor="bg-[#E8F7F6]"
      />

      <Container className="mt-8">
        {items.length > 0 ? (
          <>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Cart Items Section */}
              <div className="lg:col-span-2">
                {/* Free Shipping Progress */}
                {amountToFreeShipping > 0 ? (
                  <div className="mb-6 bg-blue-50/10 dark:bg-blue-900/10 rounded-xl p-4 border border-blue-100/30 dark:border-blue-800/30">
                    <div className="flex flex-col xs:flex-row xs:items-center xs:justify-between mb-1.5 gap-1">
                      <span className="text-xs xs:text-sm text-blue-700 dark:text-blue-400 font-medium">
                        Add ${amountToFreeShipping.toFixed(2)} more for FREE shipping
                      </span>
                      <span className="text-xs xs:text-sm text-blue-700 dark:text-blue-400 font-medium">
                        ${subtotal.toFixed(2)} / ${freeShippingThreshold.toFixed(2)}
                      </span>
                    </div>
                    <div className="w-full h-2 bg-blue-100 dark:bg-blue-800/30 rounded-full overflow-hidden">
                      <div
                        className="h-full bg-blue-500 dark:bg-blue-600 rounded-full"
                        style={{ width: `${(subtotal / freeShippingThreshold) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                ) : (
                  <div className="mb-6 bg-green-50/10 dark:bg-green-900/10 rounded-xl p-4 border border-green-100/30 dark:border-green-800/30 flex items-center gap-2">
                    <Check className="h-4 xs:h-5 w-4 xs:w-5 text-green-600 dark:text-green-500 flex-shrink-0" />
                    <span className="text-xs xs:text-sm text-green-700 dark:text-green-500 font-medium">
                      You&apos;ve unlocked FREE shipping!
                    </span>
                  </div>
                )}

                {/* Mobile Cart Items (visible on small screens) */}
                <div className="md:hidden">
                  {items.map((item) => (
                    <CartItemMobile
                      key={item.id}
                      item={item}
                      updateQuantity={updateQuantity}
                      removeItem={removeItem}
                    />
                  ))}
                </div>

                {/* Desktop Cart Items Table (hidden on small screens) */}
                <div className="hidden md:block overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-muted text-left">
                      <tr>
                        <th className="py-4 px-6 text-sm font-medium text-muted-foreground">Product</th>
                        <th className="py-4 px-6 text-sm font-medium text-muted-foreground">Price</th>
                        <th className="py-4 px-6 text-sm font-medium text-muted-foreground">Quantity</th>
                        <th className="py-4 px-6 text-sm font-medium text-muted-foreground">Total</th>
                        <th className="py-4 px-6 text-sm font-medium text-muted-foreground sr-only">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-border bg-card">
                      {items.map((item) => (
                        <tr key={item.id} className="hover:bg-muted/50 transition-colors">
                          <td className="py-4 px-6">
                            <div className="flex items-center gap-4">
                              <div className="w-16 h-16 bg-muted rounded-lg overflow-hidden relative flex-shrink-0">
                                <Image
                                  src={item.image}
                                  alt={item.name}
                                  fill
                                  className="object-cover"
                                />
                                {item.originalPrice > item.price && (
                                  <div className="absolute top-0 right-0 bg-accent text-white text-[10px] px-1.5 py-0.5 font-medium">
                                    SALE
                                  </div>
                                )}
                              </div>
                              <div>
                                <h4 className="text-sm font-medium text-card-foreground">{item.name}</h4>
                                <p className="text-xs text-muted-foreground mt-0.5">
                                  {item.vendor} • {item.color}
                                </p>
                              </div>
                            </div>
                          </td>
                          <td className="py-4 px-6">
                            <div className="flex flex-col">
                              <span className="text-sm font-medium text-card-foreground">${item.price.toFixed(2)}</span>
                              {item.originalPrice > item.price && (
                                <span className="text-xs text-muted-foreground line-through">${item.originalPrice.toFixed(2)}</span>
                              )}
                            </div>
                          </td>
                          <td className="py-4 px-6">
                            <div className="flex items-center border border-border rounded-md w-fit">
                              <button
                                onClick={() => updateQuantity(item.id, item.quantity - 1)}
                                className="h-8 w-8 flex items-center justify-center text-muted-foreground hover:text-foreground hover:bg-muted transition-colors"
                                aria-label="Decrease quantity"
                              >
                                <Minus className="h-3 w-3" />
                              </button>
                              <span className="h-8 min-w-[32px] flex items-center justify-center text-sm font-medium">
                                {item.quantity}
                              </span>
                              <button
                                onClick={() => updateQuantity(item.id, item.quantity + 1)}
                                className="h-8 w-8 flex items-center justify-center text-muted-foreground hover:text-foreground hover:bg-muted transition-colors"
                                aria-label="Increase quantity"
                              >
                                <Plus className="h-3 w-3" />
                              </button>
                            </div>
                          </td>
                          <td className="py-4 px-6">
                            <span className="text-sm font-semibold text-card-foreground">
                              ${(item.price * item.quantity).toFixed(2)}
                            </span>
                          </td>
                          <td className="py-4 px-6">
                            <div className="flex items-center gap-2">
                              <button
                                onClick={() => removeItem(item.id)}
                                className="text-muted-foreground hover:text-red-500 transition-colors p-1"
                                aria-label="Remove item"
                              >
                                <Trash2 className="h-4 w-4" />
                              </button>
                              <button
                                className="text-muted-foreground hover:text-pink-500 transition-colors p-1"
                                aria-label="Save for later"
                              >
                                <Heart className="h-4 w-4" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Continue Shopping Button */}
                <div className="mt-8 flex justify-start">
                  <Link
                    href="/shop"
                    className="flex items-center gap-2 text-primary hover:underline text-sm font-medium"
                  >
                    <ArrowRight className="h-4 w-4 rotate-180" />
                    Continue Shopping
                  </Link>
                </div>
              </div>

              {/* Order Summary Section */}
              <div className="lg:col-span-1">
                {/* Mobile Order Summary (Sticky at bottom for small screens) */}
                <div className="fixed bottom-0 left-0 right-0 bg-background border-t border-border p-4 shadow-lg lg:hidden z-10">
                  <div className="flex justify-between items-center mb-3">
                    <div>
                      <span className="text-sm text-muted-foreground">Total:</span>
                      <span className="font-bold text-foreground text-lg ml-2">${total.toFixed(2)}</span>
                    </div>
                    <Link
                      href="/checkout"
                      className="py-2.5 px-4 bg-gradient-to-r from-primary to-primary/80 text-primary-foreground rounded-md text-sm font-medium hover:opacity-90 transition-opacity flex items-center justify-center gap-1 shadow-sm"
                    >
                      Checkout <ChevronRight className="h-4 w-4" />
                    </Link>
                  </div>
                </div>

                {/* Desktop Order Summary */}
                <Card className="bg-muted rounded-xl sticky top-4 hidden lg:block border-0 shadow-none">
                  <CardHeader className="pb-0">
                    <h2 className="font-bold text-xl text-foreground">Order Summary</h2>
                  </CardHeader>

                  <CardContent className="pt-6">
                    <div className="space-y-4 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Subtotal</span>
                        <span className="font-medium text-foreground">${subtotal.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Shipping</span>
                        {shipping === 0 ? (
                          <span className="text-green-600 dark:text-green-500 font-medium">FREE</span>
                        ) : (
                          <span className="font-medium text-foreground">${shipping.toFixed(2)}</span>
                        )}
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Estimated Tax</span>
                        <span className="font-medium text-foreground">${tax.toFixed(2)}</span>
                      </div>
                      {savings > 0 && (
                        <div className="flex justify-between text-muted-foreground">
                          <span className="font-medium">Your Savings</span>
                          <span className="font-medium dark:text-green-500">-${savings.toFixed(2)}</span>
                        </div>
                      )}
                    </div>

                    <div className="border-t border-border my-6 pt-4 flex justify-between items-center">
                      <span className="font-bold text-foreground text-lg">Total</span>
                      <span className="font-bold text-foreground text-xl">${total.toFixed(2)}</span>
                    </div>

                    {/* Checkout Button */}
                    <Link
                      href="/checkout"
                      className="w-full py-3 rounded-full bg-gradient-to-r from-primary to-secondary text-primary-foreground text-sm font-medium hover:opacity-90 transition-opacity flex items-center justify-center gap-1 shadow-sm"
                    >
                      Proceed to Checkout <ChevronRight className="h-4 w-4" />
                    </Link>
                  </CardContent>

                  <CardFooter className="pt-0">
                    {/* Payment Methods */}
                    <div className="w-full">
                      <p className="text-xs text-muted-foreground text-center my-2">We Accept</p>
                      <div className="flex justify-center gap-2">
                        <div className="w-10 h-6 bg-muted-foreground/20 rounded"></div>
                        <div className="w-10 h-6 bg-muted-foreground/20 rounded"></div>
                        <div className="w-10 h-6 bg-muted-foreground/20 rounded"></div>
                        <div className="w-10 h-6 bg-muted-foreground/20 rounded"></div>
                      </div>
                    </div>
                  </CardFooter>
                </Card>

                {/* Mobile Order Summary Details (Collapsible) */}
                <Card className="bg-card rounded-xl border border-border overflow-hidden mb-20 lg:hidden shadow-none">
                  <CardHeader className="p-4 border-b border-border">
                    <h2 className="font-bold text-lg text-card-foreground">Order Summary</h2>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="space-y-3 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Subtotal</span>
                        <span className="font-medium text-card-foreground">${subtotal.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Shipping</span>
                        {shipping === 0 ? (
                          <span className="text-green-600 dark:text-green-500 font-medium">FREE</span>
                        ) : (
                          <span className="font-medium text-card-foreground">${shipping.toFixed(2)}</span>
                        )}
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Estimated Tax</span>
                        <span className="font-medium text-card-foreground">${tax.toFixed(2)}</span>
                      </div>
                      {savings > 0 && (
                        <div className="flex justify-between text-muted-foreground">
                          <span className="font-medium">Your Savings</span>
                          <span className="font-medium dark:text-green-500">-${savings.toFixed(2)}</span>
                        </div>
                      )}
                      <div className="border-t border-border pt-3 mt-3 flex justify-between items-center">
                        <span className="font-bold text-card-foreground">Total</span>
                        <span className="font-bold text-card-foreground text-lg">${total.toFixed(2)}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* You May Also Like Section */}
            <div className="mt-16">
              <ProductGrid
                title="You May Also Like"
                products={[
                  { name: "Smart Home Speaker", category: "Electronics", price: 129.99, discount: true, oldPrice: 159.99, rating: 4.7 },
                  { name: "Fitness Smartwatch", category: "Wearables", price: 199.99, discount: false, rating: 4.8 },
                  { name: "Wireless Earbuds", category: "Audio", price: 119.99, discount: true, oldPrice: 149.99, rating: 4.9 },
                  { name: "Ultra-Thin Laptop", category: "Computers", price: 899.99, discount: false, rating: 4.6 },
                  { name: "Robot Vacuum Cleaner", category: "Home Appliances", price: 199.99, discount: true, oldPrice: 349.99, rating: 4.6 }
                ]}
                shopMoreLink="/shop"
                backgroundColor="bg-background"
                useContainer={false}
              />
            </div>
          </>
        ) : (
          <Card className="bg-card rounded-xl border border-border p-4 sm:p-8 text-center shadow-none">
            <CardContent className="flex flex-col items-center justify-center max-w-md mx-auto p-0">
              <div className="w-20 h-20 sm:w-24 sm:h-24 rounded-full bg-muted flex items-center justify-center mb-4">
                <ShoppingBag className="h-8 w-8 sm:h-10 sm:w-10 text-muted-foreground/50" />
              </div>
              <h3 className="text-foreground font-semibold text-base sm:text-lg mb-2">Your cart is empty</h3>
              <p className="text-muted-foreground text-xs sm:text-sm mb-6">
                Looks like you haven&apos;t added anything to your cart yet.
              </p>
              <Link
                href="/shop"
                className="flex items-center gap-2 py-2.5 sm:py-3 px-5 sm:px-6 bg-gradient-to-r from-primary to-primary/80 text-primary-foreground rounded-md text-sm font-medium hover:opacity-90 transition-opacity shadow-sm"
              >
                Start Shopping <ArrowRight className="h-4 w-4" />
              </Link>
            </CardContent>
          </Card>
        )}
      </Container>
    </div>
  );
}
