/**
 * Store related type definitions
 */

export type Store = {
  id: string;
  name: string;
  slug: string;
  image: string;
  rating: number;
  productCount: number;
  categories: string[];
  discount?: string;
  logo?: string;
  description?: string;
  address?: string;
  phone?: string;
  email?: string;
  website?: string;
  openingHours?: string;
  socialLinks?: StoreSocialLinks;
  joinedDate?: string;
};

export type StoreSocialLinks = {
  facebook?: string;
  instagram?: string;
  twitter?: string;
  linkedin?: string;
  [key: string]: string | undefined; // Allow for additional social platforms
};

export type StoreCategory = {
  name: string;
  slug: string;
  icon: React.ComponentType<{ className?: string; size?: number }>;
};
