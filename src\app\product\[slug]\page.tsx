"use client";

import React, { useState, useRef, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { Container } from "@/components/ui/container";
import {
  Heart,
  ShoppingCart,
  Star,
  Share2,
  ChevronRight,
  Minus,
  Plus,
  Bookmark,
  ChevronLeft,
  ChevronRight as ChevronRightIcon,
  Search
} from "lucide-react";
import { ProductGrid } from "@/components/sections/product-grid";
import { ProductReviews } from "@/components/product/product-reviews";
import { CustomBreadcrumb } from "@/components/ui/custom-breadcrumb";
import { useIsMobile } from "@/hooks/use-mobile";

// Mock product data
const productData = {
  id: "brown-bear-printed-sweater",
  name: "Brown Bear Printed Sweater",
  category: "Moisturiser",
  price: 28.72,
  oldPrice: 35.90,
  discount: true,
  discountPercentage: 20,
  rating: 4.8,
  reviewCount: 12,
  inStock: true,
  description: "Inspired by the beauty of nature, this cozy sweater features a charming brown bear print. Made from a soft blend of cotton and wool, it's perfect for staying warm during chilly days. The relaxed fit and ribbed cuffs provide comfort and style.",
  details: {
    composition: "80% cotton, 20% wool",
    properties: "Soft, warm, and breathable",
    style: "Casual",
    pattern: "Animal print",
    care: "Machine wash cold, tumble dry low"
  },
  features: [
    "Ribbed cuffs and hem",
    "Crew neckline",
    "Regular fit",
    "Soft and comfortable fabric",
    "Suitable for everyday wear"
  ],
  images: [
    "/images/products/headphone.png",
    "/images/products/berry.png",
    "/images/products/headphone.png",
    "/images/products/berry.png"
  ],
  sizes: ["XS", "S", "M", "L", "XL"],
  colors: ["Brown", "Gray", "Black"],
  tags: ["Sweater", "Winter", "Casual", "Cotton", "Wool"]
};

// Mock related products
const relatedProducts = [
  {
    id: "wireless-headphones",
    name: "Wireless Noise Cancelling Headphones",
    category: "Electronics",
    price: 129.99,
    oldPrice: 249.99,
    discount: true,
    discountPercentage: 48,
    rating: 4.5,
    image: "/images/products/headphone.png"
  },
  {
    id: "organic-juice",
    name: "Organic Cold-Pressed Juice Set",
    category: "Food & Beverages",
    price: 34.99,
    discount: false,
    rating: 4.4,
    image: "/images/products/berry.png"
  },
  {
    id: "leather-wallet",
    name: "Premium Leather Minimalist Wallet",
    category: "Accessories",
    price: 79.99,
    oldPrice: 99.99,
    discount: true,
    discountPercentage: 20,
    rating: 4.7,
    image: "/images/products/headphone.png"
  },
  {
    id: "security-camera",
    name: "Smart Home Security Camera",
    category: "Electronics",
    price: 129.99,
    discount: false,
    rating: 4.8,
    image: "/images/products/berry.png"
  }
];

export default function ProductDetailPage() {
  const [activeTab, setActiveTab] = useState("description");
  const [quantity, setQuantity] = useState(1);
  const [activeImage, setActiveImage] = useState(0);
  const [showMagnifier, setShowMagnifier] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  // Use the custom hook for mobile detection to avoid hydration issues
  const isMobile = useIsMobile();

  // References for the image container and image element
  const imageContainerRef = useRef<HTMLDivElement>(null);
  const magnifierSize = { width: 600, height: 400 }; // Increased width from 500 to 600
  const zoomLevel = 2.5; // Adjusted zoom level for better clarity

  // Function to navigate to the previous image
  const prevImage = () => {
    setActiveImage((prev) => (prev === 0 ? product.images.length - 1 : prev - 1));
  };

  // Function to navigate to the next image
  const nextImage = () => {
    setActiveImage((prev) => (prev === product.images.length - 1 ? 0 : prev + 1));
  };

  // Function to handle mouse enter
  const handleMouseEnter = () => {
    if (!isMobile) {
      setShowMagnifier(true);
    }
  };

  // Function to handle mouse leave
  const handleMouseLeave = () => {
    setShowMagnifier(false);
  };

  // Function to handle mouse move
  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (imageContainerRef.current && !isMobile) {
      const { left, top, width, height } = imageContainerRef.current.getBoundingClientRect();

      // Calculate mouse position relative to the image container
      const x = ((e.clientX - left) / width);
      const y = ((e.clientY - top) / height);

      // Ensure values are between 0 and 1
      const boundedX = Math.max(0, Math.min(1, x));
      const boundedY = Math.max(0, Math.min(1, y));

      setMousePosition({ x: boundedX, y: boundedY });
    }
  };

  // Placeholder for actual product data fetching
  const product = productData;

  // Handle quantity change
  const decreaseQuantity = () => {
    if (quantity > 1) {
      setQuantity(quantity - 1);
    }
  };

  const increaseQuantity = () => {
    setQuantity(quantity + 1);
  };

  return (
    <div className="bg-background dark:bg-card min-h-screen pb-12">
      {/* Breadcrumb */}
      <CustomBreadcrumb
        items={[
          { label: "Home", href: "/" },
          { label: "Moisturiser", href: "/category/moisturiser" },
          { label: product.name }
        ]}
        backgroundColor="bg-[#E8F7F6]"
      />

      {/* Product Detail Section with Highlight Rectangle */}
      <Container className="mt-8 relative">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Product Images */}
          <div className="space-y-4">
            {/* Main Image with Magnifier */}
            <div className="relative">
              {/* Main Image Container */}
              <div
                ref={imageContainerRef}
                className="relative mx-auto w-full h-[450px] border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden cursor-crosshair dark:bg-gray-800/30"
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
                onMouseMove={handleMouseMove}
              >
                <Image
                  src={product.images[activeImage] || "/images/products/headphone.png"}
                  alt={product.name}
                  fill
                  className="object-contain transition-all duration-300 hover:opacity-95"
                  priority
                />

                {/* Selection Rectangle - Only visible when magnifying */}
                {showMagnifier && (
                  <div
                    className="absolute pointer-events-none border border-secondary/30 dark:border-secondary/50 bg-secondary/20 dark:bg-secondary/30"
                    style={{
                      left: `${mousePosition.x * 100}%`,
                      top: `${mousePosition.y * 100}%`,
                      width: '200px',
                      height: '140px',
                      transform: 'translate(-50%, -50%)',
                      zIndex: 15
                    }}
                  />
                )}

                {product.discount && (
                  <div className="absolute top-4 left-4 bg-gradient-to-r from-red-500 to-red-600 text-white text-xs font-bold px-2 py-0.5 rounded shadow-sm">
                    {product.discountPercentage ? `-${product.discountPercentage}%` : 'SALE'}
                  </div>
                )}

                {/* Hover Indicator - Only show on non-mobile */}
                {!isMobile && (
                  <div className="absolute bottom-3 right-3 bg-white/70 dark:bg-gray-800/40 text-primary dark:text-accent/60 text-xs px-2 py-1 rounded-md shadow-sm flex items-center gap-1">
                    <Search className="h-3 w-3" /> Hover to zoom
                  </div>
                )}

                {/* Image Navigation Buttons */}
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    prevImage();
                  }}
                  className="absolute left-2 top-1/2 -translate-y-1/2 text-slate-500 hover:text-slate-600 bg-white/50 dark:bg-gray-800/40 p-2 rounded-full shadow-md z-10 transition-all"
                >
                  <ChevronLeft className="h-4 w-4" />
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    nextImage();
                  }}
                  className="absolute right-2 top-1/2 -translate-y-1/2 text-slate-500 hover:text-slate-600 bg-white/50 dark:bg-gray-800/40 p-2 rounded-full shadow-md z-10 transition-all"
                >
                  <ChevronRightIcon className="h-4 w-4" />
                </button>
              </div>

              {/* Magnified Image - Shows on hover (only on desktop) */}
              {showMagnifier && !isMobile && (
                <div
                  className="absolute rounded-lg overflow-hidden shadow-lg bg-white dark:bg-gray-800 z-20 hidden md:block"
                  style={{
                    width: `${magnifierSize.width}px`,
                    height: `${magnifierSize.height}px`,
                    top: '0',
                    left: '100%',
                    marginLeft: '20px'
                  }}
                >
                  <div
                    className="relative w-full h-full"
                    style={{
                      backgroundImage: `url(${product.images[activeImage] || "/images/products/headphone.png"})`,
                      backgroundPosition: `${mousePosition.x * 100}% ${mousePosition.y * 100}%`,
                      backgroundRepeat: 'no-repeat',
                      backgroundSize: `${zoomLevel * 100}%`,
                    }}
                  />
                </div>
              )}
            </div>

            {/* Thumbnail Gallery */}
            <div className="flex justify-center gap-3 mt-4">
              {product.images.map((image, index) => (
                <button
                  key={index}
                  className={`relative h-20 w-20 border-2 rounded-md overflow-hidden transition-all ${
                    activeImage === index ? 'border-primary/60 scale-110 dark:border-primary/60' : ''
                  }`}
                  onClick={() => setActiveImage(index)}
                  onMouseEnter={() => setActiveImage(index)}
                >
                  <Image
                    src={image || "/images/products/headphone.png"}
                    alt={`${product.name} - view ${index + 1}`}
                    fill
                    className="object-cover"
                  />
                </button>
              ))}
            </div>
          </div>

          {/* Product Info */}
          <div className="space-y-6">
            <h1 className="text-2xl md:text-3xl font-bold text-primary dark:text-primary">{product.name}</h1>

            {/* Rating */}
            <div className="flex items-center gap-1">
              {Array.from({ length: 5 }).map((_, i) => (
                <Star
                  key={i}
                  className={`h-4 w-4 ${i < Math.floor(product.rating) ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300 dark:text-gray-600'}`}
                />
              ))}
              <span className="text-sm text-gray-600 dark:text-gray-400 ml-2">{product.rating} ({product.reviewCount} reviews)</span>
            </div>

            {/* Price */}
            <div className="flex items-center gap-2">
              <span className="text-2xl font-bold text-accent dark:text-accent/70">${product.price.toFixed(2)}</span>
              {product.discount && product.oldPrice && (
                <span className="text-gray-400 dark:text-gray-500 text-lg line-through">${product.oldPrice.toFixed(2)}</span>
              )}
              {product.discount && product.discountPercentage && (
                <span className="bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 text-xs font-semibold px-2 py-1 rounded">
                  Save {product.discountPercentage}%
                </span>
              )}
            </div>

            {/* Availability */}
            <div className="text-sm">
              <span className="text-gray-600 dark:text-gray-400">Availability: </span>
              {product.inStock ? (
                <span className="text-green-600 dark:text-green-500 font-medium">In Stock</span>
              ) : (
                <span className="text-red-600 dark:text-red-400 font-medium">Out of Stock</span>
              )}
            </div>

            {/* Divider */}
            <div className="border-t border-gray-200 dark:border-gray-700 my-4"></div>

            {/* Short Description */}
            <p className="text-gray-600 dark:text-gray-300">{product.description}</p>

            {/* Quantity Selector */}
            <div className="flex items-center gap-4">
              <span className="text-gray-600 dark:text-gray-300 font-medium">Quantity:</span>
              <div className="flex items-center border border-gray-300 dark:border-gray-700 rounded-md">
                <button
                  className="px-3 py-2 text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary transition-colors"
                  onClick={decreaseQuantity}
                >
                  <Minus className="h-4 w-4" />
                </button>
                <span className="px-3 py-2 border-x border-gray-300 dark:border-gray-700 min-w-[40px] text-center dark:text-gray-200">
                  {quantity}
                </span>
                <button
                  className="px-3 py-2 text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary transition-colors"
                  onClick={increaseQuantity}
                >
                  <Plus className="h-4 w-4" />
                </button>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-wrap gap-3">
              <button className="flex-1 bg-gradient-to-r from-primary to-secondary text-white px-6 py-3 rounded-full flex items-center justify-center gap-2 hover:shadow-md transition-all duration-300 hover:scale-[1.02]">
                <ShoppingCart className="h-5 w-5" />
                Add to Cart
              </button>
              <button className="bg-white dark:bg-card border border-gray-200 dark:border-border text-gray-700 dark:text-gray-300 px-4 py-3 rounded-full flex items-center justify-center hover:border-accent hover:text-accent dark:hover:border-accent dark:hover:text-accent transition-colors">
                <Heart className="h-4 w-4" />
              </button>
              <button className="bg-white dark:bg-card border border-gray-200 dark:border-border text-gray-700 dark:text-gray-300 px-4 py-3 rounded-full flex items-center justify-center hover:border-accent hover:text-accent dark:hover:border-accent dark:hover:text-accent transition-colors">
                <Share2 className="h-4 w-4" />
              </button>
            </div>

            {/* Tags */}
            <div className="flex flex-wrap gap-2 text-sm">
              <span className="text-gray-600 dark:text-gray-400">Tags:</span>
              {product.tags.map((tag, index) => (
                <Link
                  key={index}
                  href={`/tag/${tag.toLowerCase()}`}
                  className="text-accent dark:text-gray-300 hover:underline"
                >
                  {tag}{index < product.tags.length - 1 ? ',' : ''}
                </Link>
              ))}
            </div>

            {/* Social Share */}
            <div className="flex items-center gap-3 pt-2">
              <span className="text-sm text-gray-600 dark:text-gray-400">Share:</span>
              <div className="flex gap-2">
                <button className="bg-gradient-to-r from-primary to-primary/80 text-white p-1.5 rounded-full hover:shadow-md transition-all duration-300 hover:scale-105">
                  <Share2 className="h-4 w-4" />
                </button>
                <button className="bg-gradient-to-r from-primary to-primary/80 text-white p-1.5 rounded-full hover:shadow-md transition-all duration-300 hover:scale-105">
                  <Heart className="h-4 w-4" />
                </button>
                <button className="bg-gradient-to-r from-secondary to-secondary/80 text-white p-1.5 rounded-full hover:shadow-md transition-all duration-300 hover:scale-105">
                  <Star className="h-4 w-4" />
                </button>
                <button className="bg-gradient-to-r from-secondary to-secondary/80 text-white p-1.5 rounded-full hover:shadow-md transition-all duration-300 hover:scale-105">
                  <Bookmark className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Product Tabs */}
        <div className="mt-12">
          {/* Tab Headers */}
          <div className="flex border-b border-gray-200 dark:border-gray-700">
            <button
              className={`px-6 py-3 text-sm font-medium ${
                activeTab === "description"
                  ? "text-primary border-b-2 border-primary"
                  : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200"
              }`}
              onClick={() => setActiveTab("description")}
            >
              Description
            </button>
            <button
              className={`px-6 py-3 text-sm font-medium ${
                activeTab === "details"
                  ? "text-primary border-b-2 border-primary"
                  : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200"
              }`}
              onClick={() => setActiveTab("details")}
            >
              Product Details
            </button>
          </div>

          {/* Tab Content */}
          <div className="py-6">
            {activeTab === "description" && (
              <div className="prose dark:prose-invert max-w-none">
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                  {product.description}
                </p>
                <ul className="mt-4 space-y-2">
                  {product.features.map((feature, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <ChevronRight className="h-5 w-5 text-accent flex-shrink-0 mt-0.5" />
                      <span className="text-gray-600 dark:text-gray-300">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {activeTab === "details" && (
              <div className="space-y-4">
                <table className="w-full border-collapse">
                  <tbody>
                    {Object.entries(product.details).map(([key, value], index) => (
                      <tr key={index} className={index % 2 === 0 ? "bg-gray-50 dark:bg-gray-800/50" : "dark:bg-card"}>
                        <td className="py-3 px-4 border border-gray-200 dark:border-gray-700 font-medium capitalize dark:text-gray-200">{key}</td>
                        <td className="py-3 px-4 border border-gray-200 dark:border-gray-700 text-gray-600 dark:text-gray-300">{value}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}


          </div>
        </div>

        {/* Related Products */}
        <div className="mt-12">
          <h2 className="text-xl font-bold mb-6 dark:text-gray-100">Related Products</h2>
          <ProductGrid
            products={relatedProducts}
            shopMoreLink="/shop"
            showTitle={false}
            useContainer={false}
          />
        </div>

        {/* Customer Reviews Section */}
        <ProductReviews
          productName={product.name}
          rating={product.rating}
          reviewCount={product.reviewCount}
        />
      </Container>
    </div>
  );
}
