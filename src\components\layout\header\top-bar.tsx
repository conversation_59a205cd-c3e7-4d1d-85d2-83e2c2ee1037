"use client";

import React from "react";
import Link from "next/link";
import { MapPin, Package, Shield, HelpCircle, Smartphone, ChevronDown, Globe } from "lucide-react";
import { Container } from "@/components/ui/container";
import { ThemeToggle } from "@/components/theme/theme-toggle";

export function TopBar() {
  return (
    <div className="w-full bg-muted dark:bg-card py-2 text-xs border-b border-gray-100 dark:border-border">
      <Container className="flex items-center justify-between">
        <div className="flex items-center space-x-2 xs:space-x-3 sm:space-x-4 md:space-x-6">
          <div className="flex items-center space-x-1 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors duration-200">
            <MapPin className="h-3 w-3 text-primary/70 dark:text-primary/80" />
            <span className="hidden xs:inline">New York</span>
          </div>
          <div className="flex items-center space-x-1">
            <Package className="h-3 w-3 text-primary/70 dark:text-primary/80" />
            <Link href="/track-order" className="hover:underline hover:text-primary dark:hover:text-primary transition-colors duration-200 whitespace-nowrap">
              Track Order
            </Link>
          </div>
        </div>

        <div className="flex items-center space-x-2 xs:space-x-3 sm:space-x-4 md:space-x-6">
          {/* Login/Register Links */}
          <div className="flex items-center space-x-3">
            {/* Vendor Login/Register with Gradient */}
            <div className="flex items-center space-x-2">
              <Link
                href="/vendor/register"
                className="bg-gradient-to-r from-primary to-secondary text-white px-2 py-1 rounded-full text-[10px] font-medium shadow-sm transition-all duration-300 hover:shadow-md hover:brightness-110"
              >
                Register / Login Store
              </Link>
            </div>
            <div className="h-3 w-px bg-gray-300 dark:bg-gray-700 hidden xs:block"></div>
          </div>

          {/* Show on small tablets and up */}
          <div className="hidden sm:flex items-center space-x-1">
            <Shield className="h-3 w-3 text-primary/70 dark:text-primary/80" />
            <Link href="/buyer-protection" className="hover:underline hover:text-primary dark:hover:text-primary transition-colors duration-200">
              Buyer Protection
            </Link>
          </div>
          {/* Show on small tablets and up */}
          <div className="hidden sm:flex items-center space-x-1">
            <HelpCircle className="h-3 w-3 text-primary/70 dark:text-primary/80" />
            <Link href="/help" className="hover:underline hover:text-primary dark:hover:text-primary transition-colors duration-200">
              Help
            </Link>
          </div>
          {/* Show on medium tablets and up */}
          {/* <div className="hidden md:flex items-center space-x-1">
            <Smartphone className="h-3 w-3 text-primary/70 dark:text-primary/80" />
            <Link href="/app" className="hover:underline hover:text-primary dark:hover:text-primary transition-colors duration-200">
              Save big on our app!
            </Link>
          </div> */}

          <div className="flex items-center space-x-3 ml-1 xs:ml-2 border-l border-gray-300 dark:border-gray-700 pl-2 xs:pl-3 sm:pl-4">
            {/* Theme Toggle */}
            <div className="flex items-center">
              <ThemeToggle className="scale-75 sm:scale-80" />
            </div>

            {/* Language Selector */}
            <div className="relative group">
              <button className="hover:text-primary dark:hover:text-primary transition-colors duration-200 p-1 rounded-full hover:bg-accent/10 dark:hover:bg-accent/5 flex items-center">
                <Globe className="h-3 w-3 sm:h-3.5 sm:w-3.5 text-primary/70 dark:text-primary/80" />
                <span className="ml-1 text-xs hidden xs:inline">EN</span>
                <ChevronDown className="h-2.5 w-2.5 ml-0.5 text-gray-400 dark:text-gray-500" />
              </button>
              <div className="absolute right-0 top-full pt-2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform translate-y-2 group-hover:translate-y-0 z-50">
                <div className="bg-card rounded-lg shadow-lg dark:shadow-none border border-border p-1.5 w-28">
                  <button className="w-full text-left px-2 py-1.5 text-xs hover:bg-accent/10 dark:hover:bg-accent/5 rounded-md transition-colors">English</button>
                  <button className="w-full text-left px-2 py-1.5 text-xs hover:bg-accent/10 dark:hover:bg-accent/5 rounded-md transition-colors">Español</button>
                  <button className="w-full text-left px-2 py-1.5 text-xs hover:bg-accent/10 dark:hover:bg-accent/5 rounded-md transition-colors">Français</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Container>
    </div>
  );
}
