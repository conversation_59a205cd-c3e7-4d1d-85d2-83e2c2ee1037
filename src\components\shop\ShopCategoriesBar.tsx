"use client";

import React, { useState } from "react";
import <PERSON> from "next/link";
import { Container } from "@/components/ui/container";
import {
  Palette,
  Car,
  BookOpen,
  Cpu,
  Shirt,
  Smartphone,
  Gamepad,
  Gift,
  Apple,
  Stethoscope,
  Home,
  Laptop,
  TabletSmartphone,
  Music,
  Watch,
  Dumbbell,
  PenTool,
  Wrench,
  ChevronLeft,
  ChevronRight
} from "lucide-react";

// Using the same categories from StoreByCategory component
const categories = [
  {
    name: "Electronics",
    slug: "electronics",
    icon: Cpu
  },
  {
    name: "Fashion",
    slug: "fashion",
    icon: Shirt
  },
  {
    name: "Gadgets",
    slug: "gadgets",
    icon: Smartphone
  },
  {
    name: "Home & Kitchen",
    slug: "home-kitchen",
    icon: Home
  },
  {
    name: "Books",
    slug: "books-readables",
    icon: BookOpen
  },
  {
    name: "Health & Beauty",
    slug: "health-beauty",
    icon: Stethoscope
  },
  {
    name: "Sports",
    slug: "sports-outdoors",
    icon: <PERSON><PERSON><PERSON>
  },
  {
    name: "Automotive",
    slug: "automotive",
    icon: Car
  },
  {
    name: "Art & Crafts",
    slug: "art-crafts-handmades",
    icon: Palette
  },
  {
    name: "Gaming",
    slug: "games-softwares",
    icon: Gamepad
  },
  {
    name: "Gifts",
    slug: "gifts-presents",
    icon: Gift
  },
  {
    name: "Grocery",
    slug: "grocery-vegetables",
    icon: Apple
  },
  {
    name: "Laptops",
    slug: "laptop-computer",
    icon: Laptop
  },
  {
    name: "Mobile",
    slug: "mobile-tablets",
    icon: TabletSmartphone
  },
  {
    name: "Music",
    slug: "music-others",
    icon: Music
  },
  {
    name: "Watches",
    slug: "smart-watches-wearable",
    icon: Watch
  },
  {
    name: "Stationery",
    slug: "stationary-office",
    icon: PenTool
  },
  {
    name: "Tools",
    slug: "tools-home-improvement",
    icon: Wrench
  }
];

export interface ShopCategoriesBarProps {
  activeCategories?: string[];
  onCategoryChange?: (category: string) => void;
}

export function ShopCategoriesBar({
  activeCategories = [],
  onCategoryChange
}: ShopCategoriesBarProps) {
  const [scrollPosition, setScrollPosition] = useState(0);
  const maxScroll = categories.length * 100 - 1000; // Approximate width calculation

  const scrollLeft = () => {
    setScrollPosition(Math.max(0, scrollPosition - 300));
  };

  const scrollRight = () => {
    setScrollPosition(Math.min(maxScroll, scrollPosition + 300));
  };

  const handleCategoryClick = (e: React.MouseEvent<HTMLAnchorElement>, categoryName: string) => {
    if (onCategoryChange) {
      e.preventDefault();
      onCategoryChange(categoryName);
    }
  };

  return (
    <div className="bg-[#F7F7F9] dark:bg-gray-800/50 mb-6">
      <Container>


        <div className="relative overflow-hidden">
          {/* Left scroll button */}
          {scrollPosition > 0 && (
            <button
              onClick={scrollLeft}
              className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-gradient-to-r from-[#F7F7F9] via-[#F7F7F9]/95 to-transparent dark:from-card dark:via-card/95 pl-1 pr-3 py-1 text-gray-600 dark:text-gray-400 hover:text-primary transition-colors"
            >
              <ChevronLeft className="h-4 w-4" />
            </button>
          )}

          {/* Categories scroll container */}
          <div className="overflow-x-hidden">
            <div
              className="flex space-x-3 py-2 transition-transform duration-300 ease-out"
              style={{ transform: `translateX(-${scrollPosition}px)` }}
            >
              {categories.map((category) => (
                <Link
                  key={category.slug}
                  href={`/category/${category.slug}`}
                  onClick={(e) => handleCategoryClick(e, category.name)}
                  className={`flex items-center gap-1.5 whitespace-nowrap px-3 py-1.5 rounded-full border ${
                      activeCategories.includes(category.name)
                        ? "bg-gradient-to-r from-primary to-secondary text-white border-transparent"
                        : "border-gray-100 dark:border-gray-700 hover:border-primary/30 dark:hover:border-secondary/30 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-secondary hover:bg-primary/5 dark:hover:bg-secondary/10"
                    } transition-colors flex-shrink-0`}
                >
                  <category.icon className={`h-3.5 w-3.5 ${
                    activeCategories.includes(category.name)
                      ? "text-white"
                      : "text-primary dark:text-secondary"
                  }`} />
                  <span className={`text-xs font-medium ${
                    activeCategories.includes(category.name)
                      ? "text-white"
                      : "text-primary dark:text-secondary"
                  }`}>{category.name}</span>
                </Link>
              ))}
            </div>
          </div>

          {/* Right scroll button */}
          {scrollPosition < maxScroll && (
            <button
              onClick={scrollRight}
              className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-gradient-to-l from-[#F7F7F9] via-[#F7F7F9]/95 to-transparent dark:from-card dark:via-card/95 pr-1 pl-3 py-1 text-gray-600 dark:text-gray-400 hover:text-primary transition-colors"
            >
              <ChevronRight className="h-4 w-4" />
            </button>
          )}
        </div>
      </Container>
    </div>
  );
}
