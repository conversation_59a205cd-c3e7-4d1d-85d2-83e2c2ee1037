This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Multi-Tenant E-commerce Application

A modern multi-tenant e-commerce platform built with Next.js 15, TypeScript, and shadcn/ui components.

### Features

- **Multi-tenant Architecture**: Separate dashboards for superadmin, vendor, and customer
- **Authentication**: Secure JWT-based authentication with Nest.js backend
- **Modern UI**: Built with shadcn/ui components and Tailwind CSS
- **TypeScript**: Full type safety throughout the application
- **Responsive Design**: Mobile-first responsive design

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Nest.js backend running on port 4000 (or configure in .env.local)

### Installation

1. Clone the repository
2. Install dependencies:

```bash
npm install
```

3. Create environment file:

```bash
cp .env.example .env.local
```

4. Update the API URL in `.env.local`:

```env
NEXT_PUBLIC_API_URL=http://localhost:4000
```

5. Run the development server:

```bash
npm run dev
```

### Colors
Primary: #052E7F
Secondary: #2A928F
Accent: #D9F7F5
body background: #FFFFFF

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## UI Components with shadcn/ui

This project uses [shadcn/ui](https://ui.shadcn.com/) for building beautiful, accessible UI components with Tailwind CSS and Radix UI primitives.

### Adding New Components

To add a new shadcn/ui component, run:

```bash
npx shadcn-ui@latest add <component>
# e.g.
npx shadcn-ui@latest add button
```

This will add the component to `src/components/ui/`.

You can then import and use the component in your app, for example:

```tsx
import { Button } from "@/components/ui/button";

export default function Example() {
  return <Button>Click me</Button>;
}
```

- See the [shadcn/ui documentation](https://ui.shadcn.com/docs/installation/next) for more details and available components.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.

