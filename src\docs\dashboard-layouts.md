# Dashboard Layouts Documentation

This document provides an overview of the dashboard layout structure in the multi-tenant e-commerce application.

## Overview

The application has separate dashboard layouts for different user types:
- **SuperAdmin**: Main tenant dashboard
- **Vendor**: Store owner dashboard

Each dashboard uses a shared component structure but maintains its own navigation items and styling.

## Component Structure

### Shared Components

1. **DashboardLayout** (`src/components/dashboard/layout.tsx`)
   - Base layout component used by both SuperAdmin and Vendor layouts
   - Handles the sidebar, header, and main content areas
   - Uses the SidebarProvider from shadcn UI
   - Implements the sidebar-07 component pattern from shadcn UI
   - Supports collapsible sidebar with icon-only mode

2. **DashboardSidebar** (`src/components/dashboard/sidebar.tsx`)
   - Shared sidebar component with customizable navigation
   - Takes navigation data, logo, and user information as props
   - Renders navigation items with proper collapsible sections
   - Supports nested navigation with expandable sub-items
   - Includes user profile section in the sidebar footer

### User-Specific Components

1. **SuperAdmin**
   - Layout: `src/components/layout/superadmin/layout.tsx`
   - Sidebar: `src/components/layout/superadmin/sidebar.tsx`
   - Header: `src/components/layout/superadmin/header.tsx`
   - Navigation data: `src/data/superadmin-nav.ts`

2. **Vendor**
   - Layout: `src/components/layout/vendor/layout.tsx`
   - Sidebar: `src/components/layout/vendor/sidebar.tsx`
   - Header: `src/components/layout/vendor/header.tsx`
   - Navigation data: `src/data/vendor-nav.ts`

## Routing

The `LayoutProvider` component (`src/components/layout/layout-provider.tsx`) determines which layout to use based on the current URL path:

- URLs starting with `/superadmin` use the SuperAdmin layout
- URLs starting with `/vendor` use the Vendor layout
- All other URLs use the Customer layout

Special cases:
- `/superadmin/login` has no layout (direct rendering)
- `/vendor/login` and `/vendor/register` have no layout (direct rendering)

## TypeScript Types

Navigation item types are defined in `src/types/navigation.ts`:

- `BaseNavItem`: Basic navigation item with title, URL, and optional icon
- `NavItem`: Extends BaseNavItem with optional sub-items
- `DashboardNavData`: Collection of navigation items with a group label

## Styling

The dashboard layouts use the application's color scheme:
- Primary: #052E7F
- Secondary: #2A928F
- Accent: #D9F7F5
- Background: #FFFFFF

Dark mode is supported through the theme provider and CSS variables.

## Adding New Navigation Items

To add new navigation items to a dashboard:

1. Update the corresponding navigation data file:
   - SuperAdmin: `src/data/superadmin-nav.ts`
   - Vendor: `src/data/vendor-nav.ts`

2. Follow the existing pattern for items with or without sub-items

Example:
```typescript
{
  title: "New Section",
  url: "/superadmin/new-section",
  icon: NewIcon,
  items: [
    {
      title: "Sub Item 1",
      url: "/superadmin/new-section/sub-1",
    },
    {
      title: "Sub Item 2",
      url: "/superadmin/new-section/sub-2",
    }
  ],
}
```

## Best Practices

1. Keep navigation data separate from component files
2. Use TypeScript interfaces for type safety
3. Maintain consistent styling across dashboards
4. Use the shared components for new dashboard types
5. Follow the established pattern for new navigation items
