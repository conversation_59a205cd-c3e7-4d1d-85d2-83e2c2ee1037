import { Container } from "@/components/ui/container";
import { Truck, RefreshCcw, Headphones, Mail } from "lucide-react";

type FeaturesSectionProps = {
  backgroundColor?: string;
};

export const FeaturesSection = ({ backgroundColor = "bg-white" }: FeaturesSectionProps) => {
  return (
    <div className={`py-4 border-b border-gray-100 dark:border-border ${backgroundColor} dark:bg-card`}>
      <Container>
        <div className="flex flex-wrap justify-between items-center">
          {/* Feature 1: Payment & Delivery */}
          <div className="flex items-center px-3 py-2">
            <div className="w-7 h-7 flex-shrink-0 bg-gradient-soft-primary dark:bg-primary/20 rounded-full flex items-center justify-center">
              <Truck className="h-3.5 w-3.5 text-primary" strokeWidth={2.5} />
            </div>
            <div className="ml-2.5">
              <span className="text-xs font-semibold text-gray-800 dark:text-gray-100">Payment & Delivery</span>
              <p className="text-[10px] text-gray-500 dark:text-gray-400">Free shipping for orders over $50</p>
            </div>
          </div>

          {/* Divider */}
          <div className="hidden md:block h-6 w-px bg-gray-200 dark:bg-gray-700"></div>

          {/* Feature 2: Return & Refund */}
          <div className="flex items-center px-3 py-2">
            <div className="w-7 h-7 flex-shrink-0 bg-gradient-soft-secondary dark:bg-secondary/20 rounded-full flex items-center justify-center">
              <RefreshCcw className="h-3.5 w-3.5 text-secondary" strokeWidth={2.5} />
            </div>
            <div className="ml-2.5">
              <span className="text-xs font-semibold text-gray-800 dark:text-gray-100">Return & Refund</span>
              <p className="text-[10px] text-gray-500 dark:text-gray-400">Free 100% money back guarantee</p>
            </div>
          </div>

          {/* Divider */}
          <div className="hidden md:block h-6 w-px bg-gray-200 dark:bg-gray-700"></div>

          {/* Feature 3: Quality Support */}
          <div className="flex items-center px-3 py-2">
            <div className="w-7 h-7 flex-shrink-0 bg-gradient-soft-accent dark:bg-accent/20 rounded-full flex items-center justify-center">
              <Headphones className="h-3.5 w-3.5 text-destructive" strokeWidth={2.5} />
            </div>
            <div className="ml-2.5">
              <span className="text-xs font-semibold text-gray-800 dark:text-gray-100">Quality Support</span>
              <p className="text-[10px] text-gray-500 dark:text-gray-400">Always online feedback 24/7</p>
            </div>
          </div>

          {/* Divider */}
          <div className="hidden md:block h-6 w-px bg-gray-200 dark:bg-gray-700"></div>

          {/* Feature 4: Join Our Newsletter */}
          <div className="flex items-center px-3 py-2">
            <div className="w-7 h-7 flex-shrink-0 bg-gradient-soft-primary dark:bg-primary/20 rounded-full flex items-center justify-center">
              <Mail className="h-3.5 w-3.5 text-primary" strokeWidth={2.5} />
            </div>
            <div className="ml-2.5">
              <span className="text-xs font-semibold text-gray-800 dark:text-gray-100">Join Our Newsletter</span>
              <p className="text-[10px] text-gray-500 dark:text-gray-400">10% off by subscribing to our newsletter</p>
            </div>
          </div>
        </div>
      </Container>
    </div>
  );
};
