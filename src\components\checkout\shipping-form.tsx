import React from "react";
import { Input } from "@/components/ui/input";
import { Mail, Phone, User, Home, Building, MapPin, Globe, Truck, Zap, ChevronDown } from "lucide-react";

interface ShippingFormProps {
  formData: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    address: string;
    apartment: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
    shippingMethod: string;
  };
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
  handleShippingMethodChange: (method: string) => void;
}

export function ShippingForm({ formData, handleInputChange, handleShippingMethodChange }: ShippingFormProps) {
  return (
    <div className="space-y-6">
      {/* Contact Information */}
      <div>
        <h3 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
          <Mail className="h-4 w-4 text-primary mr-2" />
          Contact Information
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <div>
            <Input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className="w-full"
              placeholder="Email Address *"
              required
            />
          </div>
          <div>
            <Input
              type="tel"
              id="phone"
              name="phone"
              value={formData.phone}
              onChange={handleInputChange}
              className="w-full"
              placeholder="Phone Number *"
              required
            />
          </div>
        </div>
      </div>

      {/* Shipping Address */}
      <div>
        <h3 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
          <MapPin className="h-4 w-4 text-primary mr-2" />
          Shipping Address
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <div>
            <Input
              type="text"
              id="firstName"
              name="firstName"
              value={formData.firstName}
              onChange={handleInputChange}
              className="w-full"
              placeholder="First Name *"
              required
            />
          </div>
          <div>
            <Input
              type="text"
              id="lastName"
              name="lastName"
              value={formData.lastName}
              onChange={handleInputChange}
              className="w-full"
              placeholder="Last Name *"
              required
            />
          </div>
        </div>

        <div className="mt-3">
          <Input
            type="text"
            id="address"
            name="address"
            value={formData.address}
            onChange={handleInputChange}
            className="w-full"
            placeholder="Street Address *"
            required
          />
        </div>

        <div className="mt-3">
          <Input
            type="text"
            id="apartment"
            name="apartment"
            value={formData.apartment}
            onChange={handleInputChange}
            className="w-full"
            placeholder="Apartment, Suite, etc. (optional)"
          />
        </div>

        <div className="grid grid-cols-2 md:grid-cols-3 gap-3 mt-3">
          <div>
            <Input
              type="text"
              id="city"
              name="city"
              value={formData.city}
              onChange={handleInputChange}
              className="w-full"
              placeholder="City *"
              required
            />
          </div>
          <div>
            <Input
              type="text"
              id="state"
              name="state"
              value={formData.state}
              onChange={handleInputChange}
              className="w-full"
              placeholder="State/Province *"
              required
            />
          </div>
          <div className="col-span-2 md:col-span-1">
            <Input
              type="text"
              id="zipCode"
              name="zipCode"
              value={formData.zipCode}
              onChange={handleInputChange}
              className="w-full"
              placeholder="ZIP/Postal Code *"
              required
            />
          </div>
        </div>

        <div className="mt-3">
          <select
            id="country"
            name="country"
            value={formData.country}
            onChange={handleInputChange}
            className="w-full h-9 rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-xs transition-colors outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]"
            required
          >
            <option value="" disabled>Select Country *</option>
            <option value="United States">United States</option>
            <option value="Canada">Canada</option>
            <option value="United Kingdom">United Kingdom</option>
            <option value="Australia">Australia</option>
            <option value="Germany">Germany</option>
            <option value="France">France</option>
            <option value="Japan">Japan</option>
            <option value="China">China</option>
          </select>
        </div>
      </div>

      {/* Shipping Method */}
      <div>
        <h3 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
          <Truck className="h-4 w-4 text-primary mr-2" />
          Shipping Method
        </h3>
        <div className="space-y-3">
          <div
            className={`flex items-center p-3 border rounded-lg cursor-pointer transition-colors ${
              formData.shippingMethod === "standard"
                ? "border-primary bg-primary/5"
                : "border-gray-200 hover:border-gray-300"
            }`}
            onClick={() => handleShippingMethodChange("standard")}
          >
            <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 transition-colors ${
              formData.shippingMethod === "standard"
                ? "bg-primary text-white"
                : "bg-gray-100 text-gray-400"
            }`}>
              <Truck className="h-4 w-4" />
            </div>
            <input
              type="radio"
              id="standard-shipping"
              name="shippingMethod"
              value="standard"
              checked={formData.shippingMethod === "standard"}
              onChange={() => handleShippingMethodChange("standard")}
              className="hidden"
            />
            <label htmlFor="standard-shipping" className="flex flex-1 justify-between cursor-pointer">
              <div>
                <span className="block text-sm font-medium text-gray-900">Standard Shipping</span>
                <span className="block text-xs text-gray-500">3-5 business days</span>
              </div>
              <span className={`text-sm font-medium ${formData.shippingMethod === "standard" ? "text-primary" : "text-gray-900"}`}>
                {formData.country === "United States" && subtotal > 100 ? "Free" : "$4.99"}
              </span>
            </label>
          </div>

          <div
            className={`flex items-center p-3 border rounded-lg cursor-pointer transition-colors ${
              formData.shippingMethod === "express"
                ? "border-primary bg-primary/5"
                : "border-gray-200 hover:border-gray-300"
            }`}
            onClick={() => handleShippingMethodChange("express")}
          >
            <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 transition-colors ${
              formData.shippingMethod === "express"
                ? "bg-primary text-white"
                : "bg-gray-100 text-gray-400"
            }`}>
              <Zap className="h-4 w-4" />
            </div>
            <input
              type="radio"
              id="express-shipping"
              name="shippingMethod"
              value="express"
              checked={formData.shippingMethod === "express"}
              onChange={() => handleShippingMethodChange("express")}
              className="hidden"
            />
            <label htmlFor="express-shipping" className="flex flex-1 justify-between cursor-pointer">
              <div>
                <span className="block text-sm font-medium text-gray-900">Express Shipping</span>
                <span className="block text-xs text-gray-500">1-2 business days</span>
              </div>
              <span className={`text-sm font-medium ${formData.shippingMethod === "express" ? "text-primary" : "text-gray-900"}`}>
                $9.99
              </span>
            </label>
          </div>
        </div>
      </div>
    </div>
  );
}

// Mock data for component
const subtotal = 100;
