"use client";

import React from "react";
import { cn } from "@/lib/utils";
import { ContainerProvider } from "@/contexts/container-context";

interface ContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  fullWidth?: boolean;
}

export function Container({ children, className, fullWidth = false, ...props }: ContainerProps) {
  return (
    <div
      className={cn(
        fullWidth ? "w-full" : "max-w-[90%] w-full mx-auto px-1 sm:px-2 lg:px-3",
        className
      )}
      {...props}
    >
      <ContainerProvider isContainer={true}>
        {children}
      </ContainerProvider>
    </div>
  );
}
