import { PromoBanner } from "@/components/sections/promo-banner";
import { FeaturesSection } from "@/components/sections/features-section";
import { StoreByCategory } from "@/components/sections/store-by-category";
// import { PromoProducts } from "@/components/sections/promo-products";
import { BestSellerSection } from "@/components/sections/best-seller-section";
import { BannerSection } from "@/components/sections/banner-section";
import { RecommendedProducts } from "@/components/sections/recommended-products";
import { FeaturedProducts } from "@/components/sections/featured-products";
import { FeaturedStores } from "@/components/sections/featured-stores";
import { NewsletterSection } from "@/components/sections/newsletter-section";

export default function Home() {
  return (
    <>
      {/* Promotional Banner Section */}
      <PromoBanner backgroundColor="bg-[#F7F7F9]" />

      {/* Features Section */}
      <FeaturesSection backgroundColor="bg-white" />

      {/* Stores By Category */}
      <StoreByCategory backgroundColor="bg-[#F7F7F9]" />

      {/* Featured Products Section */}
      <FeaturedProducts
        title="Featured Products"
        shopMoreLink="/featured"
        backgroundColor="bg-white"
      />

      {/* Featured Stores Section */}
      <FeaturedStores
        title="Featured Stores"
        shopMoreLink="/stores"
        backgroundColor="bg-[#F7F7F9]"
      />

      {/* Best Seller Section */}
      <BestSellerSection
        title="Best Sellers"
        shopMoreLink="/best-sellers"
        backgroundColor="bg-white"
      />

      {/* Banner Section */}
      <BannerSection backgroundColor="bg-[#F7F7F9]" />

      {/* Recommended Products Section */}
      <RecommendedProducts
        title="Recommended For You"
        shopMoreLink="/recommended"
        backgroundColor="bg-white"
      />

      {/* Newsletter Section */}
      <NewsletterSection backgroundColor="bg-[#F7F7F9]" />
    </>
  );
}
