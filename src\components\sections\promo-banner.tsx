"use client";

import React, { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { Container } from "@/components/ui/container";
import { ArrowRight } from "lucide-react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  type CarouselApi
} from "@/components/ui/carousel";
import Autoplay from "embla-carousel-autoplay";
import { Card } from "@/components/ui/card";

type PromoBannerProps = {
  backgroundColor?: string;
};

// Define the carousel items
const carouselItems = [
  {
    id: 1,
    title: "Say Hello To The Future",
    category: "SMART TV!",
    discount: "60%",
    image: "/images/products/smart-tv-projector.png",
    link: "/shop/electronics/smart-tv",
    alt: "Smart TV Projector"
  },
  {
    id: 2,
    title: "Next-Gen Gaming Experience",
    category: "GAMING CONSOLE",
    discount: "40%",
    image: "/images/products/gaming-console.png",
    link: "/shop/electronics/gaming",
    alt: "Gaming Console"
  },
  {
    id: 3,
    title: "Premium Audio Quality",
    category: "HEADPHONES",
    discount: "30%",
    image: "/images/products/headphones.png",
    link: "/shop/electronics/audio",
    alt: "Premium Headphones"
  }
];

export const PromoBanner = ({ backgroundColor = "bg-white" }: PromoBannerProps) => {
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const [count, setCount] = useState(0);

  useEffect(() => {
    if (!api) {
      return;
    }

    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap());

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap());
    });
  }, [api]);

  return (
    <div className={`py-6 sm:py-8 ${backgroundColor} dark:bg-card`}>
      <Container>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
          {/* Main Promo Banner with Carousel */}
          <Card className="lg:col-span-2 overflow-hidden border border-gray-200 dark:border-border shadow-none rounded-xl relative">
            <Carousel
              setApi={setApi}
              opts={{ loop: true, duration: 30 }}
              plugins={[
                Autoplay({
                  delay: 5000,
                  stopOnInteraction: true,
                  stopOnMouseEnter: true,
                }),
              ]}
            >
              <CarouselContent>
                {carouselItems.map((item) => (
                  <CarouselItem key={item.id}>
                    <div className="flex flex-col md:flex-row items-center h-full">
                      <div className="p-6 md:p-8 md:w-1/2">
                        <div className="uppercase text-xs text-gray-400 dark:text-gray-300 tracking-wider mb-2">{item.category}</div>
                        <h2 className="text-2xl sm:text-3xl font-bold mb-2 text-gray-800 dark:text-gray-100">{item.title}</h2>
                        <p className="text-base mb-6 dark:text-gray-200">
                          Sale up to <span className="text-pink-500 dark:text-pink-400 font-semibold">{item.discount}</span> off
                        </p>
                        <Link
                          href={item.link}
                          className="inline-flex items-center justify-center btn-gradient-secondary px-6 py-2.5 rounded-full font-medium"
                        >
                          Shop Now <ArrowRight className="ml-2 h-4 w-4" />
                        </Link>
                      </div>
                      <div className="md:w-1/2 relative h-48 md:h-64 lg:h-72">
                        <div className="absolute inset-0 flex items-center justify-center">
                          <div className="relative w-full h-full">
                            <Image
                              src={item.image}
                              alt={item.alt}
                              fill
                              className="object-contain p-4"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </CarouselItem>
                ))}
              </CarouselContent>

              {/* Navigation buttons removed as requested */}
            </Carousel>

            {/* Indicator Dots with improved visibility */}
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex items-center gap-1.5">
              {Array.from({ length: count }).map((_, index) => {
                const isActive = index === current;

                return (
                  <button
                    key={index}
                    onClick={() => api?.scrollTo(index)}
                    aria-label={`Go to slide ${index + 1}`}
                    className="focus:outline-none"
                  >
                    <div className={`
                      carousel-dot
                      ${isActive ? 'carousel-dot-active' : ''}
                    `}></div>
                  </button>
                );
              })}
            </div>
          </Card>

          {/* Side Product Showcases */}
          <div className="lg:col-span-1 grid grid-cols-1 gap-4 sm:gap-6">
            {/* Product 1 - PlayStation */}
            <Card className="overflow-hidden border border-gray-200 dark:border-border shadow-none rounded-xl p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-medium dark:text-gray-100">Playstation 4</h3>
                  <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">game pro</h4>
                  <div className="mt-2">
                    <span className="text-xs text-gray-500 dark:text-gray-400">From</span>
                    <div className="text-lg font-semibold text-pink-500 dark:text-pink-400">29.99<span className="text-xs align-top">$</span></div>
                  </div>
                  <Link
                    href="/shop/electronics/gaming"
                    className="inline-flex items-center text-xs bg-gradient-soft-secondary text-secondary hover:bg-gradient-soft-accent hover:text-accent px-2 py-1 rounded-full mt-1 transition-all dark:text-secondary-foreground"
                  >
                    Shop now <ArrowRight className="ml-1 h-3 w-3" />
                  </Link>
                </div>
                <div className="relative w-24 h-24 sm:w-28 sm:h-28">
                  <Image
                    src="/images/products/playstation.png"
                    alt="PlayStation 4"
                    fill
                    className="object-contain"
                  />
                </div>
              </div>
            </Card>

            {/* Product 2 - Smartphone */}
            <Card className="overflow-hidden border border-gray-200 dark:border-border shadow-none rounded-xl p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-medium dark:text-gray-100">Smart phone</h3>
                  <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">mix 2</h4>
                  <div className="mt-2">
                    <span className="text-xs text-gray-500 dark:text-gray-400">From</span>
                    <div className="text-lg font-semibold text-pink-500 dark:text-pink-400">99.99<span className="text-xs align-top">$</span></div>
                  </div>
                  <Link
                    href="/shop/electronics/smartphones"
                    className="inline-flex items-center text-xs bg-gradient-soft-secondary text-secondary hover:bg-gradient-soft-accent hover:text-accent px-2 py-1 rounded-full mt-1 transition-all dark:text-secondary-foreground"
                  >
                    Shop now <ArrowRight className="ml-1 h-3 w-3" />
                  </Link>
                </div>
                <div className="relative w-24 h-24 sm:w-28 sm:h-28">
                  <Image
                    src="/images/products/smartphone.png"
                    alt="Smart Phone"
                    fill
                    className="object-contain"
                  />
                </div>
              </div>
            </Card>
          </div>
        </div>
      </Container>
    </div>
  );
}
