"use client"

import * as React from "react"
import { LogOut, ChevronRight } from "lucide-react"
import Link from "next/link"

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarRail,
  SidebarGroup,
  SidebarGroupLabel,
} from "@/components/ui/sidebar"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { DashboardNavData, NavItem, DashboardUserInfo } from "@/types/navigation"

/**
 * Props for the DashboardSidebar component
 */
export interface DashboardSidebarProps extends React.ComponentProps<typeof Sidebar> {
  /** Navigation data for the sidebar */
  navData: DashboardNavData;
  /** Text to display as the logo text */
  logoText: string;
  /** Initials to display in the logo */
  logoInitials: string;
  /** User information for the sidebar footer */
  userInfo: DashboardUserInfo;
}

/**
 * Shared dashboard sidebar component used by both SuperAdmin and Vendor layouts
 *
 * This component renders a sidebar with navigation items, logo, and user information.
 */
export function DashboardSidebar({
  navData,
  logoText,
  logoInitials,
  userInfo,
  ...props
}: DashboardSidebarProps) {
  const { displayName, email, avatarSrc, avatarFallback, logoutUrl } = userInfo;

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader className="border-b border-border p-4">
        <div className="flex items-center gap-2">
          <div className="bg-gradient-to-r from-primary to-secondary rounded-md p-1">
            <div className="text-white font-bold text-lg">{logoInitials}</div>
          </div>
          <div className="flex flex-col">
            <div className="font-semibold text-sm">{logoText}</div>
            <div className="text-xs text-muted-foreground">Dashboard</div>
          </div>
        </div>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>{navData.groupLabel}</SidebarGroupLabel>
          <SidebarMenu>
            {navData.mainNavItems.map((item) => (
              <NavMenuItem key={item.title} item={item} />
            ))}
          </SidebarMenu>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter className="border-t border-border p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Avatar className="h-8 w-8">
              <AvatarImage src={avatarSrc} alt={displayName} />
              <AvatarFallback>{avatarFallback}</AvatarFallback>
            </Avatar>
            <div>
              <div className="text-sm font-medium">{displayName}</div>
              <div className="text-xs text-muted-foreground">{email}</div>
            </div>
          </div>
          <Button variant="ghost" size="icon" asChild>
            <Link href={logoutUrl}>
              <LogOut className="h-4 w-4" />
              <span className="sr-only">Log out</span>
            </Link>
          </Button>
        </div>
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}

/**
 * Props for the NavMenuItem component
 */
interface NavMenuItemProps {
  /** The navigation item to render */
  item: NavItem;
}

/**
 * Renders a navigation menu item with optional sub-items
 *
 * If the item has sub-items, it renders a collapsible menu.
 * Otherwise, it renders a simple link.
 */
function NavMenuItem({ item }: NavMenuItemProps) {
  return (
    <Collapsible
      asChild
      defaultOpen={item.isActive}
      className="group/collapsible"
    >
      <SidebarMenuItem>
        {item.items ? (
          <>
            <CollapsibleTrigger asChild>
              <SidebarMenuButton tooltip={item.title}>
                {item.icon && <item.icon className="h-4 w-4" />}
                <span>{item.title}</span>
                <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
              </SidebarMenuButton>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <SidebarMenuSub>
                {item.items?.map((subItem) => (
                  <SidebarMenuSubItem key={subItem.title}>
                    <SidebarMenuSubButton asChild>
                      <Link href={subItem.url}>
                        <span>{subItem.title}</span>
                      </Link>
                    </SidebarMenuSubButton>
                  </SidebarMenuSubItem>
                ))}
              </SidebarMenuSub>
            </CollapsibleContent>
          </>
        ) : (
          <SidebarMenuButton asChild tooltip={item.title}>
            <Link href={item.url}>
              {item.icon && <item.icon className="h-4 w-4" />}
              <span>{item.title}</span>
            </Link>
          </SidebarMenuButton>
        )}
      </SidebarMenuItem>
    </Collapsible>
  );
}
