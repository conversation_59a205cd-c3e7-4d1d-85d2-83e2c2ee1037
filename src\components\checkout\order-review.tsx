import React from "react";
import Image from "next/image";
import { CreditCard, MapPin, Truck, ShoppingBag, CheckCircle, Mail, Phone, Wallet, Zap, Receipt } from "lucide-react";

interface OrderReviewProps {
  formData: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    address: string;
    apartment: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
    sameAsShipping: boolean;
    billingFirstName: string;
    billingLastName: string;
    billingAddress: string;
    billingApartment: string;
    billingCity: string;
    billingState: string;
    billingZipCode: string;
    billingCountry: string;
    paymentMethod: string;
    cardNumber: string;
    shippingMethod: string;
  };
  cartItems: Array<{
    id: number;
    name: string;
    price: number;
    originalPrice: number;
    quantity: number;
    image: string;
    vendor: string;
    color: string;
  }>;
  subtotal: number;
  shipping: number;
  tax: number;
  total: number;
}

export function OrderReview({ formData, cartItems, subtotal, shipping, tax, total }: OrderReviewProps) {
  // Get billing information based on whether it's the same as shipping
  const billingInfo = formData.sameAsShipping
    ? {
        firstName: formData.firstName,
        lastName: formData.lastName,
        address: formData.address,
        apartment: formData.apartment,
        city: formData.city,
        state: formData.state,
        zipCode: formData.zipCode,
        country: formData.country,
      }
    : {
        firstName: formData.billingFirstName,
        lastName: formData.billingLastName,
        address: formData.billingAddress,
        apartment: formData.billingApartment,
        city: formData.billingCity,
        state: formData.billingState,
        zipCode: formData.billingZipCode,
        country: formData.billingCountry,
      };

  // Format credit card number to show only last 4 digits
  const formatCardNumber = (cardNumber: string) => {
    if (!cardNumber) return "";
    const lastFourDigits = cardNumber.slice(-4);
    return `•••• •••• •••• ${lastFourDigits}`;
  };

  return (
    <div className="space-y-5">
      {/* Order Items */}
      <div>
        <h3 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
          <ShoppingBag className="h-4 w-4 text-primary mr-2" />
          Order Items
        </h3>
        <div className="space-y-3 max-h-[250px] overflow-y-auto pr-2">
          {cartItems.map((item) => (
            <div key={item.id} className="flex gap-3 pb-3 border-b border-gray-100 last:border-b-0 last:pb-0">
              <div className="relative w-16 h-16 rounded-md border border-gray-200 overflow-hidden flex-shrink-0 bg-[#F7F7F9]">
                <Image
                  src={item.image}
                  alt={item.name}
                  fill
                  className="object-cover"
                />
                <div className="absolute top-0 right-0 bg-primary text-white text-xs w-5 h-5 flex items-center justify-center rounded-bl-md">
                  {item.quantity}
                </div>
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-medium text-gray-900 truncate">{item.name}</h4>
                <p className="text-xs text-gray-500">{item.vendor} • {item.color}</p>
                <div className="flex justify-between items-center mt-1">
                  <span className="text-sm font-medium text-primary">${item.price.toFixed(2)}</span>
                  <span className="text-sm font-medium text-gray-900">${(item.price * item.quantity).toFixed(2)}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Shipping and Billing */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Shipping Information */}
        <div className="border border-gray-200 rounded-lg p-3">
          <div className="flex items-center mb-2">
            <MapPin className="h-4 w-4 text-primary mr-2" />
            <h3 className="text-sm font-medium text-gray-700">Shipping Address</h3>
          </div>
          <div className="text-sm text-gray-600">
            <p>{formData.firstName} {formData.lastName}</p>
            <p>{formData.address}{formData.apartment ? `, ${formData.apartment}` : ''}</p>
            <p>{formData.city}, {formData.state} {formData.zipCode}</p>
            <p>{formData.country}</p>
            <p className="text-xs text-gray-500 mt-1">{formData.email} • {formData.phone}</p>
          </div>
        </div>

        {/* Billing Information */}
        <div className="border border-gray-200 rounded-lg p-3">
          <div className="flex items-center mb-2">
            <MapPin className="h-4 w-4 text-primary mr-2" />
            <h3 className="text-sm font-medium text-gray-700">Billing Address</h3>
            {formData.sameAsShipping && (
              <span className="ml-auto text-xs bg-primary/10 text-primary px-2 py-0.5 rounded-full flex items-center">
                <CheckCircle className="h-3 w-3 mr-1" /> Same as shipping
              </span>
            )}
          </div>
          <div className="text-sm text-gray-600">
            <p>{billingInfo.firstName} {billingInfo.lastName}</p>
            <p>{billingInfo.address}{billingInfo.apartment ? `, ${billingInfo.apartment}` : ''}</p>
            <p>{billingInfo.city}, {billingInfo.state} {billingInfo.zipCode}</p>
            <p>{billingInfo.country}</p>
          </div>
        </div>
      </div>

      {/* Payment and Shipping Method */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Payment Method */}
        <div className="border border-gray-200 rounded-lg p-3">
          <div className="flex items-center mb-2">
            <CreditCard className="h-4 w-4 text-primary mr-2" />
            <h3 className="text-sm font-medium text-gray-700">Payment Method</h3>
          </div>
          <div className="flex items-center">
            <div className={`w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-3`}>
              {formData.paymentMethod === "credit-card" ? (
                <CreditCard className="h-4 w-4" />
              ) : (
                <Wallet className="h-4 w-4" />
              )}
            </div>
            <div className="text-sm text-gray-600">
              {formData.paymentMethod === "credit-card" ? (
                <>
                  <p className="font-medium">Credit Card</p>
                  <p>{formatCardNumber(formData.cardNumber)}</p>
                </>
              ) : (
                <>
                  <p className="font-medium">PayPal</p>
                  <p>Pay with your PayPal account</p>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Shipping Method */}
        <div className="border border-gray-200 rounded-lg p-3">
          <div className="flex items-center mb-2">
            <Truck className="h-4 w-4 text-primary mr-2" />
            <h3 className="text-sm font-medium text-gray-700">Shipping Method</h3>
          </div>
          <div className="flex items-center">
            <div className={`w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-3`}>
              {formData.shippingMethod === "standard" ? (
                <Truck className="h-4 w-4" />
              ) : (
                <Zap className="h-4 w-4" />
              )}
            </div>
            <div className="text-sm text-gray-600">
              {formData.shippingMethod === "standard" ? (
                <>
                  <p className="font-medium">Standard Shipping</p>
                  <p>3-5 business days • {shipping === 0 ? "Free" : `$${shipping.toFixed(2)}`}</p>
                </>
              ) : (
                <>
                  <p className="font-medium">Express Shipping</p>
                  <p>1-2 business days • ${shipping.toFixed(2)}</p>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Order Summary */}
      <div className="border border-gray-200 rounded-lg p-3">
        <h3 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
          <Receipt className="h-4 w-4 text-primary mr-2" />
          Order Summary
        </h3>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600">Subtotal</span>
            <span className="font-medium">${subtotal.toFixed(2)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Shipping</span>
            <span className="font-medium">
              {shipping === 0 ? (
                <span className="text-green-600">Free</span>
              ) : (
                `$${shipping.toFixed(2)}`
              )}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Estimated Tax</span>
            <span className="font-medium">${tax.toFixed(2)}</span>
          </div>
          <div className="border-t border-gray-100 my-2 pt-2"></div>
          <div className="flex justify-between font-medium">
            <span className="text-gray-900">Total</span>
            <span className="text-gray-900 text-lg">${total.toFixed(2)}</span>
          </div>
        </div>
      </div>
    </div>
  );
}
