"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { Container } from "@/components/ui/container";
import { ArrowRight, Store, ChevronLeft, ChevronRight } from "lucide-react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  type CarouselApi
} from "@/components/ui/carousel";
import { Card, CardContent } from "@/components/ui/card";

// Store type definition
type Store = {
  id: string;
  name: string;
  slug: string;
  image: string;
  rating: number;
  productCount: number;
  categories: string[];
  discount?: string;
  logo?: string;
};

// Featured stores data
const featuredStoresData: Store[] = [
  {
    id: "1",
    name: "Organic Delights",
    slug: "organic-delights",
    image: "/images/stores/berry.png",
    rating: 4.8,
    productCount: 347,
    categories: ["Organic", "Groceries", "Health Foods"]
  },
  {
    id: "2",
    name: "Tech Haven",
    slug: "tech-haven",
    image: "/images/stores/headphone.png",
    rating: 4.6,
    productCount: 165,
    categories: ["Electronics", "Gadgets", "Smart Home"],
    logo: "/images/categories/store-placeholder.png"
  },
  {
    id: "3",
    name: "Style Avenue",
    slug: "style-avenue",
    image: "/images/stores/berry.png",
    rating: 4.7,
    productCount: 583,
    categories: ["Fashion", "Apparel", "Accessories"],
    logo: "/images/categories/store-placeholder.png"
  },
  {
    id: "4",
    name: "Home Essentials",
    slug: "home-essentials",
    image: "/images/stores/headphone.png",
    rating: 4.5,
    productCount: 429,
    categories: ["Home & Kitchen", "Furniture", "Decor"],
    discount: "50%",
    logo: "/images/categories/store-placeholder.png"
  },
  {
    id: "5",
    name: "Fitness World",
    slug: "fitness-world",
    image: "/images/stores/berry.png",
    rating: 4.9,
    productCount: 278,
    categories: ["Fitness", "Sports", "Outdoor"],
    logo: "/images/categories/store-placeholder.png"
  },
  {
    id: "6",
    name: "Beauty Boutique",
    slug: "beauty-boutique",
    image: "/images/stores/headphone.png",
    rating: 4.7,
    productCount: 412,
    categories: ["Beauty", "Skincare", "Makeup"],
    discount: "30%",
    logo: "/images/categories/store-placeholder.png"
  },
  {
    id: "7",
    name: "Pet Paradise",
    slug: "pet-paradise",
    image: "/images/stores/berry.png",
    rating: 4.8,
    productCount: 195,
    categories: ["Pets", "Pet Food", "Pet Accessories"],
    logo: "/images/categories/store-placeholder.png"
  },
  {
    id: "8",
    name: "Book Corner",
    slug: "book-corner",
    image: "/images/stores/headphone.png",
    rating: 4.6,
    productCount: 623,
    categories: ["Books", "Stationery", "Gifts"],
    logo: "/images/categories/store-placeholder.png"
  },
  {
    id: "9",
    name: "Gourmet Foods",
    slug: "gourmet-foods",
    image: "/images/stores/berry.png",
    rating: 4.9,
    productCount: 312,
    categories: ["Gourmet", "Specialty Foods", "Imported"],
    logo: "/images/categories/store-placeholder.png"
  },
  {
    id: "10",
    name: "Outdoor Adventure",
    slug: "outdoor-adventure",
    image: "/images/stores/headphone.png",
    rating: 4.7,
    productCount: 245,
    categories: ["Outdoor", "Camping", "Hiking"],
    discount: "25%",
    logo: "/images/categories/store-placeholder.png"
  }
];

type FeaturedStoresProps = {
  title?: string;
  stores?: Store[];
  shopMoreLink?: string;
  backgroundColor?: string;
};

export const FeaturedStores = ({
  title = "Featured Stores",
  stores = featuredStoresData,
  shopMoreLink = "/stores",
  backgroundColor = "bg-[#F7F7F9]"
}: FeaturedStoresProps) => {
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const [count, setCount] = useState(0);

  useEffect(() => {
    if (!api) {
      return;
    }

    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap());

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap());
    });
  }, [api]);
  return (
    <section className={`pt-12 pb-8 ${backgroundColor} dark:bg-card`}>
      <Container>
        <div className="flex flex-col space-y-6">
          {/* Header with title and shop more link - Desktop */}
          <div className="hidden sm:block">
            <div className="flex items-center w-full relative">
              {/* Horizontal line that spans the entire width */}
              <div className="absolute h-px bg-gray-200 dark:bg-gray-700 w-full"></div>

              {/* Title with icon - positioned on top of the line */}
              <div className={`flex-shrink-0 flex items-center ${backgroundColor} dark:bg-card rounded-full p-1 z-10 mr-4`}>
                <div className="flex items-center gap-2 px-3">
                  <div className="w-6 h-6 flex items-center justify-center text-primary dark:text-accent">
                    <Store size={20} />
                  </div>
                  <h2 className="text-lg font-bold text-primary dark:text-accent">{title}</h2>
                </div>
              </div>

              {/* Empty flex-grow div to push the Shop More button to the right */}
              <div className="flex-grow"></div>

              {/* Shop More button - positioned on top of the line */}
              <div className="flex-shrink-0 z-10">
                <Link
                  href={shopMoreLink}
                  className={`flex ${backgroundColor} dark:bg-card hover:bg-gradient-primary hover:text-white text-primary px-3 sm:px-4 py-2 rounded-full items-center gap-1 whitespace-nowrap transition-all text-xs sm:text-sm border border-gray-200 dark:border-border dark:text-accent`}
                >
                  Shop More <ArrowRight className="h-3 w-3 sm:h-4 sm:w-4" />
                </Link>
              </div>
            </div>
          </div>

          {/* Header with title and shop more link - Mobile */}
          <div className="sm:hidden">
            <div className="flex items-center w-full relative">
              {/* Horizontal line that spans the entire width */}
              <div className="absolute h-px bg-gray-200 dark:bg-gray-700 w-full"></div>

              {/* Title with icon - positioned on top of the line */}
              <div className={`flex-shrink-0 flex items-center ${backgroundColor} dark:bg-card rounded-full p-1 z-10 mr-4`}>
                <div className="flex items-center gap-2 px-2">
                  <div className="w-5 h-5 flex items-center justify-center text-primary">
                    <Store size={16} />
                  </div>
                  <h2 className="text-base font-bold text-primary">{title}</h2>
                </div>
              </div>

              {/* Empty flex-grow div to push the Shop More button to the right */}
              <div className="flex-grow"></div>

              {/* Shop More button - positioned on top of the line */}
              <div className="flex-shrink-0 z-10">
                <Link
                  href={shopMoreLink}
                  className={`flex ${backgroundColor} dark:bg-card hover:bg-gradient-primary hover:text-white text-primary px-3 sm:px-4 py-2 rounded-full items-center gap-1 whitespace-nowrap transition-all text-xs sm:text-sm border border-gray-200 dark:border-border`}
                >
                  Shop More <ArrowRight className="h-3 w-3" />
                </Link>
              </div>
            </div>
          </div>

          {/* Stores Carousel */}
          <div className="mt-6 relative z-0 px-3">
            <Carousel
              setApi={setApi}
              className="w-full"
              opts={{
                align: "start",
                loop: true,
                slidesToScroll: 1,
              }}
            >
              <CarouselContent>
                {stores.map((store) => (
                  <CarouselItem key={store.id} className="sm:basis-1/2 md:basis-1/3 lg:basis-1/4 xl:basis-1/5">
                    <Link href={`/store/${store.slug}`} className="block group">
                      <Card className="relative overflow-hidden border border-gray-200 dark:border-border hover:border-gray-300 dark:hover:border-gray-600 transition-all duration-300 h-[280px] group-hover:translate-y-[-2px] p-0 rounded-lg shadow-none">
                        {/* Store Banner Image */}
                        <div className="relative h-[140px] w-full overflow-hidden border-b border-gray-100 dark:border-gray-700">
                          {/* Featured Badge */}
                          <div className="absolute top-2 left-2 z-10 bg-gradient-primary text-white text-xs font-medium px-2 py-0.5 rounded-full">
                            Featured
                          </div>

                          {/* Discount Badge (if applicable) */}
                          {store.discount && (
                            <div className="absolute top-2 right-2 z-10 bg-gradient-secondary text-white text-xs font-medium px-2 py-0.5 rounded-full">
                              {store.discount}
                            </div>
                          )}

                          {/* Store Image */}
                          <Image
                            src={store.image}
                            alt={store.name}
                            fill
                            className="object-cover"
                          />

                          {/* Store Logo Circle */}
                          <div className="absolute top-[110px] left-4 z-20">
                            <div className="w-12 h-12 rounded-full border-2 border-white dark:border-gray-800 bg-white dark:bg-gray-800 shadow-md overflow-hidden">
                              <Image
                                src={store.logo || "/images/categories/store-placeholder.png"}
                                alt={`${store.name} logo`}
                                width={48}
                                height={48}
                                className="object-cover"
                              />
                            </div>
                          </div>
                        </div>

                        {/* Store Info */}
                        <CardContent className="mt-4 px-3 pb-3 min-h-[120px] bg-white dark:bg-card gap-0">
                          <h3 className="font-medium text-base dark:text-gray-100">{store.name}</h3>

                          {/* Rating Stars */}
                          <div className="flex items-center mt-1">
                            <div className="flex">
                              {Array.from({ length: 5 }).map((_, i) => (
                                <span key={i} className={`${i < Math.floor(store.rating) ? "text-yellow-400" : "text-gray-200 dark:text-gray-600"} text-xs`}>★</span>
                              ))}
                            </div>
                            <span className="text-xs text-gray-500 dark:text-gray-400 ml-1">{store.rating} ({store.productCount} products)</span>
                          </div>

                          {/* Categories */}
                          <div className="flex flex-wrap gap-1 mt-2">
                            {store.categories.map((category, index) => (
                              <span
                                key={index}
                                className="text-xs bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 px-2 py-0.5 rounded-md"
                              >
                                {category}
                              </span>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    </Link>
                  </CarouselItem>
                ))}
              </CarouselContent>
            </Carousel>

            {/* Custom Navigation Buttons positioned on the border of first and last cards */}
            <div className="absolute top-1/2 left-0 right-0 transform -translate-y-1/2 flex justify-between pointer-events-none w-full">
              <button
                onClick={() => api?.scrollPrev()}
                className="p-2.5 rounded-full bg-white/40 dark:bg-card/40 backdrop-blur-sm border border-gray-200/30 dark:border-gray-700/30 hover:bg-gradient-primary hover:text-white hover:border-primary/20 transition-all shadow-sm pointer-events-auto -ml-3"
                disabled={!api?.canScrollPrev()}
              >
                <ChevronLeft className="h-5 w-5 text-gray-800 dark:text-gray-200" />
              </button>
              <button
                onClick={() => api?.scrollNext()}
                className="p-2.5 rounded-full bg-white/40 dark:bg-card/40 backdrop-blur-sm border border-gray-200/30 dark:border-gray-700/30 hover:bg-gradient-primary hover:text-white hover:border-primary/20 transition-all shadow-sm pointer-events-auto -mr-3"
                disabled={!api?.canScrollNext()}
              >
                <ChevronRight className="h-5 w-5 text-gray-800 dark:text-gray-200" />
              </button>
            </div>

            {/* Indicator Dots with improved visibility */}
            <div className="flex justify-center mt-4">
              <div className="flex items-center gap-1.5">
                {Array.from({ length: count }).map((_, index) => {
                  const isActive = index === current;

                  return (
                    <button
                      key={index}
                      onClick={() => api?.scrollTo(index)}
                      aria-label={`Go to slide ${index + 1}`}
                      className="focus:outline-none"
                    >
                      <div className={`
                        carousel-dot
                        ${isActive ? 'carousel-dot-active' : ''}
                      `}></div>
                    </button>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      </Container>
    </section>
  );
};
