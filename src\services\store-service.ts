/**
 * Store service for handling store data
 */

import { Store, StoreCategory } from '@/types';
import { 
  Palette, Car, BookOpen, Cpu, Shirt, Smartphone, 
  Gamepad, Gift, Apple, Stethoscope, Home, Laptop, 
  TabletSmartphone, Music, Watch, Dumbbell, PenTool, Wrench 
} from 'lucide-react';

// Sample stores data
const stores: Store[] = [
  {
    id: "1",
    name: "Organic Delights",
    slug: "organic-delights",
    image: "/images/stores/berry.png",
    rating: 4.8,
    productCount: 347,
    categories: ["Organic", "Groceries", "Health Foods"],
    logo: "/images/categories/store-placeholder.png",
    description: "Organic Delights is your one-stop shop for all things organic. We offer a wide range of certified organic products, from fresh produce to pantry staples, all sourced from trusted local and international farmers who share our commitment to sustainable and ethical farming practices.",
    address: "123 Green Street, Organic Valley, CA 94123",
    phone: "+****************",
    email: "<EMAIL>",
    website: "www.organicdelights.com",
    openingHours: "Mon-Sat: 9:00 AM - 8:00 PM, Sun: 10:00 AM - 6:00 PM",
    socialLinks: {
      facebook: "https://facebook.com/organicdelights",
      instagram: "https://instagram.com/organicdelights",
      twitter: "https://twitter.com/organicdelights",
      linkedin: "https://linkedin.com/company/organicdelights"
    },
    joinedDate: "January 2020"
  },
  {
    id: "2",
    name: "Tech Haven",
    slug: "tech-haven",
    image: "/images/stores/headphone.png",
    rating: 4.6,
    productCount: 165,
    categories: ["Electronics", "Gadgets", "Smart Home"],
    logo: "/images/categories/store-placeholder.png",
    description: "Tech Haven is your destination for cutting-edge technology and gadgets. We curate the best tech products from around the world, focusing on innovation, quality, and user experience. From smartphones to smart home devices, we've got all your tech needs covered.",
    address: "456 Innovation Drive, Tech City, CA 95123",
    phone: "+****************",
    email: "<EMAIL>",
    website: "www.techhaven.com",
    openingHours: "Mon-Fri: 10:00 AM - 9:00 PM, Sat-Sun: 11:00 AM - 7:00 PM",
    socialLinks: {
      facebook: "https://facebook.com/techhaven",
      instagram: "https://instagram.com/techhaven",
      twitter: "https://twitter.com/techhaven",
      linkedin: "https://linkedin.com/company/techhaven"
    },
    joinedDate: "March 2019"
  },
  {
    id: "3",
    name: "Style Avenue",
    slug: "style-avenue",
    image: "/images/stores/berry.png",
    rating: 4.7,
    productCount: 583,
    categories: ["Fashion", "Apparel", "Accessories"],
    logo: "/images/categories/store-placeholder.png",
    description: "Style Avenue brings you the latest fashion trends from around the world. Our carefully curated collection includes clothing, accessories, and footwear for all occasions. We believe that fashion is a form of self-expression, and we're here to help you express yourself in style.",
    address: "789 Fashion Boulevard, Style District, NY 10001",
    phone: "+****************",
    email: "<EMAIL>",
    website: "www.styleavenue.com",
    openingHours: "Mon-Sun: 10:00 AM - 9:00 PM",
    socialLinks: {
      facebook: "https://facebook.com/styleavenue",
      instagram: "https://instagram.com/styleavenue",
      twitter: "https://twitter.com/styleavenue",
      linkedin: "https://linkedin.com/company/styleavenue"
    },
    joinedDate: "June 2018"
  }
];

// Store categories
export const storeCategories: StoreCategory[] = [
  {
    name: "All Stores",
    slug: "all",
    icon: Palette
  },
  {
    name: "Automotive",
    slug: "automotive",
    icon: Car
  },
  {
    name: "Books",
    slug: "books",
    icon: BookOpen
  },
  {
    name: "Electronics",
    slug: "electronics",
    icon: Cpu
  },
  {
    name: "Fashion",
    slug: "fashion",
    icon: Shirt
  },
  {
    name: "Mobile",
    slug: "mobile",
    icon: TabletSmartphone
  },
  {
    name: "Music",
    slug: "music",
    icon: Music
  },
  {
    name: "Watches",
    slug: "watches",
    icon: Watch
  },
  {
    name: "Fitness",
    slug: "fitness",
    icon: Dumbbell
  },
  {
    name: "Stationery",
    slug: "stationery",
    icon: PenTool
  },
  {
    name: "Tools",
    slug: "tools",
    icon: Wrench
  }
];

/**
 * Get all stores
 */
export const getAllStores = (): Store[] => {
  return stores;
};

/**
 * Get store by slug
 */
export const getStoreBySlug = (slug: string): Store | undefined => {
  return stores.find(store => store.slug === slug);
};

/**
 * Get store by ID
 */
export const getStoreById = (id: string): Store | undefined => {
  return stores.find(store => store.id === id);
};

/**
 * Get featured stores
 */
export const getFeaturedStores = (limit: number = 3): Store[] => {
  return stores.slice(0, limit);
};

/**
 * Filter stores based on criteria
 */
export const filterStores = (
  filters: {
    categories?: string[];
    ratings?: number | null;
    sortBy?: string;
  }
): Store[] => {
  let filteredStores = [...stores];

  // Filter by categories
  if (filters.categories && filters.categories.length > 0) {
    filteredStores = filteredStores.filter(store => 
      store.categories.some(category => 
        filters.categories?.includes(category)
      )
    );
  }

  // Filter by ratings
  if (filters.ratings) {
    filteredStores = filteredStores.filter(
      store => store.rating >= (filters.ratings || 0)
    );
  }

  // Sort stores
  if (filters.sortBy) {
    switch (filters.sortBy) {
      case 'name_asc':
        filteredStores.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case 'name_desc':
        filteredStores.sort((a, b) => b.name.localeCompare(a.name));
        break;
      case 'rating':
        filteredStores.sort((a, b) => b.rating - a.rating);
        break;
      case 'products':
        filteredStores.sort((a, b) => b.productCount - a.productCount);
        break;
      default: // popularity (rating)
        filteredStores.sort((a, b) => b.rating - a.rating);
    }
  }

  return filteredStores;
};
