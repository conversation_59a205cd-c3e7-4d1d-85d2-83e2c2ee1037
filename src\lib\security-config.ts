/**
 * Security configuration and utilities
 */

/**
 * Security configuration constants
 */
export const SECURITY_CONFIG = {
  // Session management
  SESSION_TIMEOUT_MINUTES: 30,
  SESSION_WARNING_MINUTES: 5,
  
  // Rate limiting
  LOGIN_MAX_ATTEMPTS: 5,
  <PERSON><PERSON><PERSON><PERSON>_WINDOW_MINUTES: 15,
  <PERSON><PERSON><PERSON><PERSON>ATION_MAX_ATTEMPTS: 3,
  REGIS<PERSON><PERSON>ION_WINDOW_MINUTES: 60,
  
  // Password requirements
  PASSWORD_MIN_LENGTH: 8,
  PASSWORD_MAX_LENGTH: 128,
  PASSWORD_REQUIRE_UPPERCASE: true,
  PASSWORD_REQUIRE_LOWERCASE: true,
  PASSWORD_REQUIRE_NUMBERS: true,
  PASSWORD_REQUIRE_SPECIAL_CHARS: true,
  
  // Token settings
  ACCESS_TOKEN_EXPIRY_HOURS: 24,
  REFRESH_TOKEN_EXPIRY_DAYS: 7,
  
  // Security headers
  SECURITY_HEADERS: {
    'X-Frame-Options': 'DENY',
    'X-Content-Type-Options': 'nosniff',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'X-XSS-Protection': '1; mode=block',
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
    'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; frame-ancestors 'none';",
  },
  
  // CSRF protection
  CSRF_TOKEN_LENGTH: 32,
  CSRF_TOKEN_EXPIRY_MINUTES: 60,
  
  // Input validation
  MAX_INPUT_LENGTH: 1000,
  MAX_EMAIL_LENGTH: 254,
  MAX_NAME_LENGTH: 50,
  MAX_PHONE_LENGTH: 20,
} as const;

/**
 * Security utility functions
 */
export class SecurityUtils {
  /**
   * Generate secure random string
   */
  static generateSecureRandom(length: number = 32): string {
    const array = new Uint8Array(length);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Generate secure password
   */
  static generateSecurePassword(length: number = 16): string {
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '0123456789';
    const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';
    
    const allChars = lowercase + uppercase + numbers + symbols;
    let password = '';
    
    // Ensure at least one character from each category
    password += lowercase[Math.floor(Math.random() * lowercase.length)];
    password += uppercase[Math.floor(Math.random() * uppercase.length)];
    password += numbers[Math.floor(Math.random() * numbers.length)];
    password += symbols[Math.floor(Math.random() * symbols.length)];
    
    // Fill the rest randomly
    for (let i = 4; i < length; i++) {
      password += allChars[Math.floor(Math.random() * allChars.length)];
    }
    
    // Shuffle the password
    return password.split('').sort(() => Math.random() - 0.5).join('');
  }

  /**
   * Check password strength
   */
  static checkPasswordStrength(password: string): {
    score: number;
    feedback: string[];
    isStrong: boolean;
  } {
    const feedback: string[] = [];
    let score = 0;

    // Length check
    if (password.length >= 8) score += 1;
    else feedback.push('Password should be at least 8 characters long');

    if (password.length >= 12) score += 1;

    // Character variety checks
    if (/[a-z]/.test(password)) score += 1;
    else feedback.push('Add lowercase letters');

    if (/[A-Z]/.test(password)) score += 1;
    else feedback.push('Add uppercase letters');

    if (/[0-9]/.test(password)) score += 1;
    else feedback.push('Add numbers');

    if (/[^a-zA-Z0-9]/.test(password)) score += 1;
    else feedback.push('Add special characters');

    // Common patterns check
    if (!/(.)\1{2,}/.test(password)) score += 1;
    else feedback.push('Avoid repeating characters');

    // Dictionary check (basic)
    const commonPasswords = [
      'password', '123456', '123456789', 'qwerty', 'abc123',
      'password123', 'admin', 'letmein', 'welcome', 'monkey'
    ];
    
    if (!commonPasswords.includes(password.toLowerCase())) score += 1;
    else feedback.push('Avoid common passwords');

    return {
      score,
      feedback,
      isStrong: score >= 6
    };
  }

  /**
   * Validate email format
   */
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email) && email.length <= SECURITY_CONFIG.MAX_EMAIL_LENGTH;
  }

  /**
   * Sanitize filename
   */
  static sanitizeFilename(filename: string): string {
    return filename
      .replace(/[^a-zA-Z0-9.-]/g, '_')
      .replace(/_{2,}/g, '_')
      .replace(/^_+|_+$/g, '');
  }

  /**
   * Check if URL is safe for redirects
   */
  static isSafeRedirectUrl(url: string, allowedDomains: string[] = []): boolean {
    try {
      const parsedUrl = new URL(url, window.location.origin);
      
      // Only allow same origin or explicitly allowed domains
      if (parsedUrl.origin === window.location.origin) {
        return true;
      }
      
      return allowedDomains.some(domain => 
        parsedUrl.hostname === domain || parsedUrl.hostname.endsWith('.' + domain)
      );
    } catch {
      return false;
    }
  }

  /**
   * Generate Content Security Policy
   */
  static generateCSP(options: {
    allowInlineStyles?: boolean;
    allowInlineScripts?: boolean;
    allowEval?: boolean;
    additionalDomains?: string[];
  } = {}): string {
    const {
      allowInlineStyles = false,
      allowInlineScripts = false,
      allowEval = false,
      additionalDomains = []
    } = options;

    let csp = "default-src 'self'";
    
    // Script sources
    let scriptSrc = "script-src 'self'";
    if (allowInlineScripts) scriptSrc += " 'unsafe-inline'";
    if (allowEval) scriptSrc += " 'unsafe-eval'";
    csp += `; ${scriptSrc}`;
    
    // Style sources
    let styleSrc = "style-src 'self'";
    if (allowInlineStyles) styleSrc += " 'unsafe-inline'";
    csp += `; ${styleSrc}`;
    
    // Other sources
    csp += "; img-src 'self' data: https:";
    csp += "; font-src 'self' data:";
    
    let connectSrc = "connect-src 'self'";
    if (additionalDomains.length > 0) {
      connectSrc += ` ${additionalDomains.join(' ')}`;
    }
    csp += `; ${connectSrc}`;
    
    csp += "; frame-ancestors 'none'";
    csp += "; base-uri 'self'";
    csp += "; form-action 'self'";
    
    return csp;
  }
}

/**
 * Security event logger
 */
export class SecurityLogger {
  private static logs: Array<{
    timestamp: Date;
    event: string;
    details: any;
    severity: 'low' | 'medium' | 'high' | 'critical';
  }> = [];

  static log(
    event: string, 
    details: any = {}, 
    severity: 'low' | 'medium' | 'high' | 'critical' = 'medium'
  ): void {
    const logEntry = {
      timestamp: new Date(),
      event,
      details,
      severity
    };

    this.logs.push(logEntry);
    
    // Keep only last 1000 logs
    if (this.logs.length > 1000) {
      this.logs = this.logs.slice(-1000);
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`[SECURITY ${severity.toUpperCase()}]`, event, details);
    }

    // In production, you might want to send critical events to a monitoring service
    if (severity === 'critical' && process.env.NODE_ENV === 'production') {
      // Send to monitoring service
      this.sendToMonitoring(logEntry);
    }
  }

  private static sendToMonitoring(logEntry: any): void {
    // Implement monitoring service integration
    // Example: send to Sentry, DataDog, etc.
    console.error('[CRITICAL SECURITY EVENT]', logEntry);
  }

  static getLogs(severity?: 'low' | 'medium' | 'high' | 'critical'): typeof this.logs {
    if (severity) {
      return this.logs.filter(log => log.severity === severity);
    }
    return [...this.logs];
  }

  static clearLogs(): void {
    this.logs = [];
  }
}

/**
 * Security monitoring hooks
 */
export function useSecurityMonitoring() {
  const logSecurityEvent = (
    event: string, 
    details: any = {}, 
    severity: 'low' | 'medium' | 'high' | 'critical' = 'medium'
  ) => {
    SecurityLogger.log(event, details, severity);
  };

  return { logSecurityEvent };
}
