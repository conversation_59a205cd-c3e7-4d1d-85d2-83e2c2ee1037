import { useState, useCallback } from 'react';
import { AuthService } from '@/services/auth-service';
import type { LoginRequest, RegisterRequest, User, UserType } from '@/types/auth';

/**
 * Authentication hook for handling login, register, and auth state
 */
export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * Login user
   */
  const login = useCallback(async (credentials: LoginRequest, userType: UserType = 'customer') => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await AuthService.login(credentials, userType);
      setUser(response.user);
      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Login failed';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Register user
   */
  const register = useCallback(async (data: RegisterRequest) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await AuthService.register(data);
      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Registration failed';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Logout user
   */
  const logout = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      await AuthService.logout();
      setUser(null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Logout failed';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Get current user
   */
  const getCurrentUser = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const userData = await AuthService.getCurrentUser();
      setUser(userData as User);
      return userData as User;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get user data';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Refresh token
   */
  const refreshToken = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await AuthService.refreshToken();
      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Token refresh failed';
      setError(errorMessage);
      // Clear user on refresh failure
      setUser(null);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Update user data
   */
  const updateUser = useCallback((userData: Partial<User>) => {
    setUser(prev => prev ? { ...prev, ...userData } : null);
  }, []);

  /**
   * Check if user is authenticated
   */
  const isAuthenticated = AuthService.isAuthenticated();

  return {
    // State
    user,
    isLoading,
    error,
    isAuthenticated,

    // Actions
    login,
    register,
    logout,
    getCurrentUser,
    refreshToken,
    updateUser,
    clearError,
  };
}

/**
 * Hook specifically for superadmin authentication
 */
export function useSuperadminAuth() {
  const auth = useAuth();

  const loginSuperadmin = useCallback(async (credentials: LoginRequest) => {
    return auth.login(credentials, 'superadmin');
  }, [auth]);

  return {
    ...auth,
    login: loginSuperadmin,
  };
}

/**
 * Hook specifically for vendor authentication
 */
export function useVendorAuth() {
  const auth = useAuth();

  const loginVendor = useCallback(async (credentials: LoginRequest) => {
    return auth.login(credentials, 'vendor');
  }, [auth]);

  const registerVendor = useCallback(async (data: RegisterRequest) => {
    return auth.register({ ...data, userType: 'vendor' });
  }, [auth]);

  return {
    ...auth,
    login: loginVendor,
    register: registerVendor,
  };
}

/**
 * Hook specifically for customer authentication
 */
export function useCustomerAuth() {
  const auth = useAuth();

  const loginCustomer = useCallback(async (credentials: LoginRequest) => {
    return auth.login(credentials, 'customer');
  }, [auth]);

  const registerCustomer = useCallback(async (data: RegisterRequest) => {
    return auth.register({ ...data, userType: 'customer' });
  }, [auth]);

  return {
    ...auth,
    login: loginCustomer,
    register: registerCustomer,
  };
}
