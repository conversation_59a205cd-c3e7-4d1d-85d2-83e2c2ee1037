import React from "react";
import { Input } from "@/components/ui/input";
import {
  CreditCard,
  Wallet,
  User,
  Home,
  Building,
  MapPin,
  Globe,
  Calendar,
  Lock,
  CheckCircle,
  ChevronDown
} from "lucide-react";

interface PaymentFormProps {
  formData: {
    sameAsShipping: boolean;
    billingFirstName: string;
    billingLastName: string;
    billingAddress: string;
    billingApartment: string;
    billingCity: string;
    billingState: string;
    billingZipCode: string;
    billingCountry: string;
    paymentMethod: string;
    cardNumber: string;
    cardName: string;
    expiryDate: string;
    cvv: string;
  };
  shippingData: {
    firstName: string;
    lastName: string;
    address: string;
    apartment: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
  handlePaymentMethodChange: (method: string) => void;
}

export function PaymentForm({
  formData,
  shippingData,
  handleInputChange,
  handlePaymentMethodChange
}: PaymentFormProps) {
  return (
    <div className="space-y-5">
      {/* Billing Address */}
      <div>
        <h3 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
          <Home className="h-4 w-4 text-primary mr-2" />
          Billing Address
        </h3>

        <div className="mb-3">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="sameAsShipping"
              name="sameAsShipping"
              checked={formData.sameAsShipping}
              onChange={handleInputChange}
              className="h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary"
            />
            <label htmlFor="sameAsShipping" className="ml-2 block text-sm text-gray-700">
              Same as shipping address
            </label>
          </div>
        </div>

        {!formData.sameAsShipping && (
          <div className="space-y-3">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div>
                <Input
                  type="text"
                  id="billingFirstName"
                  name="billingFirstName"
                  value={formData.billingFirstName}
                  onChange={handleInputChange}
                  className="w-full"
                  placeholder="First Name *"
                  required={!formData.sameAsShipping}
                />
              </div>
              <div>
                <Input
                  type="text"
                  id="billingLastName"
                  name="billingLastName"
                  value={formData.billingLastName}
                  onChange={handleInputChange}
                  className="w-full"
                  placeholder="Last Name *"
                  required={!formData.sameAsShipping}
                />
              </div>
            </div>

            <div>
              <Input
                type="text"
                id="billingAddress"
                name="billingAddress"
                value={formData.billingAddress}
                onChange={handleInputChange}
                className="w-full"
                placeholder="Street Address *"
                required={!formData.sameAsShipping}
              />
            </div>

            <div>
              <Input
                type="text"
                id="billingApartment"
                name="billingApartment"
                value={formData.billingApartment}
                onChange={handleInputChange}
                className="w-full"
                placeholder="Apartment, Suite, etc. (optional)"
              />
            </div>

            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              <div>
                <Input
                  type="text"
                  id="billingCity"
                  name="billingCity"
                  value={formData.billingCity}
                  onChange={handleInputChange}
                  className="w-full"
                  placeholder="City *"
                  required={!formData.sameAsShipping}
                />
              </div>
              <div>
                <Input
                  type="text"
                  id="billingState"
                  name="billingState"
                  value={formData.billingState}
                  onChange={handleInputChange}
                  className="w-full"
                  placeholder="State/Province *"
                  required={!formData.sameAsShipping}
                />
              </div>
              <div className="col-span-2 md:col-span-1">
                <Input
                  type="text"
                  id="billingZipCode"
                  name="billingZipCode"
                  value={formData.billingZipCode}
                  onChange={handleInputChange}
                  className="w-full"
                  placeholder="ZIP/Postal Code *"
                  required={!formData.sameAsShipping}
                />
              </div>
            </div>

            <div>
              <select
                id="billingCountry"
                name="billingCountry"
                value={formData.billingCountry}
                onChange={handleInputChange}
                className="w-full h-9 rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-xs transition-colors outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]"
                required={!formData.sameAsShipping}
              >
                <option value="" disabled>Select Country *</option>
                <option value="United States">United States</option>
                <option value="Canada">Canada</option>
                <option value="United Kingdom">United Kingdom</option>
                <option value="Australia">Australia</option>
                <option value="Germany">Germany</option>
                <option value="France">France</option>
                <option value="Japan">Japan</option>
                <option value="China">China</option>
              </select>
            </div>
          </div>
        )}
      </div>

      {/* Payment Method */}
      <div>
        <h3 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
          <CreditCard className="h-4 w-4 text-primary mr-2" />
          Payment Method
        </h3>
        <div className="space-y-3">
          <div
            className={`flex items-center p-3 border rounded-lg cursor-pointer transition-colors ${
              formData.paymentMethod === "credit-card"
                ? "border-primary bg-primary/5"
                : "border-gray-200 hover:border-gray-300"
            }`}
            onClick={() => handlePaymentMethodChange("credit-card")}
          >
            <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 transition-colors ${
              formData.paymentMethod === "credit-card"
                ? "bg-primary text-white"
                : "bg-gray-100 text-gray-400"
            }`}>
              <CreditCard className="h-4 w-4" />
            </div>
            <input
              type="radio"
              id="credit-card"
              name="paymentMethod"
              value="credit-card"
              checked={formData.paymentMethod === "credit-card"}
              onChange={() => handlePaymentMethodChange("credit-card")}
              className="hidden"
            />
            <label htmlFor="credit-card" className="flex items-center cursor-pointer">
              <div>
                <span className="text-sm font-medium text-gray-900">Credit / Debit Card</span>
                <span className="block text-xs text-gray-500 mt-0.5">Pay securely with your card</span>
              </div>
            </label>
            {formData.paymentMethod === "credit-card" && (
              <div className="ml-auto">
                <CheckCircle className="h-4 w-4 text-primary" />
              </div>
            )}
          </div>

          <div
            className={`flex items-center p-3 border rounded-lg cursor-pointer transition-colors ${
              formData.paymentMethod === "paypal"
                ? "border-primary bg-primary/5"
                : "border-gray-200 hover:border-gray-300"
            }`}
            onClick={() => handlePaymentMethodChange("paypal")}
          >
            <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 transition-colors ${
              formData.paymentMethod === "paypal"
                ? "bg-primary text-white"
                : "bg-gray-100 text-gray-400"
            }`}>
              <Wallet className="h-4 w-4" />
            </div>
            <input
              type="radio"
              id="paypal"
              name="paymentMethod"
              value="paypal"
              checked={formData.paymentMethod === "paypal"}
              onChange={() => handlePaymentMethodChange("paypal")}
              className="hidden"
            />
            <label htmlFor="paypal" className="flex items-center cursor-pointer">
              <div>
                <span className="text-sm font-medium text-gray-900">PayPal</span>
                <span className="block text-xs text-gray-500 mt-0.5">Pay with your PayPal account</span>
              </div>
            </label>
            {formData.paymentMethod === "paypal" && (
              <div className="ml-auto">
                <CheckCircle className="h-4 w-4 text-primary" />
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Credit Card Details */}
      {formData.paymentMethod === "credit-card" && (
        <div className="mt-4 p-4 border border-gray-200 rounded-lg bg-primary/5">
          <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
            <CreditCard className="h-4 w-4 text-primary mr-2" />
            Card Details
          </h4>

          <div className="space-y-3">
            <Input
              type="text"
              id="cardNumber"
              name="cardNumber"
              value={formData.cardNumber}
              onChange={handleInputChange}
              placeholder="Card Number *"
              className="w-full"
              required={formData.paymentMethod === "credit-card"}
            />

            <Input
              type="text"
              id="cardName"
              name="cardName"
              value={formData.cardName}
              onChange={handleInputChange}
              placeholder="Name on Card *"
              className="w-full"
              required={formData.paymentMethod === "credit-card"}
            />

            <div className="grid grid-cols-2 gap-3">
              <Input
                type="text"
                id="expiryDate"
                name="expiryDate"
                value={formData.expiryDate}
                onChange={handleInputChange}
                placeholder="Expiry Date (MM/YY) *"
                className="w-full"
                required={formData.paymentMethod === "credit-card"}
              />
              <Input
                type="text"
                id="cvv"
                name="cvv"
                value={formData.cvv}
                onChange={handleInputChange}
                placeholder="CVV *"
                className="w-full"
                required={formData.paymentMethod === "credit-card"}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
