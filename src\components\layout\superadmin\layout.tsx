"use client";

import React from "react";
import { SuperAdminHeader } from "./header";
import { SuperAdminSidebar } from "./sidebar";
import { DashboardLayout } from "@/components/dashboard/layout";

/**
 * Props for the SuperAdminLayout component
 */
interface SuperAdminLayoutProps {
  /** The main content of the superadmin dashboard */
  children: React.ReactNode;
}

/**
 * Layout component for the superadmin dashboard
 *
 * This component uses the base DashboardLayout with superadmin-specific
 * sidebar and header components.
 */
export function SuperAdminLayout({ children }: SuperAdminLayoutProps) {
  return (
    <DashboardLayout
      sidebar={<SuperAdminSidebar />}
      header={<SuperAdminHeader />}
      defaultSidebarOpen={true}
    >
      {children}
    </DashboardLayout>
  );
}
