"use client";

import React, { useState } from "react";
import Link from "next/link";
import { Container } from "@/components/ui/container";
import { RoundedInput } from "@/components/ui/rounded-input";
import { User, Lock, Mail, Check } from "lucide-react";
import { <PERSON>, <PERSON>Header, Card<PERSON>ontent, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

export default function RegisterPage() {
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    confirmPassword: "",
    agreeToTerms: false
  });

  const [errors, setErrors] = useState({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    confirmPassword: "",
    agreeToTerms: ""
  });

  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value,
    });

    // Clear error when user types
    if (errors[name as keyof typeof errors]) {
      setErrors({
        ...errors,
        [name]: "",
      });
    }
  };

  const validateForm = () => {
    let isValid = true;
    const newErrors = { ...errors };

    // First Name validation
    if (!formData.firstName.trim()) {
      newErrors.firstName = "First name is required";
      isValid = false;
    }

    // Last Name validation
    if (!formData.lastName.trim()) {
      newErrors.lastName = "Last name is required";
      isValid = false;
    }

    // Email validation
    if (!formData.email) {
      newErrors.email = "Email is required";
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
      isValid = false;
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = "Password is required";
      isValid = false;
    } else if (formData.password.length < 8) {
      newErrors.password = "Password must be at least 8 characters";
      isValid = false;
    }

    // Confirm Password validation
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = "Please confirm your password";
      isValid = false;
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match";
      isValid = false;
    }

    // Terms agreement validation
    if (!formData.agreeToTerms) {
      newErrors.agreeToTerms = "You must agree to the terms and conditions";
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      // Mock registration - would be replaced with actual API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Show success message
      setIsSuccess(true);

      // Redirect to login page after successful registration (after showing success message)
      setTimeout(() => {
        window.location.href = "/login";
      }, 2000);
    } catch (error) {
      console.error("Registration failed:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-background min-h-screen py-12">
      <Container>
        <div className="max-w-md mx-auto">
          {/* Registration Card */}
          <Card className="rounded-xl border border-gray-200 shadow-none overflow-hidden">
            {/* Header */}
            <CardHeader className="pt-5 pb-2">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center">
                  <Lock className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <h1 className="text-xl font-semibold text-primary">Sign Up</h1>
                  <p className="text-sm text-muted-foreground">Create your account to get started</p>
                </div>
              </div>
            </CardHeader>

            {/* Form */}
            <CardContent className="p-6 sm:p-8">

              {isSuccess ? (
                <div className="text-center py-8">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
                    <Check className="h-8 w-8 text-green-600" />
                  </div>
                  <h3 className="text-xl font-medium text-gray-900 mb-2">Registration Successful!</h3>
                  <p className="text-gray-500 mb-4">Your account has been created successfully.</p>
                  <p className="text-gray-500">Redirecting to login page...</p>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-5">
                  {/* First Name Field */}
                  <div className="space-y-2">
                    <label htmlFor="firstName" className="text-sm font-medium text-gray-700 flex items-center">
                      <User className="h-4 w-4 text-primary/70 mr-2" />
                      First Name <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <RoundedInput
                        type="text"
                        id="firstName"
                        name="firstName"
                        value={formData.firstName}
                        onChange={handleInputChange}
                        className={`w-full ${errors.firstName ? 'border-red-500 ring-red-500/20' : ''}`}
                        placeholder="John"
                        required
                      />
                    </div>
                    {errors.firstName && (
                      <p className="text-red-500 text-xs mt-1">{errors.firstName}</p>
                    )}
                  </div>

                  {/* Last Name Field */}
                  <div className="space-y-2">
                    <label htmlFor="lastName" className="text-sm font-medium text-gray-700 flex items-center">
                      <User className="h-4 w-4 text-primary/70 mr-2" />
                      Last Name <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <RoundedInput
                        type="text"
                        id="lastName"
                        name="lastName"
                        value={formData.lastName}
                        onChange={handleInputChange}
                        className={`w-full ${errors.lastName ? 'border-red-500 ring-red-500/20' : ''}`}
                        placeholder="Doe"
                        required
                      />
                    </div>
                    {errors.lastName && (
                      <p className="text-red-500 text-xs mt-1">{errors.lastName}</p>
                    )}
                  </div>

                  {/* Email Field */}
                  <div className="space-y-2">
                    <label htmlFor="email" className="text-sm font-medium text-gray-700 flex items-center">
                      <Mail className="h-4 w-4 text-primary/70 mr-2" />
                      Email Address <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <RoundedInput
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        className={`w-full ${errors.email ? 'border-red-500 ring-red-500/20' : ''}`}
                        placeholder="<EMAIL>"
                        required
                      />
                    </div>
                    {errors.email && (
                      <p className="text-red-500 text-xs mt-1">{errors.email}</p>
                    )}
                  </div>

                  {/* Password Field */}
                  <div className="space-y-2">
                    <label htmlFor="password" className="text-sm font-medium text-gray-700 flex items-center">
                      <Lock className="h-4 w-4 text-primary/70 mr-2" />
                      Password <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <RoundedInput
                        type="password"
                        id="password"
                        name="password"
                        value={formData.password}
                        onChange={handleInputChange}
                        className={`w-full ${errors.password ? 'border-red-500 ring-red-500/20' : ''}`}
                        placeholder="••••••••"
                        required
                      />
                    </div>
                    {errors.password && (
                      <p className="text-red-500 text-xs mt-1">{errors.password}</p>
                    )}
                    <p className="text-xs text-gray-500 mt-1">Password must be at least 8 characters</p>
                  </div>

                  {/* Confirm Password Field */}
                  <div className="space-y-2">
                    <label htmlFor="confirmPassword" className="text-sm font-medium text-gray-700 flex items-center">
                      <Lock className="h-4 w-4 text-primary/70 mr-2" />
                      Confirm Password <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <RoundedInput
                        type="password"
                        id="confirmPassword"
                        name="confirmPassword"
                        value={formData.confirmPassword}
                        onChange={handleInputChange}
                        className={`w-full ${errors.confirmPassword ? 'border-red-500 ring-red-500/20' : ''}`}
                        placeholder="••••••••"
                        required
                      />
                    </div>
                    {errors.confirmPassword && (
                      <p className="text-red-500 text-xs mt-1">{errors.confirmPassword}</p>
                    )}
                  </div>

                  {/* Terms and Conditions */}
                  <div className="space-y-2">
                    <div className="flex items-start">
                      <div className="flex items-center h-5">
                        <input
                          id="agreeToTerms"
                          name="agreeToTerms"
                          type="checkbox"
                          checked={formData.agreeToTerms}
                          onChange={handleInputChange}
                          className={`h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary/50 ${errors.agreeToTerms ? 'border-red-500' : ''}`}
                        />
                      </div>
                      <div className="ml-3 text-sm">
                        <label htmlFor="agreeToTerms" className="text-gray-600">
                          I agree to the <Link href="/terms" className="text-primary hover:underline hover:text-primary/80 transition-colors duration-300">Terms and Conditions</Link> and <Link href="/privacy" className="text-primary hover:underline hover:text-primary/80 transition-colors duration-300">Privacy Policy</Link>
                        </label>
                      </div>
                    </div>
                    {errors.agreeToTerms && (
                      <p className="text-red-500 text-xs">{errors.agreeToTerms}</p>
                    )}
                  </div>

                  {/* Register Button */}
                  <div>
                    <Button
                    type="submit"
                    className="w-full bg-gradient-to-r from-primary to-secondary text-white h-10 font-medium rounded-full disabled:opacity-50 transition-all duration-300 hover:brightness-110 shadow-sm"
                    disabled={isLoading}
                    >
                      {isLoading ? "Creating Account..." : "Create Account"}
                    </Button>
                  </div>
                </form>
              )}
            </CardContent>

            {/* Login Link */}
            <CardFooter className="p-6 bg-accent/10 dark:bg-accent/5 border-t border-border text-center">
              <p className="text-sm text-muted-foreground w-full">
                Already have an account?{" "}
                <Link href="/login" className="text-secondary font-medium hover:underline hover:text-secondary/80 transition-colors duration-300">
                  Login here
                </Link>
              </p>
            </CardFooter>
          </Card>
        </div>
      </Container>
    </div>
  );
}
