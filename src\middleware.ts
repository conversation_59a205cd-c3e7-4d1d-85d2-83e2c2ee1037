import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// This function can be marked `async` if using `await` inside
export function middleware(request: NextRequest) {
  // Get the pathname of the request
  const path = request.nextUrl.pathname;
  
  // Define paths that are accessible to the public
  const publicPaths = [
    '/superadmin/login',
    '/vendor/login',
    '/vendor/register',
    '/login',
    '/register',
  ];
  
  // Check if the path is public
  const isPublicPath = publicPaths.some(publicPath => 
    path.startsWith(publicPath)
  );
  
  // Get the token from cookies
  const token = request.cookies.get('accessToken')?.value || '';
  
  // If the path is for superadmin and not the login page
  if (path.startsWith('/superadmin') && !isPublicPath) {
    // If there's no token, redirect to login
    if (!token) {
      return NextResponse.redirect(new URL('/superadmin/login', request.url));
    }
  }
  
  // If the path is for vendor and not login/register pages
  if (path.startsWith('/vendor') && !isPublicPath) {
    // If there's no token, redirect to login
    if (!token) {
      return NextResponse.redirect(new URL('/vendor/login', request.url));
    }
  }
  
  return NextResponse.next();
}

// Configure the middleware to run on specific paths
export const config = {
  matcher: [
    '/superadmin/:path*',
    '/vendor/:path*',
  ],
};