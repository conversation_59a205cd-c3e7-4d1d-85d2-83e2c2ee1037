import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

/**
 * Simple JWT token validation (basic check)
 * In production, consider using a proper JWT library for full validation
 */
function isValidJWT(token: string): boolean {
  try {
    // Basic JWT structure check (header.payload.signature)
    const parts = token.split('.');
    if (parts.length !== 3) return false;

    // Try to decode the payload to check if it's valid JSON
    const payload = JSON.parse(atob(parts[1]));

    // Check if token is expired
    if (payload.exp && payload.exp * 1000 < Date.now()) {
      return false;
    }

    return true;
  } catch {
    return false;
  }
}

/**
 * Enhanced middleware with JWT validation and comprehensive route protection
 */
export function middleware(request: NextRequest) {
  // Get the pathname of the request
  const path = request.nextUrl.pathname;

  // Define paths that are accessible to the public
  const publicPaths = [
    '/superadmin/login',
    '/vendor/login',
    '/vendor/register',
    '/login',
    '/register',
    '/forgot-password',
    '/reset-password',
    '/', // Home page
    '/shop',
    '/products',
    '/categories',
    '/about',
    '/contact',
    '/terms',
    '/privacy',
    '/help',
  ];

  // Define protected customer paths that require authentication
  const protectedCustomerPaths = [
    '/profile',
    '/orders',
    '/wishlist',
    '/checkout',
    '/account',
  ];

  // Check if the path is public
  const isPublicPath = publicPaths.some(publicPath =>
    path === publicPath || path.startsWith(publicPath + '/')
  );

  // Check if it's a protected customer path
  const isProtectedCustomerPath = protectedCustomerPaths.some(protectedPath =>
    path.startsWith(protectedPath)
  );

  // Get the token from cookies
  const token = request.cookies.get('accessToken')?.value || '';

  // Validate token if present
  const hasValidToken = token && isValidJWT(token);

  // Security headers
  const response = NextResponse.next();

  // Add security headers
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('X-XSS-Protection', '1; mode=block');

  // If the path is for superadmin and not the login page
  if (path.startsWith('/superadmin') && !isPublicPath) {
    if (!hasValidToken) {
      return NextResponse.redirect(new URL('/superadmin/login', request.url));
    }
  }

  // If the path is for vendor and not login/register pages
  if (path.startsWith('/vendor') && !isPublicPath) {
    if (!hasValidToken) {
      return NextResponse.redirect(new URL('/vendor/login', request.url));
    }
  }

  // If the path is a protected customer path
  if (isProtectedCustomerPath && !hasValidToken) {
    return NextResponse.redirect(new URL('/login', request.url));
  }

  return response;
}

// Configure the middleware to run on specific paths
export const config = {
  matcher: [
    '/superadmin/:path*',
    '/vendor/:path*',
    '/profile/:path*',
    '/orders/:path*',
    '/wishlist/:path*',
    '/checkout/:path*',
    '/account/:path*',
  ],
};