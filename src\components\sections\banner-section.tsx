import { Container } from "@/components/ui/container";
import Link from "next/link";
import { Card } from "@/components/ui/card";

type BannerSectionProps = {
  backgroundColor?: string;
};

export const BannerSection = ({ backgroundColor = "bg-white" }: BannerSectionProps) => {
  return (
    <div className={`py-8 ${backgroundColor} dark:bg-card`}>
      <Container>
        <Card className="w-full overflow-hidden rounded-lg p-0 border border-gray-200 dark:border-border shadow-none" style={{ background: "linear-gradient(135deg, #052E7F 0%, #009481 50%, #F8166F 100%)" }}>
          <Link href="/promotions/select-and-buy">
            <div className="relative w-full h-[120px] sm:h-[150px] md:h-[180px] flex items-center justify-center overflow-hidden bg-gradient-rainbow">
              <div className="absolute inset-0 bg-[url('/images/pattern-dots.svg')] opacity-10 bg-repeat"></div>
              <div className="absolute -right-4 top-1/2 transform -translate-y-1/2 hidden md:block">
                <div className="flex gap-3">
                  <div className="w-16 h-16 bg-secondary/90 rounded-full animate-float shadow-lg"></div>
                  <div className="w-12 h-12 bg-accent/90 rounded-full animate-float-delay shadow-lg"></div>
                  <div className="w-10 h-10 bg-primary/90 rounded-full animate-float shadow-lg"></div>
                </div>
              </div>
              <div className="text-center z-10 px-4">
                <h2 className="text-white text-2xl sm:text-3xl md:text-4xl font-bold mb-2 drop-shadow-md">
                  Select & BUY
                </h2>
                <p className="text-white/90 text-sm sm:text-base tracking-wide drop-shadow-sm">FROM YOUR FAVOURITE STORES</p>
              </div>
            </div>
          </Link>
        </Card>
      </Container>
    </div>
  );
};
