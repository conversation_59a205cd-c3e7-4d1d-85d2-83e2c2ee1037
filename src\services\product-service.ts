/**
 * Product service for handling product data
 */

import { Product, SortOption, ActiveFilter } from '@/types';

// Sample products data
const products: Product[] = [
  // Organic Delights Products
  {
    id: "organic-juice",
    name: "Organic Cold-Pressed Juice Set",
    category: "Food & Beverages",
    price: 34.99,
    discount: false,
    rating: 4.4,
    image: "/images/products/berry.png",
    storeId: "1"
  },
  {
    id: "organic-honey",
    name: "Raw Organic Wildflower Honey",
    category: "Food & Beverages",
    price: 12.99,
    discount: true,
    oldPrice: 15.99,
    discountPercentage: 19,
    rating: 4.8,
    image: "/images/products/headphone.png",
    storeId: "1"
  },
  {
    id: "organic-tea",
    name: "Organic Herbal Tea Collection",
    category: "Food & Beverages",
    price: 24.99,
    discount: false,
    rating: 4.6,
    image: "/images/products/berry.png",
    storeId: "1"
  },
  {
    id: "organic-snacks",
    name: "Organic Mixed Nuts Gift Box",
    category: "Food & Beverages",
    price: 29.99,
    discount: true,
    oldPrice: 39.99,
    discountPercentage: 25,
    rating: 4.7,
    image: "/images/products/headphone.png",
    storeId: "1"
  },
  {
    id: "organic-quinoa",
    name: "Organic Quinoa Super Grain",
    category: "Food & Beverages",
    price: 8.99,
    discount: true,
    oldPrice: 10.99,
    discountPercentage: 18,
    rating: 4.5,
    image: "/images/products/berry.png",
    storeId: "1"
  },
  // Tech Haven Products
  {
    id: "wireless-headphones",
    name: "Wireless Noise Cancelling Headphones",
    category: "Electronics",
    price: 129.99,
    oldPrice: 249.99,
    discount: true,
    discountPercentage: 48,
    rating: 4.5,
    image: "/images/products/headphone.png",
    storeId: "2"
  },
  {
    id: "smart-speaker",
    name: "Smart Home Speaker with Voice Assistant",
    category: "Electronics",
    price: 89.99,
    discount: false,
    rating: 4.6,
    image: "/images/products/berry.png",
    storeId: "2"
  },
  {
    id: "fitness-tracker",
    name: "Fitness Tracker Watch",
    category: "Electronics",
    price: 59.99,
    oldPrice: 79.99,
    discount: true,
    discountPercentage: 25,
    rating: 4.3,
    image: "/images/products/headphone.png",
    storeId: "2"
  },
  // Style Avenue Products
  {
    id: "leather-wallet",
    name: "Premium Leather Minimalist Wallet",
    category: "Accessories",
    price: 79.99,
    oldPrice: 99.99,
    discount: true,
    discountPercentage: 20,
    rating: 4.7,
    image: "/images/products/headphone.png",
    storeId: "3"
  },
  {
    id: "silk-scarf",
    name: "Luxury Silk Scarf",
    category: "Accessories",
    price: 49.99,
    discount: false,
    rating: 4.8,
    image: "/images/products/berry.png",
    storeId: "3"
  },
];

// Sort options for product filtering
export const sortOptions: SortOption[] = [
  { label: "Popularity", value: "popularity" },
  { label: "Price: Low to High", value: "price_asc" },
  { label: "Price: High to Low", value: "price_desc" },
  { label: "Newest First", value: "newest" },
  { label: "Discount", value: "discount" }
];

/**
 * Get all products
 */
export const getAllProducts = (): Product[] => {
  return products;
};

/**
 * Get product by ID
 */
export const getProductById = (id: string): Product | undefined => {
  return products.find(product => product.id === id);
};

/**
 * Get products by store ID
 */
export const getProductsByStoreId = (storeId: string): Product[] => {
  return products.filter(product => product.storeId === storeId);
};

/**
 * Get featured products (highest rated)
 */
export const getFeaturedProducts = (limit: number = 5): Product[] => {
  return [...products]
    .sort((a, b) => b.rating - a.rating)
    .slice(0, limit);
};

/**
 * Get best seller products (highest discount)
 */
export const getBestSellerProducts = (limit: number = 5): Product[] => {
  return [...products]
    .filter(p => p.discount)
    .sort((a, b) => (b.discountPercentage || 0) - (a.discountPercentage || 0))
    .slice(0, limit);
};

/**
 * Filter products based on criteria
 */
export const filterProducts = (
  filters: {
    priceRange?: { min: string; max: string };
    categories?: string[];
    brands?: string[];
    discounts?: string[];
    ratings?: number | null;
    sortBy?: string;
  }
): Product[] => {
  let filteredProducts = [...products];

  // Filter by price range
  if (filters.priceRange && (filters.priceRange.min || filters.priceRange.max)) {
    const min = filters.priceRange.min ? parseFloat(filters.priceRange.min) : 0;
    const max = filters.priceRange.max ? parseFloat(filters.priceRange.max) : Infinity;
    
    filteredProducts = filteredProducts.filter(
      product => product.price >= min && product.price <= max
    );
  }

  // Filter by categories
  if (filters.categories && filters.categories.length > 0) {
    filteredProducts = filteredProducts.filter(
      product => filters.categories?.includes(product.category)
    );
  }

  // Sort products
  if (filters.sortBy) {
    switch (filters.sortBy) {
      case 'price_asc':
        filteredProducts.sort((a, b) => a.price - b.price);
        break;
      case 'price_desc':
        filteredProducts.sort((a, b) => b.price - a.price);
        break;
      case 'newest':
        // In a real app, would sort by date
        break;
      case 'discount':
        filteredProducts.sort((a, b) => {
          const discountA = a.discount ? (a.discountPercentage || 0) : 0;
          const discountB = b.discount ? (b.discountPercentage || 0) : 0;
          return discountB - discountA;
        });
        break;
      default: // popularity
        filteredProducts.sort((a, b) => b.rating - a.rating);
    }
  }

  return filteredProducts;
};
