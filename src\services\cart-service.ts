/**
 * Cart service for handling cart data
 */

import { CartItem } from '@/types';

// Sample cart items
const cartItems: CartItem[] = [
  {
    id: 1,
    name: "Wireless Bluetooth Headphones",
    price: 79.99,
    originalPrice: 99.99,
    quantity: 1,
    image: "/images/products/headphones.jpg",
    vendor: "AudioTech",
    color: "Black",
  },
  {
    id: 2,
    name: "Smart Watch Series 5",
    price: 199.99,
    originalPrice: 199.99,
    quantity: 1,
    image: "/images/products/smartwatch.jpg",
    vendor: "TechGear",
    color: "Silver",
  },
  {
    id: 3,
    name: "Laptop Sleeve Case",
    price: 29.99,
    originalPrice: 39.99,
    quantity: 1,
    image: "/images/products/laptop-sleeve.jpg",
    vendor: "BagWorld",
    color: "Navy Blue",
  },
];

/**
 * Get all cart items
 */
export const getCartItems = (): CartItem[] => {
  return cartItems;
};

/**
 * Calculate cart subtotal
 */
export const calculateSubtotal = (items: CartItem[]): number => {
  return items.reduce((total, item) => total + item.price * item.quantity, 0);
};

/**
 * Calculate shipping cost
 * Free shipping for orders over $100
 */
export const calculateShipping = (subtotal: number): number => {
  return subtotal > 100 ? 0 : 4.99;
};

/**
 * Calculate tax (8%)
 */
export const calculateTax = (subtotal: number): number => {
  return subtotal * 0.08;
};

/**
 * Calculate total savings
 */
export const calculateSavings = (items: CartItem[]): number => {
  return items.reduce(
    (total, item) => total + (item.originalPrice - item.price) * item.quantity, 
    0
  );
};

/**
 * Calculate amount needed for free shipping
 */
export const calculateAmountToFreeShipping = (subtotal: number): number => {
  const freeShippingThreshold = 100;
  return Math.max(0, freeShippingThreshold - subtotal);
};
