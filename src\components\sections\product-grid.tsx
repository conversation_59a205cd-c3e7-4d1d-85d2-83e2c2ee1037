"use client";

import React from "react";
import Link from "next/link";
import Image from "next/image";
import { Container } from "@/components/ui/container";
import { ArrowRight, Heart, ShoppingCart, Star, Sparkles } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { useContainerContext } from "@/contexts/container-context";

// Product type definition
type Product = {
  name: string;
  category: string;
  price: number;
  discount: boolean;
  discountPercentage?: number;
  oldPrice?: number;
  rating: number;
  image?: string;
  id?: string; // Optional ID for product detail page linking
};

type ProductGridProps = {
  title?: string;
  products: Product[];
  shopMoreLink: string;
  sectionType?: "recommended" | "featured" | "bestseller" | "products";
  icon?: React.ReactNode;
  backgroundColor?: string;
  showTitle?: boolean;
  useContainer?: boolean;
};

export const ProductGrid = ({
  title = "Recommended For You",
  products,
  shopMoreLink,
  sectionType = "recommended", // Used for semantic purposes, can be used for conditional styling
  icon,
  backgroundColor = "bg-white",
  showTitle = true,
  useContainer,
}: ProductGridProps) => {
  // Default icon is Sparkles if none provided
  const IconComponent = icon || <Sparkles size={20} />;

  // Get container context to check if we're already inside a container
  const { isInsideContainer } = useContainerContext();

  // Determine if we should use a container based on context
  // If useContainer is explicitly set, use that value
  // Otherwise, check if we're already inside a container
  const shouldUseContainer = useContainer !== undefined ? useContainer : !isInsideContainer;

  // Content to render inside or outside the container
  const content = (
    <div className={`flex flex-col ${showTitle ? 'space-y-6' : 'space-y-0'} px-2`}>
          {showTitle && (
            <>
              {/* Header with title and shop more link - Desktop */}
              <div className="hidden sm:block">
                <div className="flex items-center w-full relative">
                  {/* Horizontal line that spans the entire width */}
                  <div className="absolute h-px bg-gray-200 dark:bg-gray-700 w-full"></div>

                  {/* Title with icon - positioned on top of the line */}
                  <div className={`flex-shrink-0 flex items-center ${backgroundColor} dark:bg-card rounded-full p-1 z-10 mr-4`}>
                    <div className="flex items-center gap-2 px-3">
                      <div className="w-6 h-6 flex items-center justify-center text-primary dark:text-accent">
                        {IconComponent}
                      </div>
                      <h2 className="text-lg font-bold text-primary dark:text-accent">{title}</h2>
                    </div>
                  </div>

                  {/* Empty flex-grow div to push the Shop More button to the right */}
                  <div className="flex-grow"></div>

                  {/* Shop More button - positioned on top of the line */}
                  <div className="flex-shrink-0 z-10">
                    <Link
                      href={shopMoreLink}
                      className={`flex ${backgroundColor} dark:bg-card hover:bg-gradient-primary hover:text-white text-primary px-3 sm:px-4 py-2 rounded-full items-center gap-1 whitespace-nowrap transition-all text-xs sm:text-sm border border-gray-200 dark:border-border dark:text-accent`}
                    >
                      Shop More <ArrowRight className="h-3 w-3 sm:h-4 sm:w-4" />
                    </Link>
                  </div>
                </div>
              </div>

              {/* Header with title and shop more link - Mobile */}
              <div className="sm:hidden">
                {/* Standard title layout for mobile */}
                <div className="flex items-center w-full relative">
                  {/* Horizontal line that spans the entire width */}
                  <div className="absolute h-px bg-gray-200 dark:bg-gray-700 w-full"></div>

                  {/* Title with icon - positioned on top of the line */}
                  <div className={`flex-shrink-0 flex items-center ${backgroundColor} dark:bg-card rounded-full p-1 z-10 mr-4`}>
                    <div className="flex items-center gap-2 px-2">
                      <div className="w-5 h-5 flex items-center justify-center text-primary dark:text-accent">
                        {icon || <Sparkles size={16} />}
                      </div>
                      <h2 className="text-base font-bold text-primary dark:text-accent">{title}</h2>
                    </div>
                  </div>

                  {/* Empty flex-grow div to push the Shop More button to the right */}
                  <div className="flex-grow"></div>

                  {/* Shop More button - positioned on top of the line */}
                  <div className="flex-shrink-0 z-10">
                    <Link
                      href={shopMoreLink}
                      className={`flex ${backgroundColor} dark:bg-card hover:bg-gradient-primary hover:text-white text-primary px-3 sm:px-4 py-2 rounded-full items-center gap-1 whitespace-nowrap transition-all text-xs sm:text-sm border border-gray-200 dark:border-border dark:text-accent`}
                    >
                      Shop More <ArrowRight className="h-3 w-3" />
                    </Link>
                  </div>
                </div>
              </div>
            </>
          )}

          {/* Products grid - Two rows */}
          <div className={showTitle ? "mt-6" : "mt-0"}>
            <div className="grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-3 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-5">
              {products.slice(0, 10).map((product, index) => (
                <Card key={index} className="overflow-hidden border border-gray-200 dark:border-border hover:border-primary/30 dark:hover:border-primary/50 hover-lift group h-full p-0 rounded-xl shadow-none">
                  <Link href={`/product/${product.id || `product-${index}`}`} className="block">
                    <div className="relative">
                      <div className="h-48 w-full relative overflow-hidden">
                          <Image
                            src={product.image || `/images/products/${index % 4 === 0 ? 'headphone.png' :
                                                                    index % 4 === 1 ? 'berry.png' :
                                                                    index % 4 === 2 ? 'headphone.png' :
                                                                    'berry.png'}`}
                            alt={product.name}
                            fill
                            className="object-cover w-full h-full transition-transform duration-300 group-hover:scale-105"
                          />
                      </div>
                      <button
                        className="absolute top-3 right-3 bg-white dark:bg-card rounded-full p-1.5 shadow-md opacity-80 group-hover:opacity-100 hover:bg-primary hover:text-white transition-all duration-300"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <Heart className="h-3.5 w-3.5" />
                      </button>
                      {product.discount && (
                        <div className="absolute top-3 left-3 bg-red-500 text-white text-xs font-bold px-2 py-0.5 rounded">
                          {product.discountPercentage ? `-${product.discountPercentage}%` : 'SALE'}
                        </div>
                      )}
                      {index === 1 && (
                        <div className="absolute top-3 left-3 bg-blue-500 text-white text-xs font-bold px-2 py-0.5 rounded">
                          NEW
                        </div>
                      )}
                    </div>
                    <CardContent className="p-4 gap-0">
                      <div className="flex items-center gap-0.5 mb-2">
                        {Array.from({ length: Math.floor(product.rating) }).map((_, i) => (
                          <Star key={i} className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                        ))}
                        {product.rating % 1 !== 0 && (
                          <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" style={{ clipPath: `inset(0 ${100 - (product.rating % 1) * 100}% 0 0)` }} />
                        )}
                        <span className="text-xs text-gray-500 dark:text-gray-400 ml-1">{product.rating}</span>
                      </div>
                      <h3 className="font-medium text-gray-900 dark:text-accent group-hover:text-primary dark:group-hover:text-accent/80 transition-colors text-sm sm:text-base truncate">{product.name}</h3>
                      <p className="text-gray-500 dark:text-gray-400 text-xs mt-1 truncate flex items-center">
                        <span className="inline-block w-2 h-2 rounded-full bg-primary/40 mr-1.5"></span>
                        {product.category}
                      </p>
                      <div className="mt-3 flex items-center justify-between">
                        <div className="flex items-center gap-1.5">
                          <p className="text-primary dark:text-accent font-semibold text-sm sm:text-base">${product.price}</p>
                          {product.discount && product.oldPrice && (
                            <p className="text-gray-400 dark:text-gray-500 text-xs sm:text-sm line-through">${product.oldPrice}</p>
                          )}
                        </div>
                        <button
                          className="bg-gray-100 dark:bg-gray-800 hover:bg-gradient-primary hover:text-white p-2 rounded-full transition-all duration-300 hover:shadow"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            // Add to cart functionality would go here
                          }}
                        >
                          <ShoppingCart className="h-3.5 w-3.5" />
                        </button>
                      </div>
                    </CardContent>
                  </Link>
                </Card>
              ))}
            </div>
          </div>
        </div>
  );

  // Render with or without container based on the shouldUseContainer value
  return (
    <div className={`${backgroundColor} dark:bg-card ${showTitle ? 'py-12' : 'py-6'}`}>
      {shouldUseContainer ? (
        <Container>{content}</Container>
      ) : (
        content
      )}
    </div>
  );
};

