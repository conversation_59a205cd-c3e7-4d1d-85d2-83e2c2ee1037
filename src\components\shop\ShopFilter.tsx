"use client";

import React, { useState } from "react";
import {
  ChevronDown,
  Star,
  Check,
  X,
  Filter,
  SlidersHorizontal
} from "lucide-react";

// Define filter types
type PriceRange = {
  min: string;
  max: string;
};

type FilterState = {
  priceRange: PriceRange;
  brands: string[];
  discounts: string[];
  sizes: string[];
  colors: string[];
  ratings: number | null;
  fabrics: string[];
  patterns: string[];
  occasions: string[];
};

type ShopFilterProps = {
  onFilterChange?: (filters: FilterState) => void;
};

export function ShopFilter({ onFilterChange }: ShopFilterProps = {}) {
  // Filter state
  const [filters, setFilters] = useState<FilterState>({
    priceRange: { min: "", max: "" },
    brands: [],
    discounts: [],
    sizes: [],
    colors: [],
    ratings: null,
    fabrics: [],
    patterns: [],
    occasions: []
  });

  // Collapsible sections state
  const [expandedSections, setExpandedSections] = useState({
    price: true,
    brand: false,
    discount: false,
    size: false,
    color: false,
    rating: false,
    fabric: false,
    pattern: false,
    occasion: false
  });

  // Toggle section expansion
  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections({
      ...expandedSections,
      [section]: !expandedSections[section]
    });
  };

  // Handle price range change
  const handlePriceChange = (type: 'min' | 'max', value: string) => {
    const updatedFilters = {
      ...filters,
      priceRange: {
        ...filters.priceRange,
        [type]: value
      }
    };
    setFilters(updatedFilters);
    onFilterChange?.(updatedFilters);
  };

  // Handle checkbox filters (brands, discounts, etc.)
  const handleCheckboxChange = (filterType: keyof FilterState, value: string) => {
    if (Array.isArray(filters[filterType])) {
      const currentValues = filters[filterType] as string[];
      const newValues = currentValues.includes(value)
        ? currentValues.filter(item => item !== value)
        : [...currentValues, value];

      const updatedFilters = {
        ...filters,
        [filterType]: newValues
      };

      setFilters(updatedFilters);
      onFilterChange?.(updatedFilters);
    }
  };

  // Handle rating filter
  const handleRatingChange = (rating: number) => {
    const updatedFilters = {
      ...filters,
      ratings: filters.ratings === rating ? null : rating
    };

    setFilters(updatedFilters);
    onFilterChange?.(updatedFilters);
  };

  // Clear all filters
  const clearAllFilters = () => {
    const emptyFilters = {
      priceRange: { min: "", max: "" },
      brands: [],
      discounts: [],
      sizes: [],
      colors: [],
      ratings: null,
      fabrics: [],
      patterns: [],
      occasions: []
    };

    setFilters(emptyFilters);
    onFilterChange?.(emptyFilters);
  };

  // Sample data for filters
  const brandOptions = ["Nike", "Adidas", "Puma", "Reebok", "Under Armour", "New Balance"];
  const discountOptions = ["10% or more", "20% or more", "30% or more", "40% or more", "50% or more"];
  const sizeOptions = ["XS", "S", "M", "L", "XL", "XXL"];
  const colorOptions = [
    { name: "Black", hex: "#000000" },
    { name: "White", hex: "#FFFFFF" },
    { name: "Red", hex: "#FF0000" },
    { name: "Blue", hex: "#0000FF" },
    { name: "Green", hex: "#00FF00" },
    { name: "Yellow", hex: "#FFFF00" },
    { name: "Purple", hex: "#800080" },
    { name: "Orange", hex: "#FFA500" },
    { name: "Pink", hex: "#FFC0CB" },
    { name: "Gray", hex: "#808080" }
  ];
  const fabricOptions = ["Cotton", "Polyester", "Nylon", "Wool", "Silk", "Linen"];
  const patternOptions = ["Solid", "Printed", "Striped", "Checked", "Graphic"];
  const occasionOptions = ["Casual", "Formal", "Sports", "Party", "Beach"];

  // Mobile filter state
  const [isMobileFilterOpen, setIsMobileFilterOpen] = useState(false);

  return (
    <>
      {/* Mobile filter button - visible only on small screens */}
      <div className="lg:hidden mb-4 flex justify-between items-center">
        <button
          onClick={() => setIsMobileFilterOpen(true)}
          className="flex items-center gap-2 px-4 py-2 bg-primary text-white rounded-md"
        >
          <Filter size={16} />
          <span>Filters</span>
        </button>

        {Object.values(filters).some(val =>
          Array.isArray(val) ? val.length > 0 : val !== null && val !== "" &&
          (typeof val === 'object' ? Object.values(val).some(v => v !== "") : true)
        ) && (
          <button
            onClick={clearAllFilters}
            className="text-sm text-gray-600 dark:text-gray-400 underline"
          >
            Clear All
          </button>
        )}
      </div>

      {/* Mobile filter overlay */}
      {isMobileFilterOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 lg:hidden">
          <div className="absolute right-0 top-0 h-full w-[80%] max-w-md bg-white dark:bg-card overflow-y-auto">
            <div className="p-4 border-b dark:border-border sticky top-0 bg-white dark:bg-card z-10 flex justify-between items-center">
              <h2 className="text-lg font-medium flex items-center gap-2 dark:text-gray-100">
                <SlidersHorizontal size={18} />
                Filters
              </h2>
              <button
                onClick={() => setIsMobileFilterOpen(false)}
                className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
              >
                <X size={20} />
              </button>
            </div>
            <div className="p-4">
              {/* Filter content - same as desktop but styled for mobile */}
              <FilterContent
                filters={filters}
                expandedSections={expandedSections}
                toggleSection={toggleSection}
                handlePriceChange={handlePriceChange}
                handleCheckboxChange={handleCheckboxChange}
                handleRatingChange={handleRatingChange}
                brandOptions={brandOptions}
                discountOptions={discountOptions}
                sizeOptions={sizeOptions}
                colorOptions={colorOptions}
                fabricOptions={fabricOptions}
                patternOptions={patternOptions}
                occasionOptions={occasionOptions}
              />
            </div>
            <div className="sticky bottom-0 p-4 border-t dark:border-border bg-white dark:bg-card">
              <button
                onClick={() => setIsMobileFilterOpen(false)}
                className="w-full py-2 bg-primary text-white rounded-md"
              >
                Apply Filters
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Desktop filter sidebar - hidden on small screens */}
      <div className="hidden lg:block w-full">
        <div className="bg-white dark:bg-card rounded-lg border border-gray-200 dark:border-border overflow-hidden">
          <div className="p-4 border-b dark:border-border flex justify-between items-center">
            <h2 className="font-medium flex items-center gap-2 dark:text-gray-100">
              <SlidersHorizontal size={16} />
              Filters
            </h2>
            {Object.values(filters).some(val =>
              Array.isArray(val) ? val.length > 0 : val !== null && val !== "" &&
              (typeof val === 'object' ? Object.values(val).some(v => v !== "") : true)
            ) && (
              <button
                onClick={clearAllFilters}
                className="text-xs text-gray-600 dark:text-gray-400 underline"
              >
                Clear All
              </button>
            )}
          </div>
          <div className="p-4">
            <FilterContent
              filters={filters}
              expandedSections={expandedSections}
              toggleSection={toggleSection}
              handlePriceChange={handlePriceChange}
              handleCheckboxChange={handleCheckboxChange}
              handleRatingChange={handleRatingChange}
              brandOptions={brandOptions}
              discountOptions={discountOptions}
              sizeOptions={sizeOptions}
              colorOptions={colorOptions}
              fabricOptions={fabricOptions}
              patternOptions={patternOptions}
              occasionOptions={occasionOptions}
            />
          </div>
        </div>
      </div>
    </>
  );
}

// Separate component for filter content to avoid duplication
function FilterContent({
  filters,
  expandedSections,
  toggleSection,
  handlePriceChange,
  handleCheckboxChange,
  handleRatingChange,
  brandOptions,
  discountOptions,
  sizeOptions,
  colorOptions,
  fabricOptions,
  patternOptions,
  occasionOptions
}: {
  filters: FilterState;
  expandedSections: Record<string, boolean>;
  toggleSection: (section: string) => void;
  handlePriceChange: (type: 'min' | 'max', value: string) => void;
  handleCheckboxChange: (filterType: keyof FilterState, value: string) => void;
  handleRatingChange: (rating: number) => void;
  brandOptions: string[];
  discountOptions: string[];
  sizeOptions: string[];
  colorOptions: { name: string; hex: string }[];
  fabricOptions: string[];
  patternOptions: string[];
  occasionOptions: string[];
}) {
  return (
    <div className="space-y-4">
      {/* Price Filter */}
      <div className="border-b dark:border-border pb-4">
        <button
          className="flex items-center justify-between w-full mb-2"
          onClick={() => toggleSection('price')}
        >
          <span className="font-medium dark:text-gray-200">Price</span>
          <ChevronDown
            size={16}
            className={`transition-transform ${expandedSections.price ? 'rotate-180' : ''} dark:text-gray-400`}
          />
        </button>

        {expandedSections.price && (
          <div className="mt-2 space-y-2">
            <div className="flex items-center gap-2">
              <input
                type="number"
                placeholder="Min"
                className="w-full p-2 border dark:border-border rounded-md text-sm dark:bg-card dark:text-gray-200"
                value={filters.priceRange.min}
                onChange={(e) => handlePriceChange('min', e.target.value)}
              />
              <span className="text-gray-500 dark:text-gray-400">to</span>
              <input
                type="number"
                placeholder="Max"
                className="w-full p-2 border dark:border-border rounded-md text-sm dark:bg-card dark:text-gray-200"
                value={filters.priceRange.max}
                onChange={(e) => handlePriceChange('max', e.target.value)}
              />

            </div>
          </div>
        )}
      </div>

      {/* Brand Filter */}
      <div className="border-b dark:border-border pb-4">
        <button
          className="flex items-center justify-between w-full mb-2"
          onClick={() => toggleSection('brand')}
        >
          <span className="font-medium dark:text-gray-200">Brand</span>
          <ChevronDown
            size={16}
            className={`transition-transform ${expandedSections.brand ? 'rotate-180' : ''} dark:text-gray-400`}
          />
        </button>

        {expandedSections.brand && (
          <div className="mt-2 space-y-1 max-h-40 overflow-y-auto">
            {brandOptions.map((brand) => (
              <label key={brand} className="flex items-center gap-2 cursor-pointer">
                <input
                  type="checkbox"
                  className="rounded text-primary focus:ring-primary dark:bg-card dark:border-border"
                  checked={filters.brands.includes(brand)}
                  onChange={() => handleCheckboxChange('brands', brand)}
                />
                <span className="text-sm dark:text-gray-300">{brand}</span>
              </label>
            ))}
          </div>
        )}
      </div>

      {/* Discount Filter */}
      <div className="border-b dark:border-border pb-4">
        <button
          className="flex items-center justify-between w-full mb-2"
          onClick={() => toggleSection('discount')}
        >
          <span className="font-medium dark:text-gray-200">Discount</span>
          <ChevronDown
            size={16}
            className={`transition-transform ${expandedSections.discount ? 'rotate-180' : ''} dark:text-gray-400`}
          />
        </button>

        {expandedSections.discount && (
          <div className="mt-2 space-y-1">
            {discountOptions.map((discount) => (
              <label key={discount} className="flex items-center gap-2 cursor-pointer">
                <input
                  type="checkbox"
                  className="rounded text-primary focus:ring-primary dark:bg-card dark:border-border"
                  checked={filters.discounts.includes(discount)}
                  onChange={() => handleCheckboxChange('discounts', discount)}
                />
                <span className="text-sm dark:text-gray-300">{discount}</span>
              </label>
            ))}
          </div>
        )}
      </div>

      {/* Size Filter */}
      <div className="border-b dark:border-border pb-4">
        <button
          className="flex items-center justify-between w-full mb-2"
          onClick={() => toggleSection('size')}
        >
          <span className="font-medium dark:text-gray-200">Size</span>
          <ChevronDown
            size={16}
            className={`transition-transform ${expandedSections.size ? 'rotate-180' : ''} dark:text-gray-400`}
          />
        </button>

        {expandedSections.size && (
          <div className="mt-2 flex flex-wrap gap-2">
            {sizeOptions.map((size) => (
              <button
                key={size}
                className={`px-3 py-1 border rounded-md text-sm ${
                  filters.sizes.includes(size)
                    ? 'bg-primary text-white border-primary'
                    : 'border-gray-300 dark:border-gray-700 hover:border-primary dark:text-gray-300'
                }`}
                onClick={() => handleCheckboxChange('sizes', size)}
              >
                {size}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Color Filter */}
      <div className="border-b dark:border-border pb-4">
        <button
          className="flex items-center justify-between w-full mb-2"
          onClick={() => toggleSection('color')}
        >
          <span className="font-medium dark:text-gray-200">Color</span>
          <ChevronDown
            size={16}
            className={`transition-transform ${expandedSections.color ? 'rotate-180' : ''} dark:text-gray-400`}
          />
        </button>

        {expandedSections.color && (
          <div className="mt-2 flex flex-wrap gap-2">
            {colorOptions.map((color) => (
              <button
                key={color.name}
                className={`w-8 h-8 rounded-full border-2 flex items-center justify-center ${
                  filters.colors.includes(color.name)
                    ? 'border-primary'
                    : 'border-transparent hover:border-gray-300 dark:hover:border-gray-600'
                }`}
                style={{ backgroundColor: color.hex }}
                onClick={() => handleCheckboxChange('colors', color.name)}
                title={color.name}
              >
                {filters.colors.includes(color.name) && (
                  <Check size={14} className={color.name === 'White' ? 'text-black' : 'text-white'} />
                )}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Customer Ratings */}
      <div className="border-b dark:border-border pb-4">
        <button
          className="flex items-center justify-between w-full mb-2"
          onClick={() => toggleSection('rating')}
        >
          <span className="font-medium dark:text-gray-200">Customer Ratings</span>
          <ChevronDown
            size={16}
            className={`transition-transform ${expandedSections.rating ? 'rotate-180' : ''} dark:text-gray-400`}
          />
        </button>

        {expandedSections.rating && (
          <div className="mt-2 space-y-1">
            {[4, 3, 2, 1].map((rating) => (
              <label key={rating} className="flex items-center gap-2 cursor-pointer">
                <input
                  type="radio"
                  className="text-primary focus:ring-primary dark:bg-card dark:border-border"
                  checked={filters.ratings === rating}
                  onChange={() => handleRatingChange(rating)}
                />
                <div className="flex items-center">
                  <span className="text-sm dark:text-gray-300">{rating}★ & above</span>
                </div>
              </label>
            ))}
          </div>
        )}
      </div>

      {/* Fabric Filter */}
      <div className="border-b dark:border-border pb-4">
        <button
          className="flex items-center justify-between w-full mb-2"
          onClick={() => toggleSection('fabric')}
        >
          <span className="font-medium dark:text-gray-200">Fabric</span>
          <ChevronDown
            size={16}
            className={`transition-transform ${expandedSections.fabric ? 'rotate-180' : ''} dark:text-gray-400`}
          />
        </button>

        {expandedSections.fabric && (
          <div className="mt-2 space-y-1">
            {fabricOptions.map((fabric) => (
              <label key={fabric} className="flex items-center gap-2 cursor-pointer">
                <input
                  type="checkbox"
                  className="rounded text-primary focus:ring-primary dark:bg-card dark:border-border"
                  checked={filters.fabrics.includes(fabric)}
                  onChange={() => handleCheckboxChange('fabrics', fabric)}
                />
                <span className="text-sm dark:text-gray-300">{fabric}</span>
              </label>
            ))}
          </div>
        )}
      </div>

      {/* Pattern Filter */}
      <div className="border-b dark:border-border pb-4">
        <button
          className="flex items-center justify-between w-full mb-2"
          onClick={() => toggleSection('pattern')}
        >
          <span className="font-medium dark:text-gray-200">Pattern</span>
          <ChevronDown
            size={16}
            className={`transition-transform ${expandedSections.pattern ? 'rotate-180' : ''} dark:text-gray-400`}
          />
        </button>

        {expandedSections.pattern && (
          <div className="mt-2 space-y-1">
            {patternOptions.map((pattern) => (
              <label key={pattern} className="flex items-center gap-2 cursor-pointer">
                <input
                  type="checkbox"
                  className="rounded text-primary focus:ring-primary dark:bg-card dark:border-border"
                  checked={filters.patterns.includes(pattern)}
                  onChange={() => handleCheckboxChange('patterns', pattern)}
                />
                <span className="text-sm dark:text-gray-300">{pattern}</span>
              </label>
            ))}
          </div>
        )}
      </div>

      {/* Occasion Filter */}
      <div className="pb-2">
        <button
          className="flex items-center justify-between w-full mb-2"
          onClick={() => toggleSection('occasion')}
        >
          <span className="font-medium dark:text-gray-200">Occasion</span>
          <ChevronDown
            size={16}
            className={`transition-transform ${expandedSections.occasion ? 'rotate-180' : ''} dark:text-gray-400`}
          />
        </button>

        {expandedSections.occasion && (
          <div className="mt-2 space-y-1">
            {occasionOptions.map((occasion) => (
              <label key={occasion} className="flex items-center gap-2 cursor-pointer">
                <input
                  type="checkbox"
                  className="rounded text-primary focus:ring-primary dark:bg-card dark:border-border"
                  checked={filters.occasions.includes(occasion)}
                  onChange={() => handleCheckboxChange('occasions', occasion)}
                />
                <span className="text-sm dark:text-gray-300">{occasion}</span>
              </label>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
