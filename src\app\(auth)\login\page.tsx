"use client";

import React from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Container } from "@/components/ui/container";
import { RoundedInput } from "@/components/ui/rounded-input";
import { Lock, Mail } from "lucide-react";
import { Card, CardHeader, CardContent, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

import { useCustomerAuth } from "@/hooks/use-auth";
import { toast } from "sonner";
import { loginSchemas, loginRateLimiter } from "@/lib/input-validation";

// Use enhanced validation schema
const formSchema = loginSchemas.customer;

type FormValues = z.infer<typeof formSchema>;

export default function LoginPage() {
  const router = useRouter();
  const { login, isLoading, clearError } = useCustomerAuth();

  // Initialize form with react-hook-form and zod validation
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const onSubmit = async (data: FormValues) => {
    // Check rate limiting
    const rateLimitKey = `customer-login-${data.email}`;
    if (!loginRateLimiter.isAllowed(rateLimitKey)) {
      const timeUntilReset = Math.ceil(loginRateLimiter.getTimeUntilReset(rateLimitKey) / 60000);
      toast.error(`Too many login attempts. Please try again in ${timeUntilReset} minutes.`);
      return;
    }

    try {
      clearError();

      await login({
        email: data.email,
        password: data.password,
      });

      // Reset rate limiter on successful login
      loginRateLimiter.reset(rateLimitKey);

      // Show success message
      toast.success("Login successful! Welcome back.");

      // Redirect to home page after successful login
      router.push("/");
    } catch (error) {
      console.error("Login failed:", error);
      const errorMessage = error instanceof Error ? error.message : 'Login failed. Please check your credentials.';
      const remainingAttempts = loginRateLimiter.getRemainingAttempts(rateLimitKey);

      if (remainingAttempts > 0) {
        toast.error(`${errorMessage} (${remainingAttempts} attempts remaining)`);
      } else {
        const timeUntilReset = Math.ceil(loginRateLimiter.getTimeUntilReset(rateLimitKey) / 60000);
        toast.error(`Too many failed attempts. Please try again in ${timeUntilReset} minutes.`);
      }
      // Don't reset form on error - keep user input values
    }
  };

  return (
    <div className="bg-background min-h-screen py-12">
      <Container>
        <div className="max-w-md mx-auto">
          {/* Login Card */}
          <Card className="rounded-xl border-border shadow-sm overflow-hidden">
            {/* Header */}
            <CardHeader className="pt-5 pb-2">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center">
                  <Lock className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <h1 className="text-xl font-semibold text-primary">Sign In</h1>
                  <p className="text-sm text-muted-foreground">Welcome back to your account</p>
                </div>
              </div>
            </CardHeader>

            {/* Form */}
            <CardContent className="p-6 sm:p-8">
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  {/* Email Field */}
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem className="space-y-2">
                        <FormLabel className="text-sm font-medium text-foreground flex items-center">
                          <Mail className="h-4 w-4 text-primary/70 mr-2" />
                          Username or email address <span className="text-destructive">*</span>
                        </FormLabel>
                        <FormControl>
                          <div className="relative">
                            <RoundedInput
                              type="text"
                              placeholder="<EMAIL>"
                              className="w-full"
                              {...field}
                            />
                          </div>
                        </FormControl>
                        <FormMessage className="text-xs" />
                      </FormItem>
                    )}
                  />

                  {/* Password Field */}
                  <FormField
                    control={form.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem className="space-y-2">
                        <FormLabel className="text-sm font-medium text-foreground flex items-center">
                          <Lock className="h-4 w-4 text-primary/70 mr-2" />
                          Password <span className="text-destructive">*</span>
                        </FormLabel>
                        <FormControl>
                          <div className="relative">
                            <RoundedInput
                              type="password"
                              placeholder="••••••••"
                              className="w-full"
                              {...field}
                            />
                          </div>
                        </FormControl>
                        <FormMessage className="text-xs" />
                      </FormItem>
                    )}
                  />

                  {/* Login Button */}
                  <div>
                    <Button
                      type="submit"
                      className="w-full bg-gradient-to-r from-primary to-secondary text-white h-10 font-medium rounded-full disabled:opacity-50 transition-all duration-300 hover:brightness-110 shadow-sm"
                      disabled={isLoading}
                    >
                      {isLoading ? "Logging in..." : "Login"}
                    </Button>
                  </div>

                  {/* Forgot Password Link */}
                  <div className="text-center">
                    <Link href="/forgot-password" className="text-sm text-primary hover:underline">
                      Lost your password?
                    </Link>
                  </div>
                </form>
              </Form>
            </CardContent>

            {/* Register Link */}
            <CardFooter className="p-6 bg-accent/10 dark:bg-accent/5 border-t border-border text-center">
              <p className="text-sm text-muted-foreground w-full">
                Don&apos;t have an account?{" "}
                <Link href="/register" className="text-secondary font-medium hover:underline hover:text-secondary/80 transition-colors duration-300">
                  Register now
                </Link>
              </p>
            </CardFooter>
          </Card>
        </div>
      </Container>
    </div>
  );
}
