"use client";

import React, { useState } from "react";
import Link from "next/link";
import { Container } from "@/components/ui/container";
import { RoundedInput } from "@/components/ui/rounded-input";
import { Lock, Mail } from "lucide-react";
import { <PERSON>, CardHeader, <PERSON><PERSON>ontent, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

export default function LoginPage() {
  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });

  const [errors, setErrors] = useState({
    email: "",
    password: "",
  });

  const [isLoading, setIsLoading] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });

    // Clear error when user types
    if (errors[name as keyof typeof errors]) {
      setErrors({
        ...errors,
        [name]: "",
      });
    }
  };

  const validateForm = () => {
    let isValid = true;
    const newErrors = { ...errors };

    // Email validation
    if (!formData.email) {
      newErrors.email = "Email is required";
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
      isValid = false;
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = "Password is required";
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      // Mock login - would be replaced with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Redirect to home page after successful login
      window.location.href = "/";
    } catch (error) {
      console.error("Login failed:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-background min-h-screen py-12">
      <Container>
        <div className="max-w-md mx-auto">
          {/* Login Card */}
          <Card className="rounded-xl border-border shadow-sm overflow-hidden">
            {/* Header */}
            <CardHeader className="pt-5 pb-2">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center">
                  <Lock className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <h1 className="text-xl font-semibold text-primary">Sign In</h1>
                  <p className="text-sm text-muted-foreground">Welcome back to your account</p>
                </div>
              </div>
            </CardHeader>

            {/* Form */}
            <CardContent className="p-6 sm:p-8">
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Email Field */}
                <div className="space-y-2">
                  <label htmlFor="email" className="text-sm font-medium text-foreground flex items-center">
                    <Mail className="h-4 w-4 text-primary/70 mr-2" />
                    Username or email address <span className="text-destructive">*</span>
                  </label>
                  <div className="relative">
                    <RoundedInput
                      type="text"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      className={`w-full ${errors.email ? 'border-destructive ring-destructive/20' : ''}`}
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>
                  {errors.email && (
                    <p className="text-destructive text-xs mt-1">{errors.email}</p>
                  )}
                </div>

                {/* Password Field */}
                <div className="space-y-2">
                  <label htmlFor="password" className="text-sm font-medium text-foreground flex items-center">
                    <Lock className="h-4 w-4 text-primary/70 mr-2" />
                    Password <span className="text-destructive">*</span>
                  </label>
                  <div className="relative">
                    <RoundedInput
                      type="password"
                      id="password"
                      name="password"
                      value={formData.password}
                      onChange={handleInputChange}
                      className={`w-full ${errors.password ? 'border-destructive ring-destructive/20' : ''}`}
                      placeholder="••••••••"
                      required
                    />
                  </div>
                  {errors.password && (
                    <p className="text-destructive text-xs mt-1">{errors.password}</p>
                  )}
                </div>

                {/* Login Button */}
                <div>
                  <Button
                    type="submit"
                    className="w-full bg-gradient-to-r from-primary to-secondary text-white h-10 font-medium rounded-full disabled:opacity-50 transition-all duration-300 hover:brightness-110 shadow-sm"
                    disabled={isLoading}
                  >
                    {isLoading ? "Logging in..." : "Login"}
                  </Button>
                </div>

                {/* Forgot Password Link */}
                <div className="text-center">
                  <Link href="/forgot-password" className="text-sm text-primary hover:underline">
                    Lost your password?
                  </Link>
                </div>
              </form>
            </CardContent>

            {/* Register Link */}
            <CardFooter className="p-6 bg-accent/10 dark:bg-accent/5 border-t border-border text-center">
              <p className="text-sm text-muted-foreground w-full">
                Don&apos;t have an account?{" "}
                <Link href="/register" className="text-secondary font-medium hover:underline hover:text-secondary/80 transition-colors duration-300">
                  Register now
                </Link>
              </p>
            </CardFooter>
          </Card>
        </div>
      </Container>
    </div>
  );
}
