import { apiClient } from '@/lib/api-client';
import type {
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  RegisterResponse,
  RefreshTokenResponse,
  ForgotPasswordRequest,
  ForgotPasswordResponse,
  ResetPasswordRequest,
  ChangePasswordRequest,
  VerifyEmailRequest,
  VerifyEmailResponse,
  UserType,
} from '@/types/auth';

/**
 * Authentication Service
 * Handles all authentication-related API calls
 */
export class AuthService {
  /**
   * Login user based on user type
   */
  static async login(credentials: LoginRequest, userType: UserType = 'customer'): Promise<LoginResponse> {
    const endpoint = this.getLoginEndpoint(userType);
    const response = await apiClient.post<LoginResponse>(endpoint, {
      email: credentials.email,
      password: credentials.password,
    });

    console.log('Login API response:', response);


    // Store the access token
    if (response.data.accessToken) {
      apiClient.setToken(response.data.accessToken);

      // Store refresh token if provided
      if (response.data.refreshToken && typeof window !== 'undefined') {
        localStorage.setItem('refreshToken', response.data.refreshToken);
      }
    }

    return response;
  }

  /**
   * Register new user
   */
  static async register(data: RegisterRequest): Promise<RegisterResponse> {
    const endpoint = this.getRegisterEndpoint(data.userType || 'customer');
    const response = await apiClient.post<RegisterResponse>(endpoint, data);
    return response;
  }

  /**
   * Logout user
   */
  static async logout(): Promise<void> {
    try {
      const refreshToken = typeof window !== 'undefined'
        ? localStorage.getItem('refreshToken')
        : null;

      if (refreshToken) {
        await apiClient.post('/auth/logout', { refreshToken });
      }
    } catch (error) {
      // Continue with logout even if API call fails
      console.warn('Logout API call failed:', error);
    } finally {
      // Always clear local tokens
      apiClient.clearToken();
    }
  }

  /**
   * Refresh access token
   */
  static async refreshToken(): Promise<RefreshTokenResponse> {
    const refreshToken = typeof window !== 'undefined'
      ? localStorage.getItem('refreshToken')
      : null;

    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await apiClient.post<RefreshTokenResponse>('/auth/refresh', {
      refreshToken,
    });

    // Update stored tokens
    if (response.data.accessToken) {
      apiClient.setToken(response.data.accessToken);

      if (response.data.refreshToken && typeof window !== 'undefined') {
        localStorage.setItem('refreshToken', response.data.refreshToken);
      }
    }

    return response;
  }

  /**
   * Get current user profile
   */
  static async getCurrentUser() {
    const response = await apiClient.get('/auth/profile');
    return response;
  }

  /**
   * Forgot password
   */
  static async forgotPassword(data: ForgotPasswordRequest): Promise<ForgotPasswordResponse> {
    const response = await apiClient.post<ForgotPasswordResponse>('/auth/forgot-password', data);
    return response;
  }

  /**
   * Reset password
   */
  static async resetPassword(data: ResetPasswordRequest): Promise<void> {
    await apiClient.post('/auth/reset-password', data);
  }

  /**
   * Change password
   */
  static async changePassword(data: ChangePasswordRequest): Promise<void> {
    await apiClient.post('/auth/change-password', data);
  }

  /**
   * Verify email
   */
  static async verifyEmail(data: VerifyEmailRequest): Promise<VerifyEmailResponse> {
    const response = await apiClient.post<VerifyEmailResponse>('/auth/verify-email', data);
    return response;
  }

  /**
   * Resend email verification
   */
  static async resendEmailVerification(email: string): Promise<void> {
    await apiClient.post('/auth/resend-verification', { email });
  }

  /**
   * Get login endpoint based on user type
   */
  private static getLoginEndpoint(userType: UserType): string {
    switch (userType) {
      case 'superadmin':
        return '/auth/superadmin/login';
      case 'vendor':
        return '/auth/vendor/login';
      case 'customer':
      default:
        return '/auth/customer/login';
    }
  }

  /**
   * Get register endpoint based on user type
   */
  private static getRegisterEndpoint(userType: UserType): string {
    switch (userType) {
      case 'vendor':
        return '/auth/vendor/register';
      case 'customer':
      default:
        return '/auth/customer/register';
    }
  }

  /**
   * Check if user is authenticated
   */
  static isAuthenticated(): boolean {
    return !!apiClient.getToken();
  }

  /**
   * Get stored access token
   */
  static getAccessToken(): string | null {
    return apiClient.getToken();
  }

  /**
   * Get stored refresh token
   */
  static getRefreshToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('refreshToken');
    }
    return null;
  }
}

// Export default instance
export default AuthService;
