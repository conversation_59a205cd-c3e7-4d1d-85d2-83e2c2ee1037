"use client";

import React from "react";
import Link from "next/link";
import Image from "next/image";
import { Container } from "@/components/ui/container";
import { ArrowRight, Heart, ShoppingCart, Star, Sparkles } from "lucide-react";

// Product type definition
type Product = {
  name: string;
  category: string;
  price: number;
  discount: boolean;
  discountPercentage?: number;
  oldPrice?: number;
  rating: number;
  image?: string;
  id?: string; // Optional ID for product detail page linking
};

type ProductBorderGridProps = {
  title?: string;
  products: Product[];
  shopMoreLink: string;
  sectionType?: "recommended" | "featured" | "bestseller" | "products";
  icon?: React.ReactNode;
  backgroundColor?: string;
};

export const ProductBorderGrid = ({
  title = "Recommended For You",
  products,
  shopMoreLink,
  sectionType = "recommended", // Used for semantic purposes, can be used for conditional styling
  icon,
  backgroundColor = "bg-white",
}: ProductBorderGridProps) => {
  // Default icon is Sparkles if none provided
  const IconComponent = icon || <Sparkles size={20} />;

  return (
    <div className={`${backgroundColor} py-12`}>
      <Container>
        <div className="flex flex-col space-y-6">
          {/* Header with title and shop more link - Desktop */}
          <div className="hidden sm:block">
            <div className="flex items-center w-full relative">
              {/* Horizontal line that spans the entire width */}
              <div className="absolute h-px bg-gray-200 w-full"></div>

              {/* Title with icon - positioned on top of the line */}
              <div className={`flex-shrink-0 flex items-center ${backgroundColor} rounded-full p-1 z-10 mr-4`}>
                <div className="flex items-center gap-2 px-3">
                  <div className="w-6 h-6 flex items-center justify-center text-primary">
                    {IconComponent}
                  </div>
                  <h2 className="text-lg font-bold">{title}</h2>
                </div>
              </div>

              {/* Empty flex-grow div to push the Shop More button to the right */}
              <div className="flex-grow"></div>

              {/* Shop More button - positioned on top of the line */}
              <div className="flex-shrink-0 z-10">
                <Link
                  href={shopMoreLink}
                  className={`flex ${backgroundColor} hover:bg-gradient-to-r hover:from-primary/90 hover:to-secondary/90 hover:text-white text-primary px-3 sm:px-4 py-2 rounded-full items-center gap-1 whitespace-nowrap transition-all text-xs sm:text-sm border border-gray-200`}
                >
                  Shop More <ArrowRight className="h-3 w-3 sm:h-4 sm:w-4" />
                </Link>
              </div>
            </div>
          </div>

          {/* Header with title and shop more link - Mobile */}
          <div className="sm:hidden">
            {/* Standard title layout for mobile */}
            <div className="flex items-center w-full relative">
              {/* Horizontal line that spans the entire width */}
              <div className="absolute h-px bg-gray-200 w-full"></div>

              {/* Title with icon - positioned on top of the line */}
              <div className={`flex-shrink-0 flex items-center ${backgroundColor} rounded-full p-1 z-10 mr-4`}>
                <div className="flex items-center gap-2 px-2">
                  <div className="w-5 h-5 flex items-center justify-center text-primary">
                    {icon || <Sparkles size={16} />}
                  </div>
                  <h2 className="text-base font-bold">{title}</h2>
                </div>
              </div>

              {/* Empty flex-grow div to push the Shop More button to the right */}
              <div className="flex-grow"></div>

              {/* Shop More button - positioned on top of the line */}
              <div className="flex-shrink-0 z-10">
                <Link
                  href={shopMoreLink}
                  className={`flex ${backgroundColor} hover:bg-gradient-to-r hover:from-primary/90 hover:to-secondary/90 hover:text-white text-primary px-2 py-1.5 rounded-full items-center gap-1 whitespace-nowrap transition-all text-xs border border-gray-200`}
                >
                  Shop More <ArrowRight className="h-3 w-3" />
                </Link>
              </div>
            </div>
          </div>

          {/* Products grid - Bordered version */}
          <div className="mt-6">
            <div className="border border-gray-200 rounded-lg overflow-hidden">
              <div className="grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-3 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 divide-x divide-y divide-gray-200">
                {products.slice(0, 10).map((product, index) => (
                  <div key={index} className="group h-full relative">
                    <Link href={`/product/${product.id || `product-${index}`}`} className="block h-full">
                      <div className="p-4">
                        <div className="relative">
                          <div className="h-48 w-full relative overflow-hidden mb-3">
                            <Image
                              src={product.image || `/images/products/${index % 4 === 0 ? 'headphone.png' :
                                                                      index % 4 === 1 ? 'berry.png' :
                                                                      index % 4 === 2 ? 'headphone.png' :
                                                                      'berry.png'}`}
                              alt={product.name}
                              fill
                              className="object-cover w-full h-full transition-transform duration-300 group-hover:scale-105"
                            />
                          </div>
                          <button
                            className="absolute top-3 right-3 bg-white rounded-full p-1.5 shadow-md opacity-80 group-hover:opacity-100 hover:bg-primary hover:text-white transition-all duration-300"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <Heart className="h-3.5 w-3.5" />
                          </button>
                          {product.discount && (
                            <div className="absolute top-3 left-3 bg-red-500 text-white text-xs font-bold px-2 py-0.5 rounded shadow-sm">
                              {product.discountPercentage ? `-${product.discountPercentage}%` : 'SALE'}
                            </div>
                          )}
                        </div>
                        <div>
                          <div className="flex items-center gap-0.5 mb-2">
                            {Array.from({ length: Math.floor(product.rating) }).map((_, i) => (
                              <Star key={i} className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                            ))}
                            {product.rating % 1 !== 0 && (
                              <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" style={{ clipPath: `inset(0 ${100 - (product.rating % 1) * 100}% 0 0)` }} />
                            )}
                            <span className="text-xs text-gray-500 ml-1">{product.rating}</span>
                          </div>
                          <h3 className="font-medium text-gray-900 group-hover:text-primary transition-colors text-sm sm:text-base truncate">{product.name}</h3>
                          <p className="text-gray-500 text-xs mt-1 truncate flex items-center">
                            <span className="inline-block w-2 h-2 rounded-full bg-primary/40 mr-1.5"></span>
                            {product.category}
                          </p>
                          <div className="mt-3 flex items-center justify-between">
                            <div className="flex items-center gap-1.5">
                              <p className="text-primary font-semibold text-sm sm:text-base">${product.price}</p>
                              {product.discount && product.oldPrice && (
                                <p className="text-gray-400 text-xs sm:text-sm line-through">${product.oldPrice}</p>
                              )}
                            </div>
                            <button
                              className="flex items-center justify-center bg-gray-100 hover:bg-gradient-to-r hover:from-[#4E598C] hover:to-[#F8166F] hover:text-white rounded-full p-1.5 transition-colors"
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                // Add to cart functionality would go here
                              }}
                            >
                              <ShoppingCart className="h-3.5 w-3.5" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </Link>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </Container>
    </div>
  );
};
