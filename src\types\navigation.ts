import { LucideIcon } from "lucide-react";

/**
 * Base navigation item interface
 * Represents a basic navigation item with title, URL, and optional icon
 */
export interface BaseNavItem {
  /** The display title of the navigation item */
  title: string;
  /** The URL the navigation item links to */
  url: string;
  /** Optional icon component to display with the navigation item */
  icon?: LucideIcon;
  /** Whether the navigation item is currently active */
  isActive?: boolean;
}

/**
 * Navigation item with optional sub-items
 * Extends BaseNavItem to include optional sub-items for dropdown menus
 */
export interface NavItem extends BaseNavItem {
  /** Optional array of sub-items for dropdown menus */
  items?: BaseNavItem[];
}

/**
 * Dashboard navigation data
 * Contains the navigation items and group label for a dashboard
 */
export interface DashboardNavData {
  /** The main navigation items to display in the sidebar */
  mainNavItems: NavItem[];
  /** The label for the navigation group */
  groupLabel: string;
}

/**
 * User information for dashboard sidebar
 */
export interface DashboardUserInfo {
  /** The display name of the user */
  displayName: string;
  /** The email of the user */
  email: string;
  /** Optional avatar image source */
  avatarSrc?: string;
  /** Fallback text for the avatar when no image is available */
  avatarFallback: string;
  /** URL to redirect to when logging out */
  logoutUrl: string;
}
