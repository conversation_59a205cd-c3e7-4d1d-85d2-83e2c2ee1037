import { 
  LayoutDashboard,
  Store,
  ShoppingBag,
  Users,
  CreditCard,
  Settings,
  BarChart3,
  FileText,
  Bell,
  HelpCircle
} from "lucide-react";
import { DashboardNavData } from "@/types/navigation";

/**
 * Navigation data for superadmin dashboard
 */
export const superadminNavData: DashboardNavData = {
  groupLabel: "Main Navigation",
  mainNavItems: [
    {
      title: "Dashboard",
      url: "/superadmin/dashboard",
      icon: LayoutDashboard,
      isActive: true,
    },
    {
      title: "Stores",
      url: "/superadmin/stores",
      icon: Store,
      items: [
        {
          title: "All Stores",
          url: "/superadmin/stores",
        },
        {
          title: "Pending Approval",
          url: "/superadmin/stores/pending",
        },
        {
          title: "Featured Stores",
          url: "/superadmin/stores/featured",
        }
      ],
    },
    {
      title: "Products",
      url: "/superadmin/products",
      icon: ShoppingBag,
      items: [
        {
          title: "All Products",
          url: "/superadmin/products",
        },
        {
          title: "Categories",
          url: "/superadmin/products/categories",
        },
        {
          title: "Featured Products",
          url: "/superadmin/products/featured",
        }
      ],
    },
    {
      title: "Customers",
      url: "/superadmin/customers",
      icon: Users,
      items: [
        {
          title: "All Customers",
          url: "/superadmin/customers",
        },
        {
          title: "Customer Groups",
          url: "/superadmin/customers/groups",
        }
      ],
    },
    {
      title: "Orders",
      url: "/superadmin/orders",
      icon: CreditCard,
      items: [
        {
          title: "All Orders",
          url: "/superadmin/orders",
        },
        {
          title: "Pending Orders",
          url: "/superadmin/orders/pending",
        },
        {
          title: "Completed Orders",
          url: "/superadmin/orders/completed",
        }
      ],
    },
    {
      title: "Analytics",
      url: "/superadmin/analytics",
      icon: BarChart3,
      items: [
        {
          title: "Sales Overview",
          url: "/superadmin/analytics/sales",
        },
        {
          title: "Store Performance",
          url: "/superadmin/analytics/stores",
        },
        {
          title: "Customer Insights",
          url: "/superadmin/analytics/customers",
        }
      ],
    },
    {
      title: "Reports",
      url: "/superadmin/reports",
      icon: FileText,
      items: [
        {
          title: "Sales Reports",
          url: "/superadmin/reports/sales",
        },
        {
          title: "Revenue Reports",
          url: "/superadmin/reports/revenue",
        },
        {
          title: "User Activity",
          url: "/superadmin/reports/activity",
        }
      ],
    },
    {
      title: "Notifications",
      url: "/superadmin/notifications",
      icon: Bell,
    },
    {
      title: "Settings",
      url: "/superadmin/settings",
      icon: Settings,
      items: [
        {
          title: "General",
          url: "/superadmin/settings/general",
        },
        {
          title: "Security",
          url: "/superadmin/settings/security",
        },
        {
          title: "Appearance",
          url: "/superadmin/settings/appearance",
        },
        {
          title: "Billing",
          url: "/superadmin/settings/billing",
        }
      ],
    },
    {
      title: "Help & Support",
      url: "/superadmin/support",
      icon: HelpCircle,
    },
  ]
};
