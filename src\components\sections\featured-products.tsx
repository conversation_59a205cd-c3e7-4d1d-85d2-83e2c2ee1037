"use client";

import { ProductGrid } from "./product-grid";
import { Award } from "lucide-react";

// Featured products data
const featuredProductsData = [
  { name: "Wireless Earbuds", category: "Electronics", price: 79.99, discount: true, oldPrice: 99.99, rating: 4.5 },
  { name: "Smart Watch", category: "Electronics", price: 149.99, discount: false, rating: 4.8 },
  { name: "Leather Backpack", category: "Fashion", price: 89.99, discount: true, oldPrice: 119.99, rating: 4.2 },
  { name: "Bluetooth Speaker", category: "Electronics", price: 59.99, discount: false, rating: 4.6 },
  { name: "Fitness Tracker", category: "Electronics", price: 49.99, discount: true, oldPrice: 69.99, rating: 4.3 },
  { name: "Stainless Steel Water Bottle", category: "Home & Kitchen", price: 24.99, discount: false, rating: 4.7 },
  { name: "Wireless Charging Pad", category: "Electronics", price: 29.99, discount: true, oldPrice: 39.99, rating: 4.4 },
  { name: "Organic Face Cream", category: "Beauty & Health", price: 34.99, discount: false, rating: 4.9 },
  { name: "Premium Coffee Maker", category: "Home & Kitchen", price: 129.99, discount: true, oldPrice: 159.99, rating: 4.7 },
  { name: "Portable Power Bank", category: "Electronics", price: 39.99, discount: false, rating: 4.5 }
];

type FeaturedProductsProps = {
  title?: string;
  products?: typeof featuredProductsData;
  shopMoreLink?: string;
  sectionType?: "recommended" | "featured";
  backgroundColor?: string;
};

export const FeaturedProducts = ({
  title = "Featured Products",
  products = featuredProductsData,
  shopMoreLink = "/products",
  sectionType = "featured",
  backgroundColor = "bg-[#F7F7F9]"
}: FeaturedProductsProps) => {
  return (
    <ProductGrid
      title={title}
      products={products}
      shopMoreLink={shopMoreLink}
      sectionType={sectionType}
      icon={<Award size={20} />}
      backgroundColor={backgroundColor}
    />
  );
};
