"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Lock,
  Mail,
  ShieldCheck,
  AlertTriangle,
  Eye,
  EyeOff,
  Shield,
  Fingerprint,
  X,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Card,
  CardHeader,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

import { useSuperadminAuth } from "@/hooks/use-auth";
import { toast } from "sonner";
import { loginSchemas, loginRateLimiter } from "@/lib/input-validation";

const loginSchema = loginSchemas.superadmin;

type LoginSchema = z.infer<typeof loginSchema>;

export default function SuperAdminLoginPage() {
  const router = useRouter();
  const { login, isLoading, error, clearError } = useSuperadminAuth();

  const [showPassword, setShowPassword] = useState(false);
  const [ipAddress, setIpAddress] = useState("Loading...");
  const [showWarning, setShowWarning] = useState(true);

  const form = useForm<LoginSchema>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
      rememberMe: false,
    },
    mode: "onSubmit", // Only validate on submit to prevent clearing on error
  });

  useEffect(() => {
    const timer = setTimeout(() => {
      setIpAddress(
        "192.168." +
          Math.floor(Math.random() * 255) +
          "." +
          Math.floor(Math.random() * 255)
      );
    }, 1000);
    return () => clearTimeout(timer);
  }, []);

  const onSubmit = async (data: LoginSchema) => {
    // Check rate limiting
    const rateLimitKey = `superadmin-login-${data.email}`;
    if (!loginRateLimiter.isAllowed(rateLimitKey)) {
      const timeUntilReset = Math.ceil(loginRateLimiter.getTimeUntilReset(rateLimitKey) / 60000);
      toast.error(`Too many login attempts. Please try again in ${timeUntilReset} minutes.`);
      return;
    }

    clearError();
    try {
      await login(data);

      // Reset rate limiter on successful login
      loginRateLimiter.reset(rateLimitKey);

      toast.success("Login successful! Welcome SuperAdmin.");
      router.push("/superadmin/dashboard");
    } catch (err) {
      console.error("Login failed:", err);
      const errorMessage = err instanceof Error ? err.message : 'Login failed. Please check your credentials and try again.';
      const remainingAttempts = loginRateLimiter.getRemainingAttempts(rateLimitKey);

      if (remainingAttempts > 0) {
        toast.error(`${errorMessage} (${remainingAttempts} attempts remaining)`);
      } else {
        const timeUntilReset = Math.ceil(loginRateLimiter.getTimeUntilReset(rateLimitKey) / 60000);
        toast.error(`Too many failed attempts. Please try again in ${timeUntilReset} minutes.`);
      }
      // Don't reset form on error - keep user input values
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen relative">
      {showWarning && (
        <div className="fixed top-0 left-0 w-full bg-red-900/90 text-white py-2 px-4 z-50">
          <div className="container mx-auto flex items-center justify-between">
            <div className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2 text-red-300" />
              <span className="text-sm font-medium">
                RESTRICTED ACCESS: Authorized SuperAdmin personnel only.
                Unauthorized access is prohibited.
              </span>
            </div>
            <button
              onClick={() => setShowWarning(false)}
              className="text-white hover:text-red-300"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}

      <div className="w-full max-w-md px-4 z-10">
        <Card className="border border-gray-800 shadow-2xl rounded-xl overflow-hidden bg-gray-900 text-gray-100">
          <div className="h-1 bg-gradient-to-r from-red-500 via-accent to-primary w-full" />
          <CardHeader className="text-center space-y-3 pt-8">
            <div className="flex justify-center mb-2">
              <div className="p-3 rounded-full bg-gray-800 border border-gray-700">
                <Shield className="h-8 w-8 text-red-500" />
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-center gap-2">
                <h1 className="text-2xl font-bold text-white">
                  SuperAdmin Portal
                </h1>
                <Badge
                  variant="outline"
                  className="bg-red-900/30 text-red-400 border-red-800"
                >
                  <ShieldCheck className="h-3 w-3 mr-1" />
                  Restricted
                </Badge>
              </div>
              <p className="text-gray-400">Secure authentication required</p>
            </div>
          </CardHeader>

          <CardContent className="pt-4">
            <Alert className="mb-6 bg-gray-800 border-red-900 text-gray-300">
              <AlertTriangle className="h-4 w-4 text-red-500" />
              <AlertDescription className="text-xs">
                Your IP address{" "}
                <span className="font-mono text-white">{ipAddress}</span> is
                being logged.
              </AlertDescription>
            </Alert>

            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-5"
              >
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm text-gray-300 flex items-center">
                        <Mail className="h-4 w-4 mr-2 text-red-500" />
                        Email Address
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          className="bg-gray-800 border-gray-700 text-white"
                          placeholder="<EMAIL>"
                        />
                      </FormControl>
                      <FormMessage className="text-red-500 text-xs" />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm text-gray-300 flex items-center">
                        <Lock className="h-4 w-4 mr-2 text-red-500" />
                        Password
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            {...field}
                            type={showPassword ? "text" : "password"}
                            className="bg-gray-800 border-gray-700 text-white pr-10"
                            placeholder="••••••••"
                          />
                          <button
                            type="button"
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                            onClick={() => setShowPassword(!showPassword)}
                          >
                            {showPassword ? (
                              <EyeOff className="h-4 w-4" />
                            ) : (
                              <Eye className="h-4 w-4" />
                            )}
                          </button>
                        </div>
                      </FormControl>
                      <FormMessage className="text-red-500 text-xs" />
                    </FormItem>
                  )}
                />

                {error && (
                  <Alert className="bg-red-900/20 border-red-800 text-red-300">
                    <AlertTriangle className="h-4 w-4 text-red-500" />
                    <AlertDescription className="text-sm">
                      {error}
                    </AlertDescription>
                  </Alert>
                )}

                <FormField
                  control={form.control}
                  name="rememberMe"
                  render={({ field }) => (
                    <FormItem className="flex items-center space-x-2">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={(checked) => field.onChange(checked)}
                          className="border-gray-600 data-[state=checked]:bg-red-600 data-[state=checked]:border-red-600"
                        />
                      </FormControl>
                      <FormLabel className="text-sm text-gray-400">
                        Remember me
                      </FormLabel>
                    </FormItem>
                  )}
                />

                <Button
                  type="submit"
                  className="w-full h-11 bg-gradient-to-r from-red-600 to-red-800 hover:opacity-90 text-white"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <span className="flex items-center">
                      <svg
                        className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        />
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"
                        />
                      </svg>
                      Authenticating...
                    </span>
                  ) : (
                    <span className="flex items-center justify-center">
                      <Fingerprint className="h-4 w-4 mr-2" />
                      Authenticate
                    </span>
                  )}
                </Button>
              </form>
            </Form>

            <div className="mt-6 text-xs text-gray-500 border-t border-gray-800 pt-4">
              <p className="flex items-start">
                <AlertTriangle className="h-3 w-3 mr-1 mt-0.5 text-red-500" />
                This system is for authorized SuperAdmin personnel only.
                Unauthorized access will be logged.
              </p>
            </div>
          </CardContent>
          <CardFooter className="flex justify-center pb-6 pt-2">
            <div className="text-xs text-gray-500">
              © 2023 Multi-Tenant E-commerce.
            </div>
          </CardFooter>
        </Card>
      </div>

      <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2MCIgaGVpZ2h0PSI2MCIgdmlld0JveD0iMCAwIDYwIDYwIj48cGF0aCBkPSJNNTkuOTEgMEg2MHYxNS40M1YwSDU5Ljkxek0wIDE1LjQzIDAgMHYxNS40M3ptMCA0NC41N0wwIDYwaDAuMDlWNDQuNTd6TTU5LjkxIDYwSDYwVjQ0LjU3VjYwSDU5Ljkxek0wIDMwLjAwMUwwIDE1LjQzdjE0LjU3em0wIDE0LjU3TDAgMzAuMDAxdjE0LjU3ek02MCAxNS40M1YzMC4wMDFWMTUuNDN6bTAgMTQuNTdWNDQuNTdWMzAuMDAxeiIgb3BhY2l0eT0iLjAyIiBmaWxsPSIjZmZmIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz48L3N2Zz4=')] opacity-5 z-0"></div>
    </div>
  );
}
