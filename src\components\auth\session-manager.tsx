"use client";

import React, { useEffect, useCallback, useState } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { SessionManager } from '@/lib/secure-storage';
import { AuthService } from '@/services/auth-service';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface SessionManagerProps {
  children: React.ReactNode;
  timeoutMinutes?: number;
  warningMinutes?: number;
  userType?: 'superadmin' | 'vendor' | 'customer';
}

/**
 * Session management component that handles automatic logout
 * and session timeout warnings
 */
export function SessionManagerProvider({
  children,
  timeoutMinutes = 30,
  warningMinutes = 5,
  userType = 'customer'
}: SessionManagerProps) {
  const router = useRouter();
  const [showWarning, setShowWarning] = useState(false);
  const [timeLeft, setTimeLeft] = useState(0);

  const handleLogout = useCallback(async () => {
    try {
      await AuthService.logout();
      toast.info('Session expired. Please log in again.');
      
      // Redirect based on user type
      switch (userType) {
        case 'superadmin':
          router.push('/superadmin/login');
          break;
        case 'vendor':
          router.push('/vendor/login');
          break;
        case 'customer':
        default:
          router.push('/login');
          break;
      }
    } catch (error) {
      console.error('Logout failed:', error);
      // Force redirect even if logout API fails
      switch (userType) {
        case 'superadmin':
          router.push('/superadmin/login');
          break;
        case 'vendor':
          router.push('/vendor/login');
          break;
        case 'customer':
        default:
          router.push('/login');
          break;
      }
    }
  }, [router, userType]);

  const handleWarning = useCallback(() => {
    setShowWarning(true);
    setTimeLeft(warningMinutes * 60); // Convert to seconds
    
    // Start countdown
    const countdownInterval = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          clearInterval(countdownInterval);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    // Auto-logout when countdown reaches 0
    setTimeout(() => {
      clearInterval(countdownInterval);
      setShowWarning(false);
      handleLogout();
    }, warningMinutes * 60 * 1000);
  }, [warningMinutes, handleLogout]);

  const extendSession = useCallback(() => {
    setShowWarning(false);
    setTimeLeft(0);
    SessionManager.clearTimeout();
    
    // Restart session timeout
    SessionManager.startTimeout(
      timeoutMinutes * 60 * 1000,
      warningMinutes * 60 * 1000,
      handleWarning,
      handleLogout
    );
    
    toast.success('Session extended successfully.');
  }, [timeoutMinutes, warningMinutes, handleWarning, handleLogout]);

  const handleUserActivity = useCallback(() => {
    // Reset session timeout on user activity
    SessionManager.resetTimeout();
    SessionManager.startTimeout(
      timeoutMinutes * 60 * 1000,
      warningMinutes * 60 * 1000,
      handleWarning,
      handleLogout
    );
  }, [timeoutMinutes, warningMinutes, handleWarning, handleLogout]);

  useEffect(() => {
    // Check if user is authenticated
    const isAuthenticated = AuthService.isAuthenticated();
    
    if (isAuthenticated) {
      // Start session timeout
      SessionManager.startTimeout(
        timeoutMinutes * 60 * 1000,
        warningMinutes * 60 * 1000,
        handleWarning,
        handleLogout
      );

      // Add event listeners for user activity
      const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
      
      events.forEach(event => {
        document.addEventListener(event, handleUserActivity, true);
      });

      // Cleanup function
      return () => {
        SessionManager.clearTimeout();
        events.forEach(event => {
          document.removeEventListener(event, handleUserActivity, true);
        });
      };
    }
  }, [timeoutMinutes, warningMinutes, handleWarning, handleLogout, handleUserActivity]);

  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return (
    <>
      {children}
      
      <AlertDialog open={showWarning} onOpenChange={setShowWarning}>
        <AlertDialogContent className="sm:max-w-md">
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <span className="text-amber-500">⚠️</span>
              Session Timeout Warning
            </AlertDialogTitle>
            <AlertDialogDescription className="space-y-2">
              <p>Your session will expire soon due to inactivity.</p>
              <p className="font-medium text-foreground">
                Time remaining: <span className="text-destructive font-mono">{formatTime(timeLeft)}</span>
              </p>
              <p className="text-sm text-muted-foreground">
                Click "Stay Logged In" to extend your session, or you will be automatically logged out.
              </p>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex-col sm:flex-row gap-2">
            <AlertDialogCancel 
              onClick={handleLogout}
              className="w-full sm:w-auto"
            >
              Log Out Now
            </AlertDialogCancel>
            <AlertDialogAction 
              onClick={extendSession}
              className="w-full sm:w-auto bg-primary hover:bg-primary/90"
            >
              Stay Logged In
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}

/**
 * Hook to manually trigger session extension
 */
export function useSessionManager() {
  const extendSession = useCallback(() => {
    SessionManager.resetTimeout();
    toast.success('Session extended.');
  }, []);

  const checkSessionStatus = useCallback(() => {
    return AuthService.isAuthenticated();
  }, []);

  return {
    extendSession,
    checkSessionStatus,
  };
}
