"use client";

import React from "react";
import Link from "next/link";
import Image from "next/image";
import {
  X,
  Home,
  ShoppingBag,
  Layers,
  FileText,
  MessageSquare,
  User,
  Heart,
  ShoppingCart,
  Phone,
  Mail,
  Store
} from "lucide-react";

export function MobileMenu({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 bg-black bg-opacity-50">
      <div className="fixed inset-y-0 left-0 w-[85%] max-w-[350px] sm:max-w-[400px] bg-card shadow-xl transform transition-transform duration-300 ease-in-out">
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="p-3 sm:p-4 border-b border-gray-100 flex justify-between items-center">
            <Link href="/" className="flex items-center" onClick={onClose}>
              <Image
                src="/logo.svg"
                alt="E-Come Logo"
                width={120}
                height={30}
                className="w-auto h-6 sm:h-7"
                priority
              />
            </Link>
            <button
              onClick={onClose}
              className="p-1.5 sm:p-2 rounded-full hover:bg-gray-100 text-gray-500 transition-colors"
            >
              <X className="h-4 w-4 sm:h-5 sm:w-5" />
            </button>
          </div>

          {/* Navigation */}
          <div className="flex-1 overflow-y-auto p-3 sm:p-4">
            <nav className="space-y-0.5 sm:space-y-1">
              <Link
                href="/"
                className="flex items-center gap-2 sm:gap-3 px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg hover:bg-gray-50 text-gray-700 hover:text-primary transition-colors"
                onClick={onClose}
              >
                <Home className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                <span className="font-medium text-sm sm:text-base">Home</span>
              </Link>

              <Link
                href="/shop"
                className="flex items-center gap-2 sm:gap-3 px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg hover:bg-gray-50 text-gray-700 hover:text-primary transition-colors"
                onClick={onClose}
              >
                <ShoppingBag className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                <span className="font-medium text-sm sm:text-base">Shop</span>
                <span className="ml-auto text-[9px] sm:text-[10px] bg-gradient-secondary text-white px-1 sm:px-1.5 py-0.5 rounded shadow-sm font-medium">
                  NEW
                </span>
              </Link>

              <Link
                href="/stores"
                className="flex items-center gap-2 sm:gap-3 px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg hover:bg-gray-50 text-gray-700 hover:text-primary transition-colors"
                onClick={onClose}
              >
                <Store className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                <span className="font-medium text-sm sm:text-base">Stores</span>
              </Link>

              <Link
                href="/collections"
                className="flex items-center gap-2 sm:gap-3 px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg hover:bg-gray-50 text-gray-700 hover:text-primary transition-colors"
                onClick={onClose}
              >
                <Layers className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                <span className="font-medium text-sm sm:text-base">Collections</span>
              </Link>

              <Link
                href="/pages"
                className="flex items-center gap-2 sm:gap-3 px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg hover:bg-gray-50 text-gray-700 hover:text-primary transition-colors"
                onClick={onClose}
              >
                <FileText className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                <span className="font-medium text-sm sm:text-base">Pages</span>
              </Link>

              <Link
                href="/blog"
                className="flex items-center gap-2 sm:gap-3 px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg hover:bg-gray-50 text-gray-700 hover:text-primary transition-colors"
                onClick={onClose}
              >
                <MessageSquare className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                <span className="font-medium text-sm sm:text-base">Blog</span>
              </Link>
            </nav>

            <div className="mt-4 sm:mt-6 pt-4 sm:pt-6 border-t border-gray-100">
              <h3 className="text-[10px] sm:text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2 sm:mb-3 px-3 sm:px-4">Account</h3>
              <nav className="space-y-0.5 sm:space-y-1">
                <Link
                  href="/account"
                  className="flex items-center gap-2 sm:gap-3 px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg hover:bg-gray-50 text-gray-700 hover:text-primary transition-colors"
                  onClick={onClose}
                >
                  <User className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                  <span className="font-medium text-sm sm:text-base">My Account</span>
                </Link>

                <Link
                  href="/wishlist"
                  className="flex items-center gap-2 sm:gap-3 px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg hover:bg-gray-50 text-gray-700 hover:text-primary transition-colors"
                  onClick={onClose}
                >
                  <Heart className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                  <span className="font-medium text-sm sm:text-base">Wishlist</span>
                </Link>

                <Link
                  href="/cart"
                  className="flex items-center gap-2 sm:gap-3 px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg hover:bg-gray-50 text-gray-700 hover:text-primary transition-colors"
                  onClick={onClose}
                >
                  <ShoppingCart className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                  <span className="font-medium text-sm sm:text-base">Cart</span>
                  <span className="ml-auto flex items-center justify-center w-4 h-4 sm:w-5 sm:h-5 bg-primary text-white text-[9px] sm:text-[10px] rounded-full shadow-sm font-medium">
                    3
                  </span>
                </Link>
              </nav>
            </div>

            <div className="mt-4 sm:mt-6 pt-4 sm:pt-6 border-t border-gray-100">
              <h3 className="text-[10px] sm:text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2 sm:mb-3 px-3 sm:px-4">Contact Us</h3>
              <div className="space-y-2 sm:space-y-3 px-3 sm:px-4">
                <div className="flex items-center gap-2 sm:gap-3">
                  <Phone className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-gray-400" />
                  <span className="text-xs sm:text-sm">(+123) 456 789</span>
                </div>
                <div className="flex items-center gap-2 sm:gap-3">
                  <Mail className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-gray-400" />
                  <span className="text-xs sm:text-sm"><EMAIL></span>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="p-3 sm:p-4 border-t border-gray-100 space-y-2">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-[10px] sm:text-xs font-semibold text-gray-500 uppercase tracking-wider">Customer</h3>
            </div>
            <div className="flex gap-2 mb-3">
              {/* Customer Login */}
              <Link
                href="/login"
                className="flex-1 flex items-center justify-center py-2 sm:py-2.5 border-2 border-primary text-primary rounded-full text-xs sm:text-sm font-medium transition-all duration-300 hover:bg-primary hover:text-white hover:shadow-md"
                onClick={onClose}
              >
                Login
              </Link>

              {/* Customer Registration */}
              <Link
                href="/register"
                className="flex-1 flex items-center justify-center py-2 sm:py-2.5 bg-gradient-angle-accent text-white rounded-full text-xs sm:text-sm font-medium shadow-sm transition-all duration-300 hover:shadow-lg hover:brightness-110"
                onClick={onClose}
              >
                Register
              </Link>
            </div>

            <div className="flex items-center justify-between mb-2">
              <h3 className="text-[10px] sm:text-xs font-semibold text-gray-500 uppercase tracking-wider">Vendor</h3>
            </div>
            <div className="flex gap-2 mb-3">
              {/* Vendor Login */}
              <Link
                href="/vendor/login"
                className="flex-1 flex items-center justify-center py-2 sm:py-2.5 border-2 border-secondary text-secondary rounded-full text-xs sm:text-sm font-medium transition-all duration-300 hover:bg-secondary hover:text-secondary-foreground hover:shadow-md"
                onClick={onClose}
              >
                Login
              </Link>

              {/* Vendor Registration */}
              <Link
                href="/vendor/register"
                className="flex-1 flex items-center justify-center py-2 sm:py-2.5 bg-gradient-angle-secondary text-secondary-foreground rounded-full text-xs sm:text-sm font-medium shadow-sm transition-all duration-300 hover:shadow-lg hover:brightness-110"
                onClick={onClose}
              >
                Register
              </Link>
            </div>

            {/* SuperAdmin Link - Small and Subtle */}
            <div className="text-center">
              <Link
                href="/superadmin/login"
                className="text-gray-400 text-[10px] hover:text-primary transition-colors duration-200"
                onClick={onClose}
              >
                Admin Login
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
