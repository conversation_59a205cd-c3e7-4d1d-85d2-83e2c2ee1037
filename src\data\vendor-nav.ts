import {
  LayoutDashboard,
  Package,
  ShoppingBag,
  Users,
  CreditCard,
  Settings,
  BarChart3,
  FileText,
  MessageSquare,
  Truck,
  Tag
} from "lucide-react";
import { DashboardNavData } from "@/types/navigation";

/**
 * Navigation data for vendor dashboard
 */
export const vendorNavData: DashboardNavData = {
  groupLabel: "Store Management",
  mainNavItems: [
    {
      title: "Dashboard",
      url: "/vendor/dashboard",
      icon: LayoutDashboard,
      isActive: true,
    },
    {
      title: "Products",
      url: "/vendor/products",
      icon: Package,
      items: [
        {
          title: "All Products",
          url: "/vendor/products",
        },
        {
          title: "Add New Product",
          url: "/vendor/products/new",
        },
        {
          title: "Categories",
          url: "/vendor/products/categories",
        },
        {
          title: "Inventory",
          url: "/vendor/products/inventory",
        }
      ],
    },
    {
      title: "Orders",
      url: "/vendor/orders",
      icon: ShoppingBag,
      items: [
        {
          title: "All Orders",
          url: "/vendor/orders",
        },
        {
          title: "Pending",
          url: "/vendor/orders/pending",
        },
        {
          title: "Processing",
          url: "/vendor/orders/processing",
        },
        {
          title: "Completed",
          url: "/vendor/orders/completed",
        },
        {
          title: "Cancelled",
          url: "/vendor/orders/cancelled",
        }
      ],
    },
    {
      title: "Customers",
      url: "/vendor/customers",
      icon: Users,
      items: [
        {
          title: "All Customers",
          url: "/vendor/customers",
        },
        {
          title: "Customer Reviews",
          url: "/vendor/customers/reviews",
        }
      ],
    },
    {
      title: "Shipping",
      url: "/vendor/shipping",
      icon: Truck,
      items: [
        {
          title: "Shipping Methods",
          url: "/vendor/shipping/methods",
        },
        {
          title: "Shipping Zones",
          url: "/vendor/shipping/zones",
        }
      ],
    },
    {
      title: "Promotions",
      url: "/vendor/promotions",
      icon: Tag,
      items: [
        {
          title: "Discounts",
          url: "/vendor/promotions/discounts",
        },
        {
          title: "Coupons",
          url: "/vendor/promotions/coupons",
        },
        {
          title: "Special Offers",
          url: "/vendor/promotions/offers",
        }
      ],
    },
    {
      title: "Finances",
      url: "/vendor/finances",
      icon: CreditCard,
      items: [
        {
          title: "Earnings",
          url: "/vendor/finances/earnings",
        },
        {
          title: "Payouts",
          url: "/vendor/finances/payouts",
        },
        {
          title: "Statements",
          url: "/vendor/finances/statements",
        }
      ],
    },
    {
      title: "Reports",
      url: "/vendor/reports",
      icon: FileText,
      items: [
        {
          title: "Sales Reports",
          url: "/vendor/reports/sales",
        },
        {
          title: "Inventory Reports",
          url: "/vendor/reports/inventory",
        },
        {
          title: "Tax Reports",
          url: "/vendor/reports/tax",
        }
      ],
    },
    {
      title: "Messages",
      url: "/vendor/messages",
      icon: MessageSquare,
    },
    {
      title: "Settings",
      url: "/vendor/settings",
      icon: Settings,
      items: [
        {
          title: "Store Profile",
          url: "/vendor/settings/profile",
        },
        {
          title: "Account",
          url: "/vendor/settings/account",
        },
        {
          title: "Payment Methods",
          url: "/vendor/settings/payments",
        },
        {
          title: "Notification Preferences",
          url: "/vendor/settings/notifications",
        }
      ],
    },
  ]
};
