@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: #FFFFFF;
  --foreground: #000000;
  --card: #FFFFFF;
  --card-foreground: #000000;
  --popover: #FFFFFF;
  --popover-foreground: #000000;
  --primary: #052E7F;
  --primary-foreground: #FFFFFF;
  --secondary: #2A928F;
  --secondary-foreground: #FFFFFF;
  --muted: #F2F2F2;
  --muted-foreground: #6B7280;
  --accent: #2A928F;
  --accent-foreground: #FFFFFF;
  --destructive: #EF4444;
  --border: #E5E7EB;
  --input: #E5E7EB;
  --ring: #052E7F;
  --chart-1: #052E7F;
  --chart-2: #2A928F;
  --chart-3: #C084FC;
  --chart-4: #FBBF24;
  --chart-5: #34D399;
  --sidebar: #F9FAFB;
  --sidebar-foreground: #111827;
  --sidebar-primary: #052E7F;
  --sidebar-primary-foreground: #FFFFFF;
  --sidebar-accent: #2A928F;
  --sidebar-accent-foreground: #FFFFFF;
  --sidebar-border: #E5E7EB;
  --sidebar-ring: #052E7F;
}

.dark {
  /* Base colors */
  --background: #0F172A;
  --foreground: #F8FAFC;
  --card: #1E293B;
  --card-foreground: #F8FAFC;
  --popover: #1E293B;
  --popover-foreground: #F8FAFC;

  /* Brand colors - adjusted for dark mode but maintaining brand identity */
  --primary: #1A56DB; /* Darker shade of primary that's still visible in dark mode */
  --primary-foreground: #FFFFFF;
  --secondary: #2A928F; /* Keep consistent with brand */
  --secondary-foreground: #FFFFFF;
  --accent: #D9F7F5; /* Keep consistent with brand */
  --accent-foreground: #052E7F;

  /* UI colors */
  --muted: #334155;
  --muted-foreground: #94A3B8;
  --destructive: #EF4444;
  --destructive-foreground: #FFFFFF;
  --border: #334155;
  --input: #334155;
  --ring: #1A56DB;

  /* Chart colors */
  --chart-1: #1A56DB;
  --chart-2: #2A928F;
  --chart-3: #A78BFA;
  --chart-4: #FBBF24;
  --chart-5: #34D399;

  /* Sidebar colors */
  --sidebar: #0F172A;
  --sidebar-foreground: #F8FAFC;
  --sidebar-primary: #1A56DB;
  --sidebar-primary-foreground: #FFFFFF;
  --sidebar-accent: #2A928F;
  --sidebar-accent-foreground: #FFFFFF;
  --sidebar-border: #334155;
  --sidebar-ring: #1A56DB;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Carousel indicator styles */
.carousel-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #D9D9D9;
  transition: all 0.3s ease;
}

.carousel-dot-active {
  width: 24px;
  border-radius: 4px;
  background-color: var(--primary);
}

.carousel-dot-before {
  background-color: var(--primary);
  opacity: 0.5;
}

.carousel-dot-after {
  background-color: #D9D9D9;
}