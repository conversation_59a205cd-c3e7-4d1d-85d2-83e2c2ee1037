"use client";

import React, { useState, useEffect } from "react";
import { TopBar } from "./top-bar";
import { MainHeader } from "./main-header";
import { NavBar } from "./nav-bar";
import { SheetCart } from "./sheet-cart";

export function Header() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isCartOpen, setIsCartOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 100);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const handleOpenCart = () => {
    setIsCartOpen(true);
  };

  const handleCloseCart = () => {
    setIsCartOpen(false);
  };

  return (
    <header className="bg-card shadow-sm dark:shadow-none dark:border-b dark:border-border">
      <TopBar />
      <div className={`hidden md:block fixed top-0 left-0 right-0 z-50 transform transition-transform duration-300 bg-card shadow-md dark:shadow-none dark:border-b dark:border-border ${isScrolled ? 'translate-y-0' : '-translate-y-full'}`}>
        <MainHeader onCartClick={handleOpenCart} />
      </div>
      <MainHeader onCartClick={handleOpenCart} />
      <NavBar />
      <SheetCart isOpen={isCartOpen} onClose={handleCloseCart} />
    </header>
  );
}


