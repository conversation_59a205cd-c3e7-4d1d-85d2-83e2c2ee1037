# API Integration Documentation

This document provides comprehensive information about the API integration setup for the multi-tenant ecommerce application.

## Overview

The application uses **Axios** as the HTTP client for communicating with the Nest.js backend. The integration follows clean architecture principles with proper error handling, authentication, and TypeScript support.

## Architecture

### API Client (`src/lib/api-client.ts`)

The `ApiClient` class provides:
- **<PERSON>ton Pattern**: Single instance for consistent configuration
- **Request/Response Interceptors**: Automatic token handling and error processing
- **TypeScript Support**: Fully typed requests and responses
- **Error Handling**: Centralized error formatting and handling
- **Token Management**: Automatic token storage and retrieval

### Authentication Service (`src/services/auth-service.ts`)

Handles all authentication-related API calls:
- Login for different user types (superadmin, vendor, customer)
- Registration
- Token refresh
- Password reset
- Email verification

### Custom Hooks (`src/hooks/use-auth.ts`)

React hooks for authentication state management:
- `useAuth()`: General authentication hook
- `useSuperadminAuth()`: Superadmin-specific authentication
- `useVendorAuth()`: Vendor-specific authentication
- `useCustomerAuth()`: Customer-specific authentication

## Configuration

### Environment Variables

Create a `.env.local` file in the project root:

```env
NEXT_PUBLIC_API_URL=http://localhost:4000
NODE_ENV=development
```

### API Base URL

The API client automatically uses the environment variable `NEXT_PUBLIC_API_URL` or defaults to `http://localhost:4000`.

## Authentication Endpoints

### Superadmin Login
- **Endpoint**: `POST /auth/superadmin/login`
- **Body**: `{ email: string, password: string }`
- **Response**: `{ user: User, accessToken: string, refreshToken?: string, expiresIn: number }`

### Vendor Login
- **Endpoint**: `POST /auth/vendor/login`
- **Body**: `{ email: string, password: string }`
- **Response**: `{ user: User, accessToken: string, refreshToken?: string, expiresIn: number }`

### Customer Login
- **Endpoint**: `POST /auth/customer/login`
- **Body**: `{ email: string, password: string }`
- **Response**: `{ user: User, accessToken: string, refreshToken?: string, expiresIn: number }`

## Usage Examples

### Basic Login Implementation

```tsx
import { useSuperadminAuth } from '@/hooks/use-auth';

function LoginComponent() {
  const { login, isLoading, error, clearError } = useSuperadminAuth();

  const handleSubmit = async (credentials) => {
    try {
      await login(credentials);
      // Redirect on success
    } catch (err) {
      // Error is automatically handled by the hook
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      {error && <div className="error">{error}</div>}
      {/* Form fields */}
      <button disabled={isLoading}>
        {isLoading ? 'Logging in...' : 'Login'}
      </button>
    </form>
  );
}
```

### Direct API Calls

```tsx
import { AuthService } from '@/services/auth-service';

// Login
const response = await AuthService.login({
  email: '<EMAIL>',
  password: 'password123'
}, 'superadmin');

// Get current user
const user = await AuthService.getCurrentUser();

// Logout
await AuthService.logout();
```

## Error Handling

### Automatic Error Handling

The API client automatically handles:
- **401 Unauthorized**: Clears tokens and redirects to appropriate login page
- **Network Errors**: Formats error messages for display
- **API Errors**: Extracts and formats error messages from API responses

### Custom Error Handling

```tsx
try {
  await AuthService.login(credentials, 'superadmin');
} catch (error) {
  // Error message is already formatted
  console.error('Login failed:', error.message);
}
```

## Token Management

### Automatic Token Handling

- **Storage**: Tokens are automatically stored in localStorage
- **Injection**: Access tokens are automatically added to request headers
- **Refresh**: Token refresh logic is built-in (implement as needed)
- **Cleanup**: Tokens are cleared on logout or 401 errors

### Manual Token Operations

```tsx
import { apiClient } from '@/lib/api-client';

// Set token
apiClient.setToken('your-access-token');

// Get token
const token = apiClient.getToken();

// Clear token
apiClient.clearToken();
```

## TypeScript Types

All API-related types are defined in `src/types/auth.ts`:

- `LoginRequest`
- `LoginResponse`
- `User`
- `UserType`
- `RegisterRequest`
- `RegisterResponse`

## Best Practices

### 1. Use Hooks for Components

Always use the authentication hooks in React components for automatic state management:

```tsx
const { login, isLoading, error } = useSuperadminAuth();
```

### 2. Handle Loading States

Always show loading indicators during API calls:

```tsx
<button disabled={isLoading}>
  {isLoading ? 'Processing...' : 'Submit'}
</button>
```

### 3. Display Error Messages

Always display API errors to users:

```tsx
{error && (
  <Alert variant="destructive">
    <AlertDescription>{error}</AlertDescription>
  </Alert>
)}
```

### 4. Clear Errors on User Input

Clear errors when users start typing:

```tsx
const handleInputChange = (e) => {
  // Update form data
  setFormData({ ...formData, [e.target.name]: e.target.value });
  
  // Clear API error
  if (error) {
    clearError();
  }
};
```

## Security Considerations

1. **HTTPS**: Always use HTTPS in production
2. **Token Storage**: Consider using secure storage for sensitive tokens
3. **CORS**: Configure CORS properly on the backend
4. **Validation**: Always validate data on both client and server
5. **Rate Limiting**: Implement rate limiting on the backend

## Testing

### Unit Tests

Test API services and hooks:

```tsx
import { AuthService } from '@/services/auth-service';

test('should login successfully', async () => {
  const response = await AuthService.login({
    email: '<EMAIL>',
    password: 'password123'
  }, 'superadmin');
  
  expect(response.user).toBeDefined();
  expect(response.accessToken).toBeDefined();
});
```

### Integration Tests

Test the complete authentication flow with mock API responses.

## Troubleshooting

### Common Issues

1. **CORS Errors**: Ensure backend CORS is configured for your frontend domain
2. **401 Errors**: Check if tokens are being sent correctly
3. **Network Errors**: Verify API URL and network connectivity
4. **Type Errors**: Ensure all types are properly imported and used

### Debug Mode

Enable debug logging by setting environment variable:

```env
NODE_ENV=development
```

This will log API requests and responses to the console.
