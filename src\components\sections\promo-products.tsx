"use client";

import React from "react";
import Link from "next/link";
import { Container } from "@/components/ui/container";
import { ArrowRight } from "lucide-react";

export function PromoProducts() {
  return (
    <div className="py-6 sm:py-8">
      <Container>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
          {/* First Promo Product */}
          <div className="bg-white rounded-xl overflow-hidden shadow-sm p-6">
            <div className="flex flex-col md:flex-row items-center justify-between">
              <div className="mb-4 md:mb-0">
                <h3 className="text-lg font-medium">The pro stage for</h3>
                <h4 className="text-lg font-medium">your home</h4>
                <div className="mt-3">
                  <span className="text-xs text-gray-500">From</span>
                  <div className="text-lg font-semibold text-pink-500">69.99<span className="text-xs align-top">$</span></div>
                </div>
                <Link
                  href="/shop/electronics/speakers"
                  className="inline-flex items-center text-sm text-pink-500 hover:text-pink-600 mt-2"
                >
                  Shop now <ArrowRight className="ml-1 h-3 w-3" />
                </Link>
              </div>
              <div className="relative w-40 h-40 md:w-48 md:h-48">
                <div className="bg-gradient-to-r from-amber-100 to-amber-200 w-full h-full rounded-lg flex items-center justify-center">
                  <div className="w-3/4 h-3/4 bg-amber-800 rounded-lg relative">
                    <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-1/3 h-4/5 bg-black rounded-r-lg"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Second Promo Product */}
          <div className="bg-white rounded-xl overflow-hidden shadow-sm p-6">
            <div className="flex flex-col md:flex-row items-center justify-between">
              <div className="mb-4 md:mb-0">
                <h3 className="text-lg font-medium">Smart speaker for</h3>
                <h4 className="text-lg font-medium">music lovers</h4>
                <div className="mt-3">
                  <span className="text-xs text-gray-500">From</span>
                  <div className="text-lg font-semibold text-pink-500">39.99<span className="text-xs align-top">$</span></div>
                </div>
                <Link
                  href="/shop/electronics/smart-speakers"
                  className="inline-flex items-center text-sm text-pink-500 hover:text-pink-600 mt-2"
                >
                  Shop now <ArrowRight className="ml-1 h-3 w-3" />
                </Link>
              </div>
              <div className="relative w-40 h-40 md:w-48 md:h-48">
                <div className="bg-gradient-to-r from-gray-800 to-gray-900 w-full h-full rounded-full flex items-center justify-center">
                  <div className="w-3/4 h-3/4 bg-black rounded-full relative overflow-hidden">
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-1/2 h-1/2 rounded-full bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 animate-pulse"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Container>
    </div>
  );
}
