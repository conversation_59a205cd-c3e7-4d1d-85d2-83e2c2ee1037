"use client";

import React, { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import {
  ShoppingCart, Plus, Minus, ChevronRight,
  Trash2, ArrowRight, ShoppingBag
} from "lucide-react";
import {
  Sheet,
  SheetContent
} from "@/components/ui/sheet";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader
} from "@/components/ui/card";

// Mock cart data
const cartItems = [
  {
    id: 1,
    name: "Wireless Bluetooth Headphones",
    price: 79.99,
    originalPrice: 99.99,
    quantity: 1,
    image: "/images/products/headphones.jpg",
    vendor: "AudioTech",
  },
  {
    id: 2,
    name: "Smart Watch Series 5",
    price: 199.99,
    originalPrice: 199.99,
    quantity: 1,
    image: "/images/products/smartwatch.jpg",
    vendor: "TechGear",
  },
  {
    id: 3,
    name: "Laptop Sleeve Case",
    price: 29.99,
    originalPrice: 39.99,
    quantity: 1,
    image: "/images/products/laptop-sleeve.jpg",
    vendor: "BagWorld",
  },
];

export function SheetCart({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) {
  const subtotal = cartItems.reduce((total, item) => total + item.price * item.quantity, 0);
  const shipping = 4.99;
  const tax = subtotal * 0.08;
  const total = subtotal + shipping + tax;
  const itemCount = cartItems.reduce((count, item) => count + item.quantity, 0);
  const [quantities, setQuantities] = useState<Record<number, number>>(
    cartItems.reduce((acc, item) => ({ ...acc, [item.id]: item.quantity }), {})
  );

  const updateQuantity = (id: number, newQuantity: number) => {
    if (newQuantity >= 1) {
      setQuantities({ ...quantities, [id]: newQuantity });
    }
  };

  const removeItem = (id: number) => {
    // In a real app, this would remove the item from the cart
    console.log(`Remove item ${id}`);
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-[90%] max-w-[420px] bg-gradient-to-b from-background to-muted p-0 sm:max-w-[420px] border-l border-border">
        {/* Header */}
        <div className="px-6 py-5 flex justify-between items-center border-b border-border">
          <div className="flex items-center gap-3">
            <div className="relative">
              <div className="h-10 w-10 rounded-full bg-gradient-to-r from-primary/20 to-primary/10 flex items-center justify-center">
                <ShoppingCart className="h-5 w-5 text-primary" />
              </div>
              {itemCount > 0 && (
                <span className="absolute -top-1 -right-1 flex items-center justify-center w-5 h-5 bg-accent text-accent-foreground text-[10px] rounded-full font-medium">
                  {itemCount}
                </span>
              )}
            </div>
            <div>
              <h3 className="font-bold text-lg text-foreground">Shopping Cart</h3>
              <p className="text-xs text-muted-foreground">{itemCount} {itemCount === 1 ? 'item' : 'items'}</p>
            </div>
          </div>
        </div>

        {/* Cart Content */}
        <div className="flex-1 overflow-y-auto h-[calc(100vh-180px)]">
          {cartItems.length > 0 ? (
            <>
              {/* Cart Items */}
              <div className="px-6 py-4">
                <div className="space-y-4">
                  {cartItems.map((item) => (
                    <Card
                      key={item.id}
                      className="bg-card rounded-xl border border-border hover:border-primary/20 transition-colors shadow-none p-0 overflow-hidden"
                    >
                      <CardContent className="p-3 flex gap-3 items-start">
                        <div className="w-[72px] h-[72px] bg-muted rounded-lg overflow-hidden relative flex-shrink-0">
                          <Image
                            src={item.image}
                            alt={item.name}
                            fill
                            className="object-cover"
                          />
                          {item.originalPrice > item.price && (
                            <div className="absolute top-0 left-0 bg-accent text-white text-[9px] px-1.5 py-0.5 font-medium rounded-br-md">
                              SALE
                            </div>
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex justify-between items-start">
                            <div>
                              <h4 className="text-sm font-medium text-card-foreground line-clamp-2">{item.name}</h4>
                              <p className="text-xs text-muted-foreground mt-0.5">Vendor: {item.vendor}</p>
                            </div>
                            <button
                              onClick={() => removeItem(item.id)}
                              className="text-muted-foreground hover:text-red-500 transition-colors p-1"
                              aria-label="Remove item"
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </div>
                          <div className="flex justify-between items-center mt-2">
                            <div className="flex items-center border border-border rounded-full h-7">
                              <button
                                onClick={() => updateQuantity(item.id, quantities[item.id] - 1)}
                                className="w-7 h-7 flex items-center justify-center text-muted-foreground hover:text-primary transition-colors"
                                aria-label="Decrease quantity"
                              >
                                <Minus className="h-3 w-3" />
                              </button>
                              <span className="w-8 text-center text-sm">{quantities[item.id]}</span>
                              <button
                                onClick={() => updateQuantity(item.id, quantities[item.id] + 1)}
                                className="w-7 h-7 flex items-center justify-center text-muted-foreground hover:text-primary transition-colors"
                                aria-label="Increase quantity"
                              >
                                <Plus className="h-3 w-3" />
                              </button>
                            </div>
                            <div className="text-right">
                              <div className="text-sm font-medium text-card-foreground">${(item.price * quantities[item.id]).toFixed(2)}</div>
                              {item.originalPrice > item.price && (
                                <div className="text-xs text-muted-foreground line-through">${(item.originalPrice * quantities[item.id]).toFixed(2)}</div>
                              )}
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                {/* Order Summary */}
                <Card className="mt-6 bg-card rounded-xl border border-border shadow-none">
                  <CardHeader className="pb-0 pt-4 px-4">
                    <h4 className="font-medium text-sm text-card-foreground">Order Summary</h4>
                  </CardHeader>
                  <CardContent className="px-4 py-3">
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Subtotal</span>
                        <span className="text-card-foreground">${subtotal.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Shipping</span>
                        <span className="text-card-foreground">${shipping.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Tax</span>
                        <span className="text-card-foreground">${tax.toFixed(2)}</span>
                      </div>
                      <div className="border-t border-border my-2 pt-2"></div>
                      <div className="flex justify-between font-medium">
                        <span className="text-card-foreground">Total</span>
                        <span className="text-card-foreground">${total.toFixed(2)}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </>
          ) : (
            <div className="flex flex-col items-center justify-center h-full p-5">
              <div className="w-20 h-20 rounded-full bg-gradient-to-r from-muted to-muted/70 flex items-center justify-center mb-4">
                <ShoppingBag className="h-8 w-8 text-muted-foreground/50" />
              </div>
              <h3 className="text-foreground font-medium text-base mb-1">Your cart is empty</h3>
              <p className="text-muted-foreground text-xs mb-5 text-center max-w-xs">
                Add items to your cart to see them here.
              </p>
              <Link
                href="/shop"
                className="flex items-center gap-2 py-2.5 px-5 bg-gradient-to-r from-primary to-primary/80 text-primary-foreground rounded-md text-sm font-medium hover:opacity-90 transition-colors"
                onClick={onClose}
              >
                Browse Products <ArrowRight className="h-3.5 w-3.5" />
              </Link>
            </div>
          )}
        </div>

        {/* Footer with buttons */}
        {cartItems.length > 0 && (
          <CardFooter className="p-6 border-t border-border">
            <div className="w-full flex flex-col gap-4">
              {/* Total */}
              <div className="flex justify-between items-center">
                <span className="font-semibold text-foreground">Total</span>
                <span className="font-bold text-primary text-lg">${total.toFixed(2)}</span>
              </div>

              {/* Buttons */}
              <div className="flex gap-3 w-full">
                <Link
                  href="/cart"
                  className="flex-1 py-3 bg-gradient-to-r from-secondary to-secondary/80 text-secondary-foreground rounded-full text-sm font-medium hover:opacity-90 transition-opacity flex items-center justify-center gap-1 shadow-sm"
                  onClick={onClose}
                >
                  View Cart <ChevronRight className="h-4 w-4" />
                </Link>

                <Link
                  href="/checkout"
                  className="flex-1 py-3 bg-gradient-to-r from-primary to-primary/80 text-primary-foreground rounded-full text-sm font-medium hover:opacity-90 transition-opacity flex items-center justify-center gap-1 shadow-sm"
                  onClick={onClose}
                >
                  Checkout <ChevronRight className="h-4 w-4" />
                </Link>
              </div>
            </div>
          </CardFooter>
        )}
      </SheetContent>
    </Sheet>
  );
}
