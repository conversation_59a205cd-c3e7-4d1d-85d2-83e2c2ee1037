/**
 * Hook to check if the current viewport is mobile
 */

import { useMediaQuery } from './use-media-query';

export const MO<PERSON>LE_BREAKPOINT = 768;

/**
 * Custom hook to check if the current viewport is mobile
 * @returns Boolean indicating if the viewport is mobile
 */
export function useIsMobile(): boolean {
  return useMediaQuery(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`);
}

/**
 * Custom hook to check if the current viewport is iPad Pro
 * iPad Pro typical widths (834px for 11", 1024px for 12.9")
 * @returns Boolean indicating if the viewport is iPad Pro
 */
export function useIsIpadPro(): boolean {
  return useMediaQuery('(min-width: 834px) and (max-width: 1100px)');
}
