
"use client";

import React, { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
  Store,
  Eye,
  EyeOff,
  Truck,
  BarChart,
  Package,
  Building2
} from "lucide-react";

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

import { useVendorAuth } from "@/hooks/use-auth";
import { toast } from "sonner";
import { loginSchemas, loginRateLimiter } from "@/lib/input-validation";

// Use enhanced validation schema
const formSchema = loginSchemas.vendor;

type FormValues = z.infer<typeof formSchema>;

// Type assertion to fix TypeScript errors with react-hook-form
type FormWithResolver = {
  resolver: any;
  defaultValues: FormValues;
};

export default function VendorLoginPage() {
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);
  const { login, isLoading, error, clearError } = useVendorAuth();

  // Initialize form with react-hook-form and zod validation
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema) as any,
    defaultValues: {
      email: "",
      password: "",
      rememberMe: false,
    },
  } as FormWithResolver);

  const onSubmit = async (data: FormValues) => {
    // Check rate limiting
    const rateLimitKey = `vendor-login-${data.email}`;
    if (!loginRateLimiter.isAllowed(rateLimitKey)) {
      const timeUntilReset = Math.ceil(loginRateLimiter.getTimeUntilReset(rateLimitKey) / 60000);
      toast.error(`Too many login attempts. Please try again in ${timeUntilReset} minutes.`);
      return;
    }

    try {
      clearError();

      await login({
        email: data.email,
        password: data.password,
        rememberMe: data.rememberMe
      });

      // Reset rate limiter on successful login
      loginRateLimiter.reset(rateLimitKey);

      // Show success message
      toast.success("Login successful! Welcome to your dashboard.");

      // Redirect to vendor dashboard after successful login
      router.push("/vendor/dashboard");
    } catch (error) {
      console.error("Login failed:", error);
      const errorMessage = error instanceof Error ? error.message : 'Login failed. Please check your credentials.';
      const remainingAttempts = loginRateLimiter.getRemainingAttempts(rateLimitKey);

      if (remainingAttempts > 0) {
        toast.error(`${errorMessage} (${remainingAttempts} attempts remaining)`);
      } else {
        const timeUntilReset = Math.ceil(loginRateLimiter.getTimeUntilReset(rateLimitKey) / 60000);
        toast.error(`Too many failed attempts. Please try again in ${timeUntilReset} minutes.`);
      }
      // Don't reset form on error - keep user input values
    }
  };

  return (
    <div className="min-h-screen flex">
      {/* Left side - Benefits */}
      <div className="hidden md:flex md:w-1/2 bg-gradient-to-br from-primary to-secondary dark:from-primary/10 dark:to-secondary/40 p-8 items-center justify-center">
        <div className="max-w-md text-white dark:text-accent">
          <div className="mb-8">
            <h2 className="text-3xl font-bold mb-4">Grow Your Business</h2>
            <p>Join thousands of merchants who trust our platform to expand their reach and increase sales.</p>
          </div>

          <div className="space-y-6">
            <div className="flex items-start space-x-4">
              <div className="bg-primary-foreground/10 p-3 rounded-lg">
                <Package className="h-6 w-6" />
              </div>
              <div>
                <h3 className="font-medium text-lg mb-1">Product Management</h3>
                <p className="text-sm">Easily manage your inventory, categories, and pricing from a single dashboard.</p>
              </div>
            </div>

            <div className="flex items-start space-x-4">
              <div className="bg-primary-foreground/10 p-3 rounded-lg">
                <Truck className="h-6 w-6" />
              </div>
              <div>
                <h3 className="font-medium text-lg mb-1">Order Tracking</h3>
                <p className="text-sm">Real-time updates on orders, shipments, and delivery status for your customers.</p>
              </div>
            </div>

            <div className="flex items-start space-x-4">
              <div className="bg-primary-foreground/10 p-3 rounded-lg">
                <BarChart className="h-6 w-6" />
              </div>
              <div>
                <h3 className="font-medium text-lg mb-1">Business Analytics</h3>
                <p className="text-sm">Comprehensive reports and insights to help you make data-driven decisions.</p>
              </div>
            </div>
          </div>

          <div className="mt-12 pt-6 border-t border-primary-foreground/20">
            <div className="flex items-center">
              <Badge className="bg-primary-foreground/20 text-primary-foreground border-none mr-3">
                Trusted
              </Badge>
              <p className="text-sm">By over 10,000+ businesses worldwide</p>
            </div>
          </div>
        </div>
      </div>


      {/* Right side - Login Form */}
      <div className="w-full md:w-1/2 flex items-center justify-center p-6 bg-background">
        <div className="w-full max-w-md">
          <div className="mb-8">
            <div className="flex items-center mb-2">
              <div className="bg-primary p-2 rounded-md mr-3">
                <Building2 className="h-5 w-5 text-primary-foreground" />
              </div>
              <h1 className="text-xl font-bold text-primary">Store Login Portal</h1>
            </div>
            <p className="text-sm text-muted-foreground">Secure access to your business dashboard</p>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit as any)} className="space-y-5">
              {/* Email Field */}
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem className="space-y-2">
                    <FormLabel className="text-sm font-medium">
                      Email Address
                    </FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          placeholder="<EMAIL>"
                          className="h-12 text-sm rounded-md"
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />

              {/* Password Field */}
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem className="space-y-2">
                    <div className="flex justify-between">
                      <FormLabel className="text-sm font-medium">
                        Password
                      </FormLabel>
                      <Link href="/vendor/forgot-password" className="text-sm text-secondary hover:underline">
                        Forgot password?
                      </Link>
                    </div>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type={showPassword ? "text" : "password"}
                          placeholder="••••••••"
                          className="h-12 text-sm pr-10 rounded-md"
                          {...field}
                        />
                        <button
                          type="button"
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-secondary"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </button>
                      </div>
                    </FormControl>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />

              {/* Remember Me */}
              <FormField
                control={form.control}
                name="rememberMe"
                render={({ field }) => (
                  <div className="flex items-center space-x-2">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        className="h-4 w-4 data-[state=checked]:bg-secondary data-[state=checked]:border-secondary"
                      />
                    </FormControl>
                    <label
                      className="text-sm text-muted-foreground leading-none cursor-pointer"
                      onClick={() => form.setValue("rememberMe", !form.getValues("rememberMe"))}
                    >
                      Keep me signed in
                    </label>
                  </div>
                )}
              />

              {/* Login Button */}
              <Button
                type="submit"
                className="w-full h-12 mt-2 bg-primary hover:bg-primary/90 text-primary-foreground font-medium"
                disabled={isLoading}
              >
                {isLoading ? (
                  <span className="flex items-center">
                    <span className="mr-2">Signing in</span>
                    <span className="loading loading-dots"></span>
                  </span>
                ) : (
                  "Sign in to Dashboard"
                )}
              </Button>
            </form>
          </Form>

          {/* Divider */}
          <div className="relative my-8">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-border"></div>
            </div>
            <div className="relative flex justify-center text-xs">
              <span className="px-2 bg-background text-muted-foreground">Don&apos;t have a store account?</span>
            </div>
          </div>

          <Button
            variant="outline"
            className="w-full h-12 text-sm border-border text-foreground hover:bg-secondary hover:border-secondary hover:text-secondary-foreground"
            onClick={() => router.push("/vendor/register")}
          >
            <Store className="mr-2 h-4 w-4" />
            Register your store
          </Button>

          {/* Footer */}
          <div className="mt-12 text-center text-xs text-muted-foreground">
            <p>© 2023 Multi-Tenant E-commerce. All rights reserved.</p>
            <div className="mt-2 flex justify-center space-x-6">
              <Link href="/terms" className="hover:text-secondary">Terms</Link>
              <Link href="/privacy" className="hover:text-secondary">Privacy</Link>
              <Link href="/help" className="hover:text-secondary">Help</Link>
            </div>
          </div>
        </div>
      </div>

    </div>
  );
}
