"use client";

import React, { useState } from "react";
import { useParams } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import { Container } from "@/components/ui/container";
import {
  Star,
  MapPin,
  Phone,
  Mail,
  Globe,
  Clock,
  Store as StoreIcon,
  Award,
  Trophy,
  Share2,
  Share
} from "lucide-react";

// No need to import social icons - we'll use custom SVG icons
import { ProductGrid } from "@/components/sections/product-grid";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { CustomBreadcrumb } from "@/components/ui/custom-breadcrumb";
import { Store, Product } from "@/types";
import { getStoreBySlug } from "@/services/store-service";
import { getProductsByStoreId } from "@/services/product-service";

export default function StoreDetailPage() {
  const params = useParams();
  const storeSlug = params.slug as string;

  // State for active tab
  const [activeTab, setActiveTab] = useState("products");

  // Get store data
  const store = getStoreBySlug(storeSlug);

  // Get products for this store
  const products = store ? getProductsByStoreId(store.id) : [];

  // Get featured products (products with highest ratings)
  const featuredProducts = [...products]
    .sort((a, b) => b.rating - a.rating)
    .slice(0, 5);

  // Get best sellers (products with highest discount)
  const bestSellerProducts = [...products]
    .filter(p => p.discount)
    .sort((a, b) => (b.discountPercentage || 0) - (a.discountPercentage || 0))
    .slice(0, 5);

  // If store not found
  if (!store) {
    return (
      <Container className="py-12">
        <Card className="p-6 shadow-sm border-gray-100 text-center">
          <CardHeader className="p-0 mb-4 space-y-0 flex flex-col items-center">
            <div className="w-16 h-16 rounded-full bg-[#F7F7F9] flex items-center justify-center mb-4">
              <StoreIcon className="h-8 w-8 text-gray-300" />
            </div>
            <CardTitle className="text-2xl font-bold text-gray-900">Store Not Found</CardTitle>
          </CardHeader>
          <CardContent className="p-0 mb-6">
            <p className="text-gray-600">The store you&apos;re looking for doesn&apos;t exist or has been removed.</p>
          </CardContent>
          <Button asChild variant="outline" className="mx-auto">
            <Link href="/stores" className="inline-flex items-center">
              <StoreIcon className="mr-2 h-4 w-4" />
              Browse All Stores
            </Link>
          </Button>
        </Card>
      </Container>
    );
  }

  return (
    <div className="bg-background min-h-screen pb-12">
      {/* Breadcrumb */}
      <CustomBreadcrumb
        items={[
          { label: "Home", href: "/" },
          { label: "Stores", href: "/stores" },
          { label: store.name }
        ]}
        backgroundColor="bg-[#E8F7F6]"
      />

      {/* Store Banner with Integrated Info */}
      <div className="relative bg-gradient-to-b from-primary/10 to-[#F7F7F9] dark:from-primary/20 dark:to-background">
        {/* Banner Image */}
        <div className="h-[250px] md:h-[350px] w-full relative overflow-hidden">
          <Image
            src={store.image}
            alt={`${store.name} banner`}
            fill
            className="object-cover dark:brightness-[0.85]"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-b from-black/40 to-black/5 dark:from-black/60 dark:to-black/20"></div>

          {/* Store badges */}
          <div className="absolute top-4 left-4 md:left-8 flex gap-2 z-10">
            <div className="bg-gradient-primary text-white text-xs font-medium px-3 py-1.5 rounded-full shadow-sm">
              Featured Store
            </div>
            {store.discount && (
              <div className="bg-gradient-secondary text-white text-xs font-medium px-3 py-1.5 rounded-full shadow-sm">
                {store.discount} Off
              </div>
            )}
          </div>
        </div>

        {/* Store Info Overlay */}
        <Container className="relative -mt-24">
          <div className="bg-white dark:bg-card rounded-xl shadow-lg p-6 md:p-8">
            <div className="flex flex-col md:flex-row gap-6">
              {/* Logo and Basic Info */}
              <div className="flex flex-col md:flex-row items-center md:items-start gap-5 flex-1">
                {/* Store Logo */}
                <div className="w-24 h-24 md:w-28 md:h-28 rounded-full border-4 border-white dark:border-primary/60 bg-white dark:bg-primary/10 shadow-md overflow-hidden -mt-16 md:-mt-20 flex-shrink-0">
                  <Image
                    src={store.logo || "/images/categories/store-placeholder.png"}
                    alt={`${store.name} logo`}
                    width={112}
                    height={112}
                    className="object-cover"
                  />
                </div>

                {/* Store Info */}
                <div className="flex-1 text-center md:text-left">
                  <h1 className="text-2xl md:text-3xl font-bold text-primary dark:text-accent">{store.name}</h1>

                  <div className="flex flex-wrap items-center justify-center md:justify-start gap-4 mt-3">
                    {/* Rating */}
                    <div className="flex items-center">
                      <div className="flex">
                        {Array.from({ length: 5 }).map((_, i) => (
                          <Star
                            key={i}
                            className={`h-4 w-4 ${
                              i < Math.floor(store.rating)
                                ? "fill-yellow-400 text-yellow-400"
                                : "text-gray-300 dark:text-gray-600"
                            }`}
                          />
                        ))}
                      </div>
                      <span className="text-sm text-gray-600 dark:text-gray-300 ml-1">{store.rating} ({store.productCount} products)</span>
                    </div>

                    {/* Joined Date */}
                    {store.joinedDate && (
                      <span className="text-xs text-gray-500 dark:text-gray-400 px-2 py-1 bg-gray-50 dark:bg-gray-800 rounded-full">
                        Since {store.joinedDate}
                      </span>
                    )}
                  </div>

                  {/* Categories */}
                  <div className="flex flex-wrap gap-1.5 mt-3 justify-center md:justify-start">
                    {store.categories.map((category, index) => (
                      <span
                        key={index}
                        className="text-xs bg-[#E8F7F6] dark:bg-secondary/30 text-primary dark:text-accent px-2 py-1 rounded-md"
                      >
                        {category}
                      </span>
                    ))}
                  </div>

                  {/* Brief Description */}
                  <div className="mt-4">
                    <p className="text-gray-600 dark:text-gray-300 line-clamp-2">{store.description}</p>
                  </div>
                </div>
              </div>

              {/* Store Stats */}
              <div className="md:w-64 lg:w-72 shrink-0 mt-4 md:mt-0 border-t md:border-t-0 md:border-l border-gray-100 dark:border-gray-800 pt-4 md:pt-0 md:pl-6">
                <div className="grid grid-cols-2 gap-3">
                  <div className="text-center p-3 bg-[#F7F7F9] dark:bg-primary/20 rounded-lg">
                    <div className="font-medium text-lg text-gray-900 dark:text-accent">{store.productCount}</div>
                    <div className="text-xs text-gray-500 dark:text-secondary">Products</div>
                  </div>

                  <div className="text-center p-3 bg-[#F7F7F9] dark:bg-primary/20 rounded-lg">
                    <div className="font-medium text-lg text-gray-900 dark:text-accent flex items-center justify-center">
                      {store.rating}
                      <Star className="h-4 w-4 text-yellow-400 fill-yellow-400 ml-1" />
                    </div>
                    <div className="text-xs text-gray-500 dark:text-secondary">Rating</div>
                  </div>

                  <div className="text-center p-3 bg-[#F7F7F9] dark:bg-primary/20 rounded-lg">
                    <div className="font-medium text-lg text-gray-900 dark:text-accent">{store.categories.length}</div>
                    <div className="text-xs text-gray-500 dark:text-secondary">Categories</div>
                  </div>

                  <div className="text-center p-3 bg-[#F7F7F9] dark:bg-primary/20 rounded-lg">
                    <div className="font-medium text-lg text-gray-900 dark:text-accent flex items-center justify-center">
                      <Mail className="h-4 w-4 text-primary dark:text-secondary mr-1" />
                    </div>
                    <div className="text-xs text-gray-500 dark:text-secondary">Contact</div>
                  </div>
                </div>

                {/* Quick Visit Button */}
                <Button
                  className="mt-4 w-full bg-gradient-primary text-white hover:opacity-90 transition-opacity shadow-sm dark:shadow-[0_0_15px_rgba(42,146,143,0.3)]"
                >
                  Visit Store
                </Button>
              </div>
            </div>
          </div>
        </Container>
      </div>

      {/* Main Content */}
      <Container className="mt-8">

        {/* Tabs Navigation */}
        <div className="flex justify-center md:justify-start mb-8">
          <Tabs
            defaultValue="products"
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList className="bg-[#F7F7F9] dark:bg-primary/20 p-1 rounded-lg shadow-sm">
              <TabsTrigger
                value="products"
                className="px-6 py-2 text-sm font-medium rounded-md transition-all data-[state=active]:bg-white dark:data-[state=active]:bg-primary/40 data-[state=active]:text-primary dark:data-[state=active]:text-accent data-[state=active]:shadow-sm data-[state=inactive]:text-gray-600 dark:data-[state=inactive]:text-gray-400 data-[state=inactive]:hover:text-primary dark:data-[state=inactive]:hover:text-accent"
              >
                Products
              </TabsTrigger>
              <TabsTrigger
                value="about"
                className="px-6 py-2 text-sm font-medium rounded-md transition-all data-[state=active]:bg-white dark:data-[state=active]:bg-primary/40 data-[state=active]:text-primary dark:data-[state=active]:text-accent data-[state=active]:shadow-sm data-[state=inactive]:text-gray-600 dark:data-[state=inactive]:text-gray-400 data-[state=inactive]:hover:text-primary dark:data-[state=inactive]:hover:text-accent"
              >
                About
              </TabsTrigger>
              <TabsTrigger
                value="reviews"
                className="px-6 py-2 text-sm font-medium rounded-md transition-all data-[state=active]:bg-white dark:data-[state=active]:bg-primary/40 data-[state=active]:text-primary dark:data-[state=active]:text-accent data-[state=active]:shadow-sm data-[state=inactive]:text-gray-600 dark:data-[state=inactive]:text-gray-400 data-[state=inactive]:hover:text-primary dark:data-[state=inactive]:hover:text-accent"
              >
                Reviews
              </TabsTrigger>
            </TabsList>

            {/* Tab Content */}
            <TabsContent value="products">
              {/* Featured Products */}
              <div className="space-y-16">
                {/* Featured Products */}
                <ProductGrid
                  title="Featured Products"
                  products={featuredProducts}
                  shopMoreLink={`/store/${store.slug}/featured-products`}
                  backgroundColor="bg-white"
                  icon={<Award size={20} />}
                />

                {/* Best Sellers */}
                <ProductGrid
                  title="Best Sellers"
                  products={bestSellerProducts}
                  shopMoreLink={`/store/${store.slug}/best-sellers`}
                  backgroundColor="bg-white"
                  icon={<Trophy size={20} />}
                  sectionType="bestseller"
                />
              </div>
            </TabsContent>

            {/* About Tab */}
            <TabsContent value="about" className="space-y-8 mt-8">
              {/* Store Description */}
              <Card className="p-6 shadow-sm border-gray-100 dark:border-gray-800 dark:bg-primary/10">
                <CardHeader className="p-0 mb-4 space-y-0">
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-gradient-to-r from-primary/20 to-secondary/20 flex items-center justify-center mr-3">
                      <StoreIcon className="h-4 w-4 text-primary dark:text-accent" />
                    </div>
                    <CardTitle className="text-xl font-bold text-primary dark:text-accent">About {store.name}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="p-0">
                  <p className="text-gray-600 dark:text-gray-300 leading-relaxed">{store.description}</p>
                </CardContent>
              </Card>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Store Details */}
                <Card className="p-6 shadow-sm border-gray-100 dark:border-gray-800 dark:bg-primary/10">
                  <CardHeader className="p-0 mb-4 space-y-0">
                    <div className="flex items-center">
                      <div className="w-8 h-8 rounded-full bg-gradient-to-r from-primary/20 to-secondary/20 flex items-center justify-center mr-3">
                        <MapPin className="h-4 w-4 text-primary dark:text-accent" />
                      </div>
                      <CardTitle className="font-bold text-primary dark:text-accent">Store Details</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent className="p-0">
                    <ul className="space-y-4">
                      {store.address && (
                        <li className="flex items-start p-3 bg-[#F7F7F9] dark:bg-primary/20 rounded-lg">
                          <MapPin className="h-5 w-5 text-primary dark:text-secondary mr-3 mt-0.5 flex-shrink-0" />
                          <span className="text-gray-700 dark:text-gray-300">{store.address}</span>
                        </li>
                      )}
                      {store.phone && (
                        <li className="flex items-center p-3 bg-[#F7F7F9] dark:bg-primary/20 rounded-lg">
                          <Phone className="h-5 w-5 text-primary dark:text-secondary mr-3 flex-shrink-0" />
                          <span className="text-gray-700 dark:text-gray-300">{store.phone}</span>
                        </li>
                      )}
                      {store.email && (
                        <li className="flex items-center p-3 bg-[#F7F7F9] dark:bg-primary/20 rounded-lg">
                          <Mail className="h-5 w-5 text-primary dark:text-secondary mr-3 flex-shrink-0" />
                          <span className="text-gray-700 dark:text-gray-300">{store.email}</span>
                        </li>
                      )}
                      {store.website && (
                        <li className="flex items-center p-3 bg-[#F7F7F9] dark:bg-primary/20 rounded-lg">
                          <Globe className="h-5 w-5 text-primary dark:text-secondary mr-3 flex-shrink-0" />
                          <a href={`https://${store.website}`} target="_blank" rel="noopener noreferrer" className="text-primary dark:text-accent hover:underline">{store.website}</a>
                        </li>
                      )}
                      {store.openingHours && (
                        <li className="flex items-start p-3 bg-[#F7F7F9] dark:bg-primary/20 rounded-lg">
                          <Clock className="h-5 w-5 text-primary dark:text-secondary mr-3 mt-0.5 flex-shrink-0" />
                          <span className="text-gray-700 dark:text-gray-300">{store.openingHours}</span>
                        </li>
                      )}
                    </ul>
                  </CardContent>
                </Card>

                {/* Social Links */}
                {store.socialLinks && (
                  <Card className="p-6 shadow-sm border-gray-100 dark:border-gray-800 dark:bg-primary/10">
                    <CardHeader className="p-0 mb-4 space-y-0">
                      <div className="flex items-center">
                        <div className="w-8 h-8 rounded-full bg-gradient-to-r from-primary/20 to-secondary/20 flex items-center justify-center mr-3">
                          <Share2 className="h-4 w-4 text-primary dark:text-accent" />
                        </div>
                        <CardTitle className="font-bold text-primary dark:text-accent">Connect With Us</CardTitle>
                      </div>
                    </CardHeader>
                    <CardContent className="p-0">
                      <div className="grid grid-cols-2 gap-4">
                        {store.socialLinks.facebook && (
                          <a
                            href={store.socialLinks.facebook}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center p-3 bg-[#F7F7F9] dark:bg-primary/30 rounded-lg hover:bg-[#E8F7F6] dark:hover:bg-primary/40 transition-colors"
                          >
                            <div className="w-10 h-10 rounded-full bg-blue-600 flex items-center justify-center mr-3">
                              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 text-white"><path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path></svg>
                            </div>
                            <span className="text-gray-700 dark:text-accent font-medium">Facebook</span>
                          </a>
                        )}
                        {store.socialLinks.instagram && (
                          <a
                            href={store.socialLinks.instagram}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center p-3 bg-[#F7F7F9] dark:bg-primary/30 rounded-lg hover:bg-[#E8F7F6] dark:hover:bg-primary/40 transition-colors"
                          >
                            <div className="w-10 h-10 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center mr-3">
                              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 text-white"><rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect><path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path><line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line></svg>
                            </div>
                            <span className="text-gray-700 dark:text-accent font-medium">Instagram</span>
                          </a>
                        )}
                        {store.socialLinks.twitter && (
                          <a
                            href={store.socialLinks.twitter}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center p-3 bg-[#F7F7F9] dark:bg-primary/30 rounded-lg hover:bg-[#E8F7F6] dark:hover:bg-primary/40 transition-colors"
                          >
                            <div className="w-10 h-10 rounded-full bg-blue-400 flex items-center justify-center mr-3">
                              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 text-white"><path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path></svg>
                            </div>
                            <span className="text-gray-700 dark:text-accent font-medium">Twitter</span>
                          </a>
                        )}
                        {store.socialLinks.linkedin && (
                          <a
                            href={store.socialLinks.linkedin}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center p-3 bg-[#F7F7F9] dark:bg-primary/30 rounded-lg hover:bg-[#E8F7F6] dark:hover:bg-primary/40 transition-colors"
                          >
                            <div className="w-10 h-10 rounded-full bg-blue-700 flex items-center justify-center mr-3">
                              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 text-white"><path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path><rect x="2" y="9" width="4" height="12"></rect><circle cx="4" cy="4" r="2"></circle></svg>
                            </div>
                            <span className="text-gray-700 dark:text-accent font-medium">LinkedIn</span>
                          </a>
                        )}
                      </div>
                      <div className="mt-4 pt-4 border-t border-gray-100 dark:border-gray-800">
                        <Button variant="outline" className="w-full flex items-center justify-center gap-2 dark:border-gray-700 dark:text-accent dark:hover:bg-primary/40">
                          <Share className="h-4 w-4" />
                          <span>Share This Store</span>
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </TabsContent>

            {/* Reviews Tab */}
            <TabsContent value="reviews" className="space-y-8 mt-8">
              <Card className="p-6 shadow-sm border-gray-100 dark:border-gray-800 dark:bg-primary/10">
                <CardHeader className="p-0 mb-4 space-y-0">
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-gradient-to-r from-primary/20 to-secondary/20 flex items-center justify-center mr-3">
                      <Star className="h-4 w-4 text-primary dark:text-accent" />
                    </div>
                    <CardTitle className="text-xl font-bold text-gray-900 dark:text-accent">Customer Reviews</CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="p-0">
                  <div className="flex flex-col items-center justify-center py-12">
                    <div className="w-16 h-16 rounded-full bg-[#F7F7F9] dark:bg-primary/30 flex items-center justify-center mb-4">
                      <Star className="h-8 w-8 text-gray-300 dark:text-secondary/50" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-accent mb-2">No Reviews Yet</h3>
                    <p className="text-gray-600 dark:text-gray-300 text-center max-w-md mb-6">Be the first to review products from this store and help other shoppers make informed decisions.</p>
                    <Button className="bg-gradient-primary text-white hover:opacity-90 transition-opacity shadow-sm dark:shadow-[0_0_10px_rgba(42,146,143,0.2)]">
                      Write a Review
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </Container>
    </div>
  );
}


