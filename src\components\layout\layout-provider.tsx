"use client";

import React from "react";
import { usePathname } from "next/navigation";
import { CustomerLayout } from "./customer/layout";
import { SuperAdminLayout } from "./superadmin/layout";
import { VendorLayout } from "./vendor/layout";

interface LayoutProviderProps {
  children: React.ReactNode;
}

/**
 * Layout provider component that determines which layout to use based on the current URL path.
 * This approach avoids conditional rendering within layouts and keeps the code clean.
 *
 * Path patterns:
 * - /superadmin/login: No layout (direct rendering)
 * - /superadmin/*: SuperAdminLayout
 * - /vendor/login or /vendor/register: No layout (direct rendering)
 * - /vendor/*: VendorLayout
 * - All other paths: CustomerLayout
 */
export function LayoutProvider({ children }: LayoutProviderProps) {
  const pathname = usePathname() || "";

  // Auth pages that should have no layout
  const noLayoutPaths = [
    "/superadmin/login",
    "/vendor/login",
    "/vendor/register"
  ];

  // Check if current path should have no layout
  if (noLayoutPaths.some(path => pathname.startsWith(path))) {
    return <>{children}</>;
  }

  // Determine which layout to use based on path prefix
  if (pathname.startsWith("/superadmin")) {
    return <SuperAdminLayout>{children}</SuperAdminLayout>;
  }

  if (pathname.startsWith("/vendor")) {
    return <VendorLayout>{children}</VendorLayout>;
  }

  // Default to customer layout for all other routes
  return <CustomerLayout>{children}</CustomerLayout>;
}
