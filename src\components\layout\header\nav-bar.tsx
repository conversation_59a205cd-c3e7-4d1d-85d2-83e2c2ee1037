"use client";

import React, { useState } from "react";
import Link from "next/link";
import {
  ChevronDown,
  Menu,
  ShoppingBag,
  Layers,
  Smartphone,
  Laptop,
  Headphones,
  Camera,
  Shirt,
  Watch,
  Footprints,
  Gem,
  Sofa,
  Utensils,
  Sparkles,
  Scissors,
  BookOpen,
  Dumbbell,
  Gamepad2 as Gamepad,
  ChevronRight,
  Store
} from "lucide-react";
import { Container } from "@/components/ui/container";
import { Button } from "@/components/ui/button";
import { MobileMenu } from "./mobile-menu";
import { useIsMobile, useIsIpadPro } from "@/hooks/use-mobile";

export function NavBar() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const isMobile = useIsMobile();
  const isIpadPro = useIsIpadPro();

  return (
    <div className="w-full bg-card py-3 sm:py-4 relative border-b border-gray-100 dark:border-border">
      <Container>
        <div className="flex items-center justify-between">
          {/* Mobile Menu Button - Only visible on small/medium screens and iPad Pro */}
          <Button
            variant="ghost"
            size="icon"
            className="xl:hidden text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-primary dark:hover:text-primary"
            aria-label="Menu"
            onClick={() => setIsMobileMenuOpen(true)}
          >
            <Menu className="h-5 w-5" />
          </Button>

          {/* Mobile Menu */}
          <MobileMenu
            isOpen={isMobileMenuOpen}
            onClose={() => setIsMobileMenuOpen(false)}
          />

          {/* Categories Dropdown - Hidden on mobile/iPad, visible on large screens */}
          <div className="relative group hidden xl:block z-10">
            <button className="flex items-center space-x-1 sm:space-x-2 bg-slate-200 dark:bg-gray-800/80 text-gray-700 dark:text-gray-200 px-4 sm:px-6 py-2 sm:py-3 rounded-full text-sm sm:text-base font-medium relative z-10 shadow-sm hover:shadow-md dark:hover:shadow-none dark:hover:bg-gray-700 dark:border dark:border-primary/20 hover:border-primary/30 transition-all duration-300 btn-glow dark:after:bg-gradient-to-r dark:after:from-primary/20 dark:after:to-secondary/20 dark:after:opacity-50">
              <Layers className="h-3.5 w-3.5 sm:h-4 sm:w-4 mr-1 text-primary dark:text-primary/90" />
              <span>All Categories</span>
              <ChevronDown className="h-3.5 w-3.5 sm:h-4 sm:w-4 ml-1 transition-transform duration-200 group-hover:rotate-180" />
            </button>
            {/* Dropdown content */}
            <div className="absolute left-0 top-full pt-3 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
              <div className="bg-white dark:bg-card rounded-xl shadow-lg dark:shadow-none border border-accent/20 dark:border-border p-3 sm:p-5 w-[450px] md:w-[550px] lg:w-[600px] grid grid-cols-3 gap-2 sm:gap-3">
                <div className="col-span-1">
                  <h3 className="font-semibold text-xs sm:text-sm mb-2 sm:mb-3 text-gray-800 dark:text-gray-200 border-b dark:border-gray-700 pb-1 sm:pb-2">Electronics</h3>
                  <div className="grid grid-cols-1 gap-1 sm:gap-2">
                    <Link href="/category/electronics/smartphones" className="flex items-center gap-1.5 sm:gap-2 px-2 sm:px-3 py-1.5 sm:py-2 rounded-lg hover:bg-accent/20 dark:hover:bg-accent/10 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-colors">
                      <Smartphone className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-primary" />
                      <span className="text-xs sm:text-sm">Smartphones</span>
                    </Link>
                    <Link href="/category/electronics/laptops" className="flex items-center gap-1.5 sm:gap-2 px-2 sm:px-3 py-1.5 sm:py-2 rounded-lg hover:bg-accent/20 dark:hover:bg-accent/10 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-colors">
                      <Laptop className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-primary" />
                      <span className="text-xs sm:text-sm">Laptops & Computers</span>
                    </Link>
                    <Link href="/category/electronics/headphones" className="flex items-center gap-1.5 sm:gap-2 px-2 sm:px-3 py-1.5 sm:py-2 rounded-lg hover:bg-accent/20 dark:hover:bg-accent/10 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-colors">
                      <Headphones className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-primary" />
                      <span className="text-xs sm:text-sm">Audio & Headphones</span>
                    </Link>
                    <Link href="/category/electronics/cameras" className="flex items-center gap-1.5 sm:gap-2 px-2 sm:px-3 py-1.5 sm:py-2 rounded-lg hover:bg-accent/20 dark:hover:bg-accent/10 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-colors">
                      <Camera className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-primary" />
                      <span className="text-xs sm:text-sm">Cameras</span>
                    </Link>
                    <Link href="/category/electronics/gaming" className="flex items-center gap-1.5 sm:gap-2 px-2 sm:px-3 py-1.5 sm:py-2 rounded-lg hover:bg-accent/20 dark:hover:bg-accent/10 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-colors">
                      <Gamepad className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-primary" />
                      <span className="text-xs sm:text-sm">Gaming</span>
                    </Link>
                  </div>
                </div>

                <div className="col-span-1">
                  <h3 className="font-semibold text-xs sm:text-sm mb-2 sm:mb-3 text-gray-800 dark:text-gray-200 border-b dark:border-gray-700 pb-1 sm:pb-2">Fashion</h3>
                  <div className="grid grid-cols-1 gap-1 sm:gap-2">
                    <Link href="/category/fashion/clothing" className="flex items-center gap-1.5 sm:gap-2 px-2 sm:px-3 py-1.5 sm:py-2 rounded-lg hover:bg-accent/20 dark:hover:bg-accent/10 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-colors">
                      <Shirt className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-secondary" />
                      <span className="text-xs sm:text-sm">Clothing</span>
                    </Link>
                    <Link href="/category/fashion/shoes" className="flex items-center gap-1.5 sm:gap-2 px-2 sm:px-3 py-1.5 sm:py-2 rounded-lg hover:bg-accent/20 dark:hover:bg-accent/10 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-colors">
                      <Footprints className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-secondary" />
                      <span className="text-xs sm:text-sm">Shoes</span>
                    </Link>
                    <Link href="/category/fashion/watches" className="flex items-center gap-1.5 sm:gap-2 px-2 sm:px-3 py-1.5 sm:py-2 rounded-lg hover:bg-accent/20 dark:hover:bg-accent/10 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-colors">
                      <Watch className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-secondary" />
                      <span className="text-xs sm:text-sm">Watches</span>
                    </Link>
                    <Link href="/category/fashion/jewelry" className="flex items-center gap-1.5 sm:gap-2 px-2 sm:px-3 py-1.5 sm:py-2 rounded-lg hover:bg-accent/20 dark:hover:bg-accent/10 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-colors">
                      <Gem className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-secondary" />
                      <span className="text-xs sm:text-sm">Jewelry</span>
                    </Link>
                    <Link href="/category/fashion/accessories" className="flex items-center gap-1.5 sm:gap-2 px-2 sm:px-3 py-1.5 sm:py-2 rounded-lg hover:bg-accent/20 dark:hover:bg-accent/10 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-colors">
                      <Scissors className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-secondary" />
                      <span className="text-xs sm:text-sm">Accessories</span>
                    </Link>
                  </div>
                </div>

                <div className="col-span-1">
                  <h3 className="font-semibold text-xs sm:text-sm mb-2 sm:mb-3 text-gray-800 dark:text-gray-200 border-b dark:border-gray-700 pb-1 sm:pb-2">Home & More</h3>
                  <div className="grid grid-cols-1 gap-1 sm:gap-2">
                    <Link href="/category/home/<USER>" className="flex items-center gap-1.5 sm:gap-2 px-2 sm:px-3 py-1.5 sm:py-2 rounded-lg hover:bg-accent/20 dark:hover:bg-accent/10 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-colors">
                      <Sofa className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-secondary" />
                      <span className="text-xs sm:text-sm">Furniture</span>
                    </Link>
                    <Link href="/category/home/<USER>" className="flex items-center gap-1.5 sm:gap-2 px-2 sm:px-3 py-1.5 sm:py-2 rounded-lg hover:bg-accent/20 dark:hover:bg-accent/10 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-colors">
                      <Utensils className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-accent" />
                      <span className="text-xs sm:text-sm">Kitchen</span>
                    </Link>
                    <Link href="/category/beauty-health" className="flex items-center gap-1.5 sm:gap-2 px-2 sm:px-3 py-1.5 sm:py-2 rounded-lg hover:bg-accent/20 dark:hover:bg-accent/10 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-colors">
                      <Sparkles className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-accent" />
                      <span className="text-xs sm:text-sm">Beauty & Health</span>
                    </Link>
                    <Link href="/category/books" className="flex items-center gap-1.5 sm:gap-2 px-2 sm:px-3 py-1.5 sm:py-2 rounded-lg hover:bg-accent/20 dark:hover:bg-accent/10 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-colors">
                      <BookOpen className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-accent" />
                      <span className="text-xs sm:text-sm">Books & Media</span>
                    </Link>
                    <Link href="/category/sports" className="flex items-center gap-1.5 sm:gap-2 px-2 sm:px-3 py-1.5 sm:py-2 rounded-lg hover:bg-accent/20 dark:hover:bg-accent/10 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-colors">
                      <Dumbbell className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-accent" />
                      <span className="text-xs sm:text-sm">Sports & Fitness</span>
                    </Link>
                  </div>
                </div>

                <div className="col-span-3 mt-2 sm:mt-3 pt-2 sm:pt-3 border-t dark:border-gray-700">
                  <Link href="/categories" className="flex items-center justify-center gap-1 text-primary hover:underline text-xs sm:text-sm font-medium">
                    <span>View All Categories</span>
                    <ChevronRight className="h-2.5 w-2.5 sm:h-3 sm:w-3" />
                  </Link>
                </div>
              </div>
            </div>
          </div>

          {/* Navigation Links - Hidden on mobile/iPad Pro, visible on large screens */}
          <nav className="hidden xl:flex items-center relative ml-4 flex-1">
            {/* Cylindrical rounded line around navbar items and All Categories button */}
            <div className="absolute -left-16 right-0 top-1/2 -translate-y-1/2 h-10 sm:h-12 border border-primary/30 dark:border-primary/20 rounded-full pointer-events-none shadow-[inset_0_0_10px_rgba(0,0,0,0.01)] dark:shadow-none"></div>

            <div className="flex items-center justify-center w-full">
              <div className="flex items-center mx-auto">

                <Link href="/shop" className="px-3 sm:px-5 py-2 sm:py-2.5 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary relative text-sm sm:text-base font-medium z-10 transition-colors duration-200 flex items-center">
                  <ShoppingBag className="h-3.5 w-3.5 sm:h-4 sm:w-4 mr-1 sm:mr-1.5" />
                  <span>Shop</span>
                  <span className="absolute -top-2 -right-1 text-[9px] sm:text-[10px] bg-secondary text-white px-1 sm:px-1.5 py-0.5 rounded shadow-sm font-medium">
                    NEW
                  </span>
                </Link>
                <div className="h-4 sm:h-5 w-px bg-primary/40 dark:bg-primary/20"></div>

                <Link href="/stores" className="px-3 sm:px-5 py-2 sm:py-2.5 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary text-sm sm:text-base font-medium z-10 transition-colors duration-200 flex items-center">
                  <Store className="h-3.5 w-3.5 sm:h-4 sm:w-4 mr-1 sm:mr-1.5" />
                  <span>Stores</span>
                </Link>
                <div className="h-4 sm:h-5 w-px bg-primary/40 dark:bg-primary/20"></div>

                <Link href="/collections" className="px-3 sm:px-5 py-2 sm:py-2.5 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary text-sm sm:text-base font-medium z-10 transition-colors duration-200 flex items-center">
                  <Layers className="h-3.5 w-3.5 sm:h-4 sm:w-4 mr-1 sm:mr-1.5" />
                  <span>Collections</span>
                </Link>
                <div className="h-4 sm:h-5 w-px bg-primary/40 dark:bg-primary/20"></div>

                <Link href="/pages" className="px-3 sm:px-5 py-2 sm:py-2.5 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary relative text-sm sm:text-base font-medium z-10 transition-colors duration-200 flex items-center">
                  <span>Pages</span>
                </Link>
                <div className="h-4 sm:h-5 w-px bg-primary/40 dark:bg-primary/20"></div>

                <Link href="/blog" className="px-3 sm:px-5 py-2 sm:py-2.5 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary text-sm sm:text-base font-medium z-10 transition-colors duration-200 flex items-center">
                  <span>Blog</span>
                </Link>
              </div>
            </div>
          </nav>

          {/* Register / Login Buttons - Hidden on mobile/iPad Pro, visible on large screens */}
          <div className="hidden xl:flex items-center space-x-3 ml-4 sm:ml-8">
            <Link
              href="/login"
              className="text-primary dark:text-primary border-2 border-primary dark:border-primary px-3 sm:px-4 py-1.5 sm:py-2 rounded-full text-xs sm:text-sm font-medium transition-all duration-300 hover:bg-primary hover:text-white dark:hover:text-white hover:shadow-md dark:hover:shadow-none hover:-translate-y-0.5"
            >
              Customer Login
            </Link>
            <Link
              href="/vendor/login"
              className="text-secondary dark:text-secondary border-2 border-secondary dark:border-secondary px-3 sm:px-4 py-1.5 sm:py-2 rounded-full text-xs sm:text-sm font-medium transition-all duration-300 hover:bg-secondary hover:text-white dark:hover:text-white hover:shadow-md dark:hover:shadow-none hover:-translate-y-0.5"
            >
              Vendor Login
            </Link>
          </div>

          {/* Mobile/Tablet Navigation - Simplified for small/medium screens and iPad Pro */}
          <nav className="xl:hidden flex items-center space-x-2 xs:space-x-3 sm:space-x-4">
            <Link href="/shop" className="py-1 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary text-xs sm:text-sm md:text-base flex items-center">
              <ShoppingBag className="h-3 w-3 sm:h-3.5 sm:w-3.5 md:h-4 md:w-4 mr-1" />
              <span>Shop</span>
            </Link>
            <Link href="/stores" className="py-1 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary text-xs sm:text-sm md:text-base flex items-center">
              <Store className="h-3 w-3 sm:h-3.5 sm:w-3.5 md:h-4 md:w-4 mr-1" />
              <span>Stores</span>
            </Link>
            <Link href="/collections" className="py-1 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary text-xs sm:text-sm md:text-base flex items-center">
              <Layers className="h-3 w-3 sm:h-3.5 sm:w-3.5 md:h-4 md:w-4 mr-1" />
              <span>Collections</span>
            </Link>

            {/* Mobile/Tablet Login/Register Buttons - Enhanced for iPad Pro */}
            <div className="flex space-x-2 md:space-x-3">
              <Link
                href="/login"
                className="py-1 px-2 md:px-3 text-primary dark:text-primary border border-primary dark:border-primary text-[10px] xs:text-xs md:text-sm font-medium rounded-full transition-all duration-300 hover:bg-primary hover:text-white dark:hover:text-white hover:shadow-sm dark:hover:shadow-none"
              >
                Customer
              </Link>
              <Link
                href="/vendor/login"
                className="py-1 px-2 md:px-3 text-secondary dark:text-secondary border border-secondary dark:border-secondary text-[10px] xs:text-xs md:text-sm font-medium rounded-full transition-all duration-300 hover:bg-secondary hover:text-white dark:hover:text-white hover:shadow-sm dark:hover:shadow-none"
              >
                Vendor
              </Link>
            </div>
          </nav>
        </div>
      </Container>
    </div>
  );
}
