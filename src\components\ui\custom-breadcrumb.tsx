"use client";

import React from "react";
import Link from "next/link";
import { Container } from "@/components/ui/container";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

export type BreadcrumbItemType = {
  label: string;
  href?: string;
};

export interface CustomBreadcrumbProps {
  items: BreadcrumbItemType[];
  backgroundColor?: string;
  className?: string;
}

export function CustomBreadcrumb({
  items,
  backgroundColor = "bg-[#E8F7F6]",
  className = "",
}: CustomBreadcrumbProps) {
  if (!items || items.length === 0) return null;

  return (
    <div className={`${backgroundColor} dark:bg-gray-800/50 py-4 dark:border-border ${className}`}>
      <Container>
        <Breadcrumb>
          <BreadcrumbList className="text-gray-600 dark:text-gray-400">
            {items.map((item, index) => {
              const isLast = index === items.length - 1;

              return (
                <React.Fragment key={index}>
                  <BreadcrumbItem>
                    {isLast || !item.href ? (
                      <BreadcrumbPage className="text-gray-800 dark:text-gray-200 font-medium">
                        {item.label}
                      </BreadcrumbPage>
                    ) : (
                      <BreadcrumbLink asChild>
                        <Link href={item.href} className="hover:text-primary transition-colors">
                          {item.label}
                        </Link>
                      </BreadcrumbLink>
                    )}
                  </BreadcrumbItem>
                  
                  {!isLast && (
                    <BreadcrumbSeparator className="text-gray-500 dark:text-gray-500">
                      |
                    </BreadcrumbSeparator>
                  )}
                </React.Fragment>
              );
            })}
          </BreadcrumbList>
        </Breadcrumb>
      </Container>
    </div>
  );
}
