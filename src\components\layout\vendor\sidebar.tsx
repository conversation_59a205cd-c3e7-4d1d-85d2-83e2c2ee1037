"use client"

import * as React from "react"
import { DashboardSidebar } from "@/components/dashboard/sidebar"
import { vendorNavData } from "@/data/vendor-nav"
import { Sidebar } from "@/components/ui/sidebar"
import { DashboardUserInfo } from "@/types/navigation"

/**
 * Vendor sidebar component
 *
 * This component uses the shared DashboardSidebar component with vendor-specific
 * navigation data and user information.
 */
export function VendorSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  // Vendor user information
  const vendorUserInfo: DashboardUserInfo = {
    displayName: "Vendor Store",
    email: "<EMAIL>",
    avatarSrc: "/avatars/vendor.jpg",
    avatarFallback: "VS",
    logoutUrl: "/vendor/logout"
  };

  return (
    <DashboardSidebar
      navData={vendorNavData}
      logoText="Vendor Store"
      logoInitials="VS"
      userInfo={vendorUserInfo}
      {...props}
    />
  )
}
