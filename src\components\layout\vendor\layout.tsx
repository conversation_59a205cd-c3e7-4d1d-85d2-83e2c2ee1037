"use client";

import React from "react";
import { VendorHeader } from "./header";
import { VendorSidebar } from "./sidebar";
import { DashboardLayout } from "@/components/dashboard/layout";

/**
 * Props for the VendorLayout component
 */
interface VendorLayoutProps {
  /** The main content of the vendor dashboard */
  children: React.ReactNode;
}

/**
 * Layout component for the vendor dashboard
 *
 * This component uses the base DashboardLayout with vendor-specific
 * sidebar and header components.
 */
export function VendorLayout({ children }: VendorLayoutProps) {
  return (
    <DashboardLayout
      sidebar={<VendorSidebar />}
      header={<VendorHeader />}
      defaultSidebarOpen={true}
    >
      {children}
    </DashboardLayout>
  );
}
