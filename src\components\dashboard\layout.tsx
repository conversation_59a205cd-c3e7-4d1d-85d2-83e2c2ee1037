"use client";

import React from "react";
import {
  Sidebar<PERSON>rovider,
  SidebarTrigger,
  SidebarInset
} from "@/components/ui/sidebar";
import { Separator } from "@/components/ui/separator";

/**
 * Props for the DashboardLayout component
 */
export interface DashboardLayoutProps {
  /** The main content of the dashboard */
  children: React.ReactNode;
  /** The sidebar component to display */
  sidebar: React.ReactNode;
  /** The header component to display */
  header: React.ReactNode;
  /** Whether the sidebar should be open by default */
  defaultSidebarOpen?: boolean;
}

/**
 * Base dashboard layout component used by both SuperAdmin and Vendor layouts
 *
 * This component provides the structure for dashboard layouts with a
 * sidebar, header, and main content area.
 */
export function DashboardLayout({
  children,
  sidebar,
  header,
  defaultSidebarOpen = true
}: DashboardLayoutProps) {
  return (
    <SidebarProvider defaultOpen={defaultSidebarOpen}>
      {sidebar}
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 border-b border-border">
          <div className="flex items-center gap-2 px-4 w-full">
            <SidebarTrigger className="-ml-1" />
            <Separator
              orientation="vertical"
              className="mr-2 data-[orientation=vertical]:h-4"
            />
            {header}
          </div>
        </header>
        <main className="flex-grow flex-shrink min-w-0 overflow-y-auto p-4 w-full">
          {children}
        </main>
      </SidebarInset>
    </SidebarProvider>
  );
}
