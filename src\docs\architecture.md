# Multi-Tenant Ecommerce Application Architecture

This document provides an overview of the architecture, component structure, and best practices for the multi-tenant ecommerce application.

## Application Overview

The application is a multi-tenant ecommerce platform with three user types:
- **Superadmin**: Main tenant with access to the entire platform and administration features
- **Vendor**: Store owners with access to their own store management dashboard
- **Customer**: End users who browse and purchase products

Each user type has its own layout and dashboard structure, with separate navigation and styling tailored to their specific needs.

## Project Structure

```
src/
├── app/                    # Next.js app router pages
│   ├── (auth)/             # Authentication pages (customer)
│   ├── superadmin/         # Superadmin pages
│   │   ├── dashboard/      # Superadmin dashboard
│   │   └── login/          # Superadmin login
│   ├── vendor/             # Vendor pages
│   │   ├── (auth)/         # Vendor authentication pages
│   │   └── dashboard/      # Vendor dashboard
│   └── ...                 # Other customer-facing pages
├── components/             # Reusable components
│   ├── dashboard/          # Shared dashboard components
│   │   ├── layout.tsx      # Base dashboard layout
│   │   └── sidebar.tsx     # Base dashboard sidebar
│   ├── layout/             # Layout components
│   │   ├── customer/       # Customer layout components
│   │   ├── superadmin/     # Superadmin layout components
│   │   ├── vendor/         # Vendor layout components
│   │   └── layout-provider.tsx # Layout provider component
│   ├── ui/                 # UI components (shadcn)
│   └── ...                 # Other components
├── data/                   # Data files
│   ├── superadmin-nav.ts   # Superadmin navigation data
│   └── vendor-nav.ts       # Vendor navigation data
├── docs/                   # Documentation
├── hooks/                  # Custom hooks
├── lib/                    # Utility functions
├── providers/              # Context providers
└── types/                  # TypeScript type definitions
```

## Layout Structure

The application uses a layout provider to determine which layout to use based on the current URL path:

```tsx
// src/components/layout/layout-provider.tsx
export function LayoutProvider({ children }: LayoutProviderProps) {
  const pathname = usePathname();

  // Special case for superadmin login page (no layout)
  if (pathname?.startsWith("/superadmin/login")) {
    return <>{children}</>;
  }

  // For other superadmin pages, use the SuperAdminLayout
  if (pathname?.startsWith("/superadmin")) {
    return <SuperAdminLayout>{children}</SuperAdminLayout>;
  }

  // Special case for vendor login/register pages (no layout)
  if (pathname?.startsWith("/vendor/login") || pathname?.startsWith("/vendor/register")) {
    return <>{children}</>;
  }

  // For other vendor pages, use the VendorLayout
  if (pathname?.startsWith("/vendor")) {
    return <VendorLayout>{children}</VendorLayout>;
  }

  // For all other pages, use the CustomerLayout
  return <CustomerLayout>{children}</CustomerLayout>;
}
```

## Dashboard Components

### Base Dashboard Components

The application uses shared base dashboard components that are extended by the superadmin and vendor dashboards:

- **DashboardLayout**: Base layout component for all dashboards
- **DashboardSidebar**: Base sidebar component for all dashboards

### User-Specific Dashboard Components

Each user type has its own specific dashboard components:

- **SuperAdminLayout**: Layout for superadmin dashboard
- **SuperAdminSidebar**: Sidebar for superadmin dashboard
- **SuperAdminHeader**: Header for superadmin dashboard
- **VendorLayout**: Layout for vendor dashboard
- **VendorSidebar**: Sidebar for vendor dashboard
- **VendorHeader**: Header for vendor dashboard

## Navigation Structure

Navigation data is defined in separate files for each user type:

- **superadmin-nav.ts**: Navigation data for superadmin dashboard
- **vendor-nav.ts**: Navigation data for vendor dashboard

The navigation data follows a consistent structure:

```typescript
export const superadminNavData: DashboardNavData = {
  groupLabel: "Main Navigation",
  mainNavItems: [
    {
      title: "Dashboard",
      url: "/superadmin/dashboard",
      icon: LayoutDashboard,
      isActive: true,
    },
    {
      title: "Stores",
      url: "/superadmin/stores",
      icon: Store,
      items: [
        {
          title: "All Stores",
          url: "/superadmin/stores",
        },
        // More sub-items...
      ],
    },
    // More items...
  ],
};
```

## Styling

The application uses Tailwind CSS with shadcn UI components. The color scheme is defined in the globals.css file:

```css
:root {
  --radius: 0.625rem;
  --background: #FFFFFF;
  --foreground: #000000;
  --card: #FFFFFF;
  --card-foreground: #000000;
  --popover: #FFFFFF;
  --popover-foreground: #000000;
  --primary: #052E7F;
  --primary-foreground: #FFFFFF;
  --secondary: #2A928F;
  --secondary-foreground: #FFFFFF;
  --accent: #D9F7F5;
  --accent-foreground: #052E7F;
  /* Other variables... */
}

.dark {
  --background: #0F172A;
  --foreground: #F8FAFC;
  --card: #1E293B;
  --card-foreground: #F8FAFC;
  --popover: #1E293B;
  --popover-foreground: #F8FAFC;
  --primary: #1A56DB;
  --primary-foreground: #FFFFFF;
  --secondary: #2A928F;
  --secondary-foreground: #FFFFFF;
  --accent: #D9F7F5;
  --accent-foreground: #052E7F;
  /* Other variables... */
}
```

## Best Practices

1. **Component Organization**:
   - Keep components small and focused on a single responsibility
   - Use composition to build complex components
   - Keep related components together in the same directory

2. **TypeScript**:
   - Define proper types for all components and functions
   - Use interfaces for object shapes and types for unions
   - Avoid using `any` type

3. **Styling**:
   - Use Tailwind CSS utility classes for styling
   - Use shadcn UI components for consistent UI
   - Follow the defined color scheme

4. **State Management**:
   - Use React hooks for local state
   - Use context for shared state
   - Keep state as close as possible to where it's used

5. **Performance**:
   - Use memoization for expensive calculations
   - Avoid unnecessary re-renders
   - Use proper keys for lists

6. **Accessibility**:
   - Ensure all components are accessible
   - Use proper ARIA attributes
   - Test with keyboard navigation

7. **Code Quality**:
   - Write clean, readable code
   - Use meaningful variable and function names
   - Add comments for complex logic
   - Follow the DRY (Don't Repeat Yourself) principle
