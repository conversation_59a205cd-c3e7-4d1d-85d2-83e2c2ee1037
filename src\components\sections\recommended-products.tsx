"use client";

import { ProductGrid } from "./product-grid";
import { Sparkles } from "lucide-react";

// Recommended products data - combining the best products from different categories
const recommendedProductsData = [
  { name: "Smart Home Speaker", category: "Electronics", price: 129.99, discount: true, oldPrice: 159.99, rating: 4.7 },
  { name: "Fitness Smartwatch", category: "Wearables", price: 199.99, discount: false, rating: 4.8 },
  { name: "Wireless Earbuds", category: "Audio", price: 119.99, discount: true, oldPrice: 149.99, rating: 4.9 },
  { name: "Ultra-Thin Laptop", category: "Computers", price: 899.99, discount: false, rating: 4.6 },
  { name: "Robot Vacuum Cleaner", category: "Home Appliances", price: 199.99, discount: true, oldPrice: 349.99, rating: 4.6 },
  { name: "Ergonomic Gaming Chair", category: "Gaming", price: 299.99, discount: false, rating: 4.7 },
  { name: "4K Ultra HD Smart TV", category: "Electronics", price: 649.99, discount: true, oldPrice: 799.99, rating: 4.8 },
  { name: "Portable Power Bank", category: "Accessories", price: 49.99, discount: false, rating: 4.6 },
  { name: "Smart Home Security System", category: "Home Security", price: 249.99, discount: true, oldPrice: 299.99, rating: 4.8 },
  { name: "Wireless Mechanical Keyboard", category: "Computer Accessories", price: 89.99, discount: false, rating: 4.7 }
];

type RecommendedProductsProps = {
  title?: string;
  products?: typeof recommendedProductsData;
  shopMoreLink?: string;
  sectionType?: "recommended" | "featured";
  backgroundColor?: string;
};

export const RecommendedProducts = ({
  title = "Recommended For You",
  products = recommendedProductsData,
  shopMoreLink = "/recommended",
  sectionType = "recommended",
  backgroundColor = "bg-[#F7F7F9]"
}: RecommendedProductsProps) => {
  return (
    <ProductGrid
      title={title}
      products={products}
      shopMoreLink={shopMoreLink}
      sectionType={sectionType}
      icon={<Sparkles size={20} />}
      backgroundColor={backgroundColor}
    />
  );
};
