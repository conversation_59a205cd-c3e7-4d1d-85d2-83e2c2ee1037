"use client";

import React, { createContext, useContext, ReactNode } from "react";

// Create a context to track if a component is inside a Container
type ContainerContextType = {
  isInsideContainer: boolean;
};

const ContainerContext = createContext<ContainerContextType>({
  isInsideContainer: false,
});

// Hook to use the container context
export const useContainerContext = () => useContext(ContainerContext);

// Provider component
export const ContainerProvider: React.FC<{
  children: ReactNode;
  isContainer?: boolean;
}> = ({ children, isContainer = true }) => {
  return (
    <ContainerContext.Provider value={{ isInsideContainer: isContainer }}>
      {children}
    </ContainerContext.Provider>
  );
};
