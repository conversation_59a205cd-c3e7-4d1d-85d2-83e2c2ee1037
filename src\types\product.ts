/**
 * Product related type definitions
 */

export type Product = {
  id: string;
  name: string;
  category: string;
  price: number;
  discount: boolean;
  discountPercentage?: number;
  oldPrice?: number;
  rating: number;
  image?: string;
  storeId?: string; // Optional for products not associated with a store
  reviewCount?: number;
  inStock?: boolean;
  description?: string;
  details?: ProductDetails;
  features?: string[];
  images?: string[];
  sizes?: string[];
  colors?: string[];
  tags?: string[];
};

export type ProductDetails = {
  composition?: string;
  properties?: string;
  style?: string;
  pattern?: string;
  care?: string;
  [key: string]: string | undefined; // Allow for additional properties
};

export type CartItem = {
  id: number;
  name: string;
  price: number;
  originalPrice: number;
  quantity: number;
  image: string;
  vendor?: string;
  color?: string;
};

export type SortOption = {
  label: string;
  value: string;
};

export type ActiveFilter = {
  type: string;
  value: string;
  label: string;
};

export type ProductSectionType = "recommended" | "featured" | "bestseller" | "products";
