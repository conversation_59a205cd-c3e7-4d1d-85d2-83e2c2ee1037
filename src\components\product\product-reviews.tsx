"use client";

import React from "react";
import { Star, ChevronLeft, ChevronRight } from "lucide-react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

// Define types for the component props
type Review = {
  id: number;
  author: string;
  rating: number;
  title: string;
  content: string;
  date: string;
  helpfulCount: number;
};

type ProductReviewsProps = {
  productName: string;
  rating: number;
  reviewCount: number;
  reviews?: Review[];
};

// Mock reviews data if not provided
const mockReviews: Review[] = [
  {
    id: 1,
    author: "<PERSON> Do<PERSON>",
    rating: 5,
    title: "Perfect fit and very comfortable!",
    content: "This sweater is amazing! The material is soft and comfortable, and the bear print is adorable. I've received many compliments when wearing it. Highly recommend!",
    date: "12 May, 2023",
    helpfulCount: 12,
  },
  {
    id: 2,
    author: "<PERSON>",
    rating: 4,
    title: "Nice sweater but runs large",
    content: "Great quality sweater! The size runs a bit large, so I would recommend sizing down if you prefer a more fitted look. Otherwise, it's perfect for casual wear during fall and winter.",
    date: "5 April, 2023",
    helpfulCount: 8,
  },
  {
    id: 3,
    author: "<PERSON> <PERSON>",
    rating: 5,
    title: "Excellent quality and design",
    content: "I bought this as a gift for my wife and she absolutely loves it! The bear design is cute without being too childish. The material is high quality and has held up well after several washes. Would definitely recommend.",
    date: "28 March, 2023",
    helpfulCount: 5,
  },
];

export function ProductReviews({ productName, rating, reviewCount, reviews = mockReviews }: ProductReviewsProps) {
  return (
    <div className="mt-16 pt-12 border-t border-gray-200 dark:border-gray-700">
      {/* Reviews Header */}
      <div className="flex items-center justify-between mb-8">
        <h2 className="text-xl font-bold dark:text-gray-100">Customer Reviews</h2>
        <div className="flex items-center gap-4">
          <div className="flex items-center">
            <div className="flex mr-2">
              {Array.from({ length: 5 }).map((_, i) => (
                <Star
                  key={i}
                  className={`h-5 w-5 ${i < Math.floor(rating) ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300 dark:text-gray-600'}`}
                />
              ))}
            </div>
            <span className="text-gray-700 dark:text-gray-300 font-medium">{rating} out of 5</span>
          </div>
          <Button
            className="bg-gradient-to-r from-primary to-secondary rounded-full text-white hover:shadow-md transition-all duration-300 hover:scale-[1.02]"
            size="sm"
          >
            Write a Review
          </Button>
        </div>
      </div>

      {/* Review Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-10">
        <Card className="shadow-sm border-gray-200 dark:border-gray-700 dark:bg-gray-800/50">
          <CardHeader>
            <CardTitle className="text-lg dark:text-gray-100">Rating Breakdown</CardTitle>
          </CardHeader>
          <CardContent>
            {[5, 4, 3, 2, 1].map((starRating) => (
              <div key={starRating} className="flex items-center mb-2">
                <span className="w-16 text-sm text-gray-600 dark:text-gray-400">{starRating} stars</span>
                <div className="flex-1 mx-3 bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 overflow-hidden">
                  <div
                    className={`h-2.5 rounded-full ${starRating >= 4 ? 'bg-gradient-to-r from-yellow-400 to-yellow-300' : 'bg-yellow-400'}`}
                    style={{ width: `${starRating === 5 ? 70 : starRating === 4 ? 20 : starRating === 3 ? 5 : starRating === 2 ? 3 : 2}%` }}
                  ></div>
                </div>
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {starRating === 5 ? 70 : starRating === 4 ? 20 : starRating === 3 ? 5 : starRating === 2 ? 3 : 2}%
                </span>
              </div>
            ))}
          </CardContent>
        </Card>

        <Card className="shadow-sm border-gray-200 dark:border-gray-700 dark:bg-gray-800/50">
          <CardHeader>
            <CardTitle className="text-lg dark:text-gray-100">Review this product</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 dark:text-gray-400 mb-4">Share your thoughts with other customers</p>
            <Button className="w-full bg-gradient-to-r from-primary to-secondary text-white hover:shadow-md rounded-full transition-all duration-300 hover:scale-[1.02]">
              Write a customer review
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Filter Reviews */}
      <div className="flex flex-wrap items-center gap-4 mb-6 pb-6 border-b border-gray-200 dark:border-gray-700">
        <span className="font-medium dark:text-gray-300">Filter reviews:</span>
        <div className="flex flex-wrap gap-2">
          <Button variant="outline" size="sm" className="rounded-full dark:border-gray-700 dark:text-gray-300 dark:hover:bg-gray-800/70 dark:hover:text-white hover:border-primary dark:hover:border-secondary">
            All stars
          </Button>
          {[5, 4, 3, 2, 1].map((starRating) => (
            <Button
              key={starRating}
              variant="outline"
              size="sm"
              className="rounded-full dark:border-gray-700 dark:text-gray-300 dark:hover:bg-gray-800/70 dark:hover:text-white hover:border-primary dark:hover:border-secondary flex items-center gap-1"
            >
              <Star className={`h-3 w-3 ${starRating === 5 ? 'fill-yellow-400 text-yellow-400' : ''}`} />
              {starRating} stars
            </Button>
          ))}
        </div>
        <div className="ml-auto">
          <Select defaultValue="recent">
            <SelectTrigger className="w-[180px] dark:border-gray-700 dark:bg-gray-800/50 dark:text-gray-300 hover:border-primary dark:hover:border-secondary">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent className="dark:bg-gray-800 dark:border-gray-700">
              <SelectItem value="recent" className="dark:text-gray-300 dark:focus:bg-gray-700 dark:hover:bg-gray-700">Most recent</SelectItem>
              <SelectItem value="highest" className="dark:text-gray-300 dark:focus:bg-gray-700 dark:hover:bg-gray-700">Highest rated</SelectItem>
              <SelectItem value="lowest" className="dark:text-gray-300 dark:focus:bg-gray-700 dark:hover:bg-gray-700">Lowest rated</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Reviews List */}
      <div className="space-y-6">
        {reviews.map((review) => (
          <Card key={review.id} className="shadow-sm border-gray-200 dark:border-gray-700 dark:bg-gray-800/50 hover:shadow-md transition-all duration-300">
            <CardContent className="p-6 space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium dark:text-gray-200">{review.author}</h4>
                  <div className="flex items-center gap-1 mt-1">
                    {Array.from({ length: 5 }).map((_, i) => (
                      <Star
                        key={i}
                        className={`h-4 w-4 ${i < review.rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300 dark:text-gray-600'}`}
                      />
                    ))}
                    <span className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                      {review.rating === 5 ? 'Excellent' : review.rating === 4 ? 'Good' : review.rating === 3 ? 'Average' : review.rating === 2 ? 'Poor' : 'Very Poor'}
                    </span>
                  </div>
                </div>
                <span className="text-xs text-gray-500 dark:text-gray-400">Posted on {review.date}</span>
              </div>
              <h5 className="font-medium text-gray-900 dark:text-gray-200">{review.title}</h5>
              <p className="text-gray-600 dark:text-gray-300">{review.content}</p>
              <div className="flex items-center gap-4 pt-2 text-sm text-gray-500 dark:text-gray-400">
                <Button variant="ghost" size="sm" className="flex items-center gap-1 hover:text-primary dark:hover:text-secondary transition-colors dark:hover:bg-gray-800/70">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5" />
                  </svg>
                  Helpful ({review.helpfulCount})
                </Button>
                <Button variant="ghost" size="sm" className="hover:text-primary dark:hover:text-secondary transition-colors dark:hover:bg-gray-800/70">
                  Report
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Pagination */}
      <div className="flex justify-center mt-8">
        <div className="flex items-center space-x-2">
          <Button 
            variant="outline" 
            size="icon" 
            className="rounded-full dark:border-gray-700 dark:hover:border-secondary"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            className="rounded-full dark:border-gray-700 hover:border-primary dark:hover:border-secondary"
          >1</Button>
          <Button 
            variant="default" 
            size="sm" 
            className="rounded-full hover:shadow-md"
          >2</Button>
          <Button 
            variant="outline" 
            size="sm" 
            className="rounded-full dark:border-gray-700 hover:border-primary dark:hover:border-secondary"
          >3</Button>
          <span className="px-2 dark:text-gray-400">...</span>
          <Button 
            variant="outline" 
            size="sm" 
            className="rounded-full dark:border-gray-700 hover:border-primary dark:hover:border-secondary"
          >8</Button>
          <Button 
            variant="outline" 
            size="sm" 
            className="rounded-full dark:border-gray-700 hover:border-primary dark:hover:border-secondary"
          >9</Button>
          <Button 
            variant="outline" 
            size="icon" 
            className="rounded-full dark:border-gray-700 dark:hover:border-secondary"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}


