# Styling Guidelines

This document provides an overview of the styling guidelines for the multi-tenant ecommerce application.

## Color Scheme

The application uses the following color scheme:

### Base Colors

- **Primary**: `#052E7F` (Deep Blue)
- **Secondary**: `#2A928F` (Teal)
- **Accent**: `#D9F7F5` (Light Teal)
- **Background**: `#FFFFFF` (White)

### Section Background Colors

- **Light Section**: `#F7F7F9` (Light Gray)
- **Accent Section**: `#E8F7F6` (Light Teal)

### Dark Mode Colors

- **Background**: `#0F172A` (Dark Blue)
- **Card**: `#1E293B` (Dark Blue-Gray)
- **Primary**: `#1A56DB` (Lighter Blue)
- **Secondary**: `#2A928F` (Teal - same as light mode)
- **Accent**: `#D9F7F5` (Light Teal - same as light mode)

## CSS Variables

The color scheme is implemented using CSS variables in `src/app/globals.css`:

```css
:root {
  --radius: 0.625rem;
  --background: #FFFFFF;
  --foreground: #000000;
  --card: #FFFFFF;
  --card-foreground: #000000;
  --popover: #FFFFFF;
  --popover-foreground: #000000;
  --primary: #052E7F;
  --primary-foreground: #FFFFFF;
  --secondary: #2A928F;
  --secondary-foreground: #FFFFFF;
  --muted: #F2F2F2;
  --muted-foreground: #6B7280;
  --accent: #D9F7F5;
  --accent-foreground: #052E7F;
  --destructive: #EF4444;
  --border: #E5E7EB;
  --input: #E5E7EB;
  --ring: #052E7F;
  /* Other variables... */
}

.dark {
  --background: #0F172A;
  --foreground: #F8FAFC;
  --card: #1E293B;
  --card-foreground: #F8FAFC;
  --popover: #1E293B;
  --popover-foreground: #F8FAFC;
  --primary: #1A56DB;
  --primary-foreground: #FFFFFF;
  --secondary: #2A928F;
  --secondary-foreground: #FFFFFF;
  --accent: #D9F7F5;
  --accent-foreground: #052E7F;
  /* Other variables... */
}
```

## Tailwind CSS Utilities

The application uses Tailwind CSS for styling. The following custom utilities are available:

### Gradient Backgrounds

```css
.bg-gradient-primary {
  @apply bg-gradient-to-r from-primary to-secondary;
}

.bg-gradient-secondary {
  @apply bg-gradient-to-r from-secondary to-accent;
}
```

### Section Backgrounds

```css
.bg-section-light {
  @apply bg-[#F7F7F9] dark:bg-section-dark;
}

.bg-section-accent {
  @apply bg-[#E8F7F6] dark:bg-section-accent-dark;
}
```

## Component Styling Guidelines

### Buttons

- Use gradient backgrounds for primary buttons
- Use fully rounded corners (border-radius: 0.625rem)
- Include hover effects
- Ensure proper dark mode support

```tsx
<Button className="bg-gradient-to-r from-primary to-secondary hover:opacity-90 text-white">
  Click Me
</Button>
```

### Cards

- Use thin border lines instead of shadows
- Follow the shadcn UI Card component design
- Ensure proper dark mode support

```tsx
<Card className="border border-border dark:border-border">
  <CardHeader>
    <CardTitle>Card Title</CardTitle>
    <CardDescription>Card Description</CardDescription>
  </CardHeader>
  <CardContent>
    Card Content
  </CardContent>
  <CardFooter>
    Card Footer
  </CardFooter>
</Card>
```

### Input Fields

- Style similar to the login form for consistency
- Avoid icons inside input fields
- Ensure proper dark mode support

```tsx
<Input
  type="text"
  placeholder="Enter text..."
  className="bg-background border-border"
/>
```

### Layout

- Use 90% width layout rather than full width
- Apply container width constraints only to md and larger devices
- Avoid redundant width constraints

```tsx
<div className="w-[90%] mx-auto">
  Content
</div>
```

### Grid Layout

- 3 columns for sm devices and regular iPads
- 4 columns for iPad Pro
- 5 columns for Featured sections

```tsx
<div className="grid grid-cols-1 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
  {items.map((item) => (
    <div key={item.id}>Item</div>
  ))}
</div>
```

## Dark Mode

The application uses the `next-themes` package for dark mode support. The theme provider is set up in `src/providers/theme-provider.tsx`.

### Dark Mode Toggle

The dark mode toggle is located in the top bar component for customer-facing pages and in the header for dashboard pages.

To toggle dark mode, use the `ThemeToggle` component:

```tsx
import { ThemeToggle } from "@/components/theme/theme-toggle";

export function MyComponent() {
  return (
    <div>
      <ThemeToggle />
    </div>
  );
}
```

### Dark Mode Implementation

All components should properly support dark mode, including:
- Proper background and text colors
- Hover states
- Borders and shadows
- Icons and images

For example, the product review section and sheet-cart component have been updated to support dark mode properly.

### Dark Mode for Dashboard

The superadmin and vendor dashboards have been updated to support dark mode with the updated primary and secondary colors. The sidebar, header, and content areas all properly support dark mode.

## Best Practices

1. **Use CSS Variables**
   - Use the CSS variables defined in `globals.css` for colors
   - Avoid hardcoding colors in components

2. **Follow Tailwind CSS Conventions**
   - Use Tailwind CSS utility classes for styling
   - Use the `@apply` directive for custom utilities

3. **Ensure Dark Mode Support**
   - Test all components in both light and dark mode
   - Use the `dark:` variant for dark mode styles

4. **Maintain Consistency**
   - Follow the established design patterns
   - Use the same styling for similar components

5. **Optimize for Responsiveness**
   - Use responsive utility classes
   - Test on different screen sizes

6. **Accessibility**
   - Ensure sufficient color contrast
   - Provide proper focus styles
   - Use semantic HTML elements
