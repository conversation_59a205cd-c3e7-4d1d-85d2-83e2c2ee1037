"use client";

import React, { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
  Store,
  Eye,
  EyeOff,
  Package,
  Building2
} from "lucide-react";

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

// Define form schema with Zod
const formSchema = z.object({
  storeName: z.string().min(2, { message: "Store name is required" }),
  phone: z.string().min(10, { message: "Please enter a valid phone number" }),
  password: z.string().min(8, { message: "Password must be at least 8 characters" }),
  confirmPassword: z.string(),
  agreeToTerms: z.boolean().refine(val => val === true, { message: "You must agree to the terms and conditions" })
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"],
});

type FormValues = z.infer<typeof formSchema>;

export default function VendorRegisterPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Initialize form with react-hook-form and zod validation
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      storeName: "",
      phone: "",
      password: "",
      confirmPassword: "",
      agreeToTerms: false,
    },
  });

  const onSubmit = async (data: FormValues) => {
    setIsLoading(true);

    try {
      // Mock registration - would be replaced with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log("Registration data:", data);

      // Redirect to vendor dashboard after successful registration
      router.push("/vendor/login?registered=true");
    } catch (error) {
      console.error("Registration failed:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex">
      {/* Left side - Benefits */}
      <div className="hidden md:flex md:w-1/2 bg-gradient-to-br from-primary to-secondary dark:from-primary/10 dark:to-secondary/40 p-8 items-center justify-center fixed left-0 h-screen">
        <div className="max-w-md text-white dark:text-accent">
          <div className="mb-8">
            <h2 className="text-3xl font-bold mb-4">Start Selling Today</h2>
            <p>Join thousands of merchants who trust our platform to expand their reach and increase sales.</p>
          </div>

          <div className="space-y-6">
            <div className="flex items-start space-x-4">
              <div className="bg-white/20 p-3 rounded-lg">
                <Package className="h-6 w-6" />
              </div>
              <div>
                <h3 className="font-medium text-lg mb-1">Easy Setup</h3>
                <p className="text-sm">Get your store up and running in minutes with our intuitive dashboard.</p>
              </div>
            </div>

            <div className="flex items-start space-x-4">
              <div className="bg-white/20 p-3 rounded-lg">
                <Store className="h-6 w-6" />
              </div>
              <div>
                <h3 className="font-medium text-lg mb-1">Powerful Tools</h3>
                <p className="text-sm">Access inventory management, order processing, and customer insights.</p>
              </div>
            </div>
          </div>

          <div className="mt-12 pt-6 border-t border-white/20">
            <div className="flex items-center">
              <p className="text-sm">Set up your store in less than 5 minutes</p>
            </div>
          </div>
        </div>
      </div>

      {/* Right side - Registration Form */}
      <div className="w-full md:w-1/2 md:ml-[50%] flex items-start justify-center p-6 bg-background overflow-y-auto min-h-screen">
        <div className="w-full max-w-md py-8">
          <div className="mb-8">
            <div className="flex items-center mb-2">
              <div className="bg-primary p-2 rounded-md mr-3">
                <Building2 className="h-5 w-5 text-primary-foreground" />
              </div>
              <h1 className="text-xl font-bold text-primary">Register Your Store</h1>
            </div>
            <p className="text-sm text-muted-foreground">Create your vendor account to start selling</p>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-5">
              {/* Store Name Field */}
              <FormField
                control={form.control}
                name="storeName"
                render={({ field }) => (
                  <FormItem className="space-y-2">
                    <FormLabel className="text-sm font-medium">
                      Store Name
                    </FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          placeholder="Your Store Name"
                          className="h-12 text-sm rounded-md"
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />

              {/* Phone Field */}
              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem className="space-y-2">
                    <FormLabel className="text-sm font-medium">
                      Phone Number
                    </FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          placeholder="Your Phone Number"
                          className="h-12 text-sm rounded-md"
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />

              {/* Password Field */}
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem className="space-y-2">
                    <FormLabel className="text-sm font-medium">
                      Password
                    </FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type={showPassword ? "text" : "password"}
                          placeholder="••••••••"
                          className="h-12 text-sm pr-10 rounded-md"
                          {...field}
                        />
                        <button
                          type="button"
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-secondary"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </button>
                      </div>
                    </FormControl>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />

              {/* Confirm Password Field */}
              <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem className="space-y-2">
                    <FormLabel className="text-sm font-medium">
                      Confirm Password
                    </FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type={showConfirmPassword ? "text" : "password"}
                          placeholder="••••••••"
                          className="h-12 text-sm pr-10 rounded-md"
                          {...field}
                        />
                        <button
                          type="button"
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-secondary"
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        >
                          {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </button>
                      </div>
                    </FormControl>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />

              {/* Terms and Conditions */}
              <FormField
                control={form.control}
                name="agreeToTerms"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 mt-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        className="h-4 w-4 data-[state=checked]:bg-secondary data-[state=checked]:border-secondary"
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel className="text-sm text-muted-foreground">
                        I agree to the <Link href="/terms" className="text-secondary hover:underline">Terms of Service</Link> and <Link href="/privacy" className="text-secondary hover:underline">Privacy Policy</Link>
                      </FormLabel>
                      <FormMessage className="text-xs" />
                    </div>
                  </FormItem>
                )}
              />

              {/* Register Button */}
              <Button
                type="submit"
                className="w-full h-12 mt-2 bg-primary hover:bg-primary/90 text-primary-foreground font-medium"
                disabled={isLoading}
              >
                {isLoading ? (
                  <span className="flex items-center">
                    <span className="mr-2">Creating account</span>
                    <span className="loading loading-dots"></span>
                  </span>
                ) : (
                  "Create Vendor Account"
                )}
              </Button>
            </form>
          </Form>

          {/* Divider */}
          <div className="relative my-8">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-border"></div>
            </div>
            <div className="relative flex justify-center text-xs">
              <span className="px-2 bg-background text-muted-foreground">Already have a store account?</span>
            </div>
          </div>

          {/* Login option */}
          <Button
            variant="outline"
            className="w-full h-12 text-sm border-border text-foreground hover:bg-secondary hover:border-secondary hover:text-secondary-foreground"
            onClick={() => router.push("/vendor/login")}
          >
            <Store className="mr-2 h-4 w-4" />
            Sign in to your store
          </Button>

          {/* Footer */}
          <div className="mt-12 text-center text-xs text-muted-foreground">
            <p>© 2023 Multi-Tenant E-commerce. All rights reserved.</p>
            <div className="mt-2 flex justify-center space-x-6">
              <Link href="/terms" className="hover:text-secondary">Terms</Link>
              <Link href="/privacy" className="hover:text-secondary">Privacy</Link>
              <Link href="/help" className="hover:text-secondary">Help</Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

