/**
 * Authentication related types
 */

/**
 * Login request payload
 */
export interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

/**
 * Login response data (wrapped in API response)
 */
export interface LoginResponseData {
  user: User;
  accessToken: string;
  refreshToken?: string;
  expiresIn: number;
}

/**
 * Login response following API pattern
 */
export interface LoginResponse {
  data: LoginResponseData;
  message: string;
  success: boolean;
  statusCode: number;
}

/**
 * User types in the multi-tenant system
 */
export type UserType = 'superadmin' | 'vendor' | 'customer';

/**
 * User interface
 */
export interface User {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  fullName?: string;
  avatar?: string;
  userType: UserType;
  isActive: boolean;
  isEmailVerified: boolean;
  createdAt: string;
  updatedAt: string;

  // Vendor specific fields
  vendorId?: string;
  storeName?: string;

  // Superadmin specific fields
  permissions?: string[];
  role?: string;
}

/**
 * Refresh token request
 */
export interface RefreshTokenRequest {
  refreshToken: string;
}

/**
 * Refresh token response data
 */
export interface RefreshTokenResponseData {
  accessToken: string;
  refreshToken?: string;
  expiresIn: number;
}

/**
 * Refresh token response following API pattern
 */
export interface RefreshTokenResponse {
  data: RefreshTokenResponseData;
  message: string;
  success: boolean;
  statusCode: number;
}

/**
 * Logout request
 */
export interface LogoutRequest {
  refreshToken?: string;
}

/**
 * Password reset request
 */
export interface ForgotPasswordRequest {
  email: string;
}

/**
 * Password reset response data
 */
export interface ForgotPasswordResponseData {
  message: string;
  resetToken?: string; // Only in development
}

/**
 * Password reset response following API pattern
 */
export interface ForgotPasswordResponse {
  data: ForgotPasswordResponseData;
  message: string;
  success: boolean;
  statusCode: number;
}

/**
 * Reset password request
 */
export interface ResetPasswordRequest {
  token: string;
  newPassword: string;
  confirmPassword: string;
}

/**
 * Change password request
 */
export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

/**
 * Register request payload
 */
export interface RegisterRequest {
  email: string;
  password: string;
  confirmPassword: string;
  firstName?: string;
  lastName?: string;
  userType?: UserType;

  // Vendor specific fields
  storeName?: string;
  storeDescription?: string;
}

/**
 * Register response data
 */
export interface RegisterResponseData {
  user: Omit<User, 'accessToken'>;
  requiresEmailVerification: boolean;
}

/**
 * Register response following API pattern
 */
export interface RegisterResponse {
  data: RegisterResponseData;
  message: string;
  success: boolean;
  statusCode: number;
}

/**
 * Email verification request
 */
export interface VerifyEmailRequest {
  token: string;
  email: string;
}

/**
 * Email verification response data
 */
export interface VerifyEmailResponseData {
  user: User;
}

/**
 * Email verification response following API pattern
 */
export interface VerifyEmailResponse {
  data: VerifyEmailResponseData;
  message: string;
  success: boolean;
  statusCode: number;
}

/**
 * Auth context state
 */
export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

/**
 * Auth context actions
 */
export interface AuthActions {
  login: (credentials: LoginRequest, userType?: UserType) => Promise<void>;
  logout: () => Promise<void>;
  register: (data: RegisterRequest) => Promise<void>;
  refreshToken: () => Promise<void>;
  clearError: () => void;
  updateUser: (user: Partial<User>) => void;
}

/**
 * Auth context type
 */
export interface AuthContextType extends AuthState, AuthActions {}
