"use client";

import React, { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { Container } from "@/components/ui/container";
import {
  Store,
  SlidersHorizontal,
  Star,
  Palette,
  Car,
  BookOpen,
  Cpu,
  Shirt,
  Smartphone,
  Gamepad,
  Gift,
  Apple,
  Stethoscope,
  Home,
  Laptop,
  TabletSmartphone,
  Music,
  Watch,
  Dumbbell,
  PenTool,
  Wrench,
  ChevronLeft,
  ChevronRight
} from "lucide-react";
import { CustomBreadcrumb } from "@/components/ui/custom-breadcrumb";

// Store type definition
type Store = {
  id: string;
  name: string;
  slug: string;
  image: string;
  rating: number;
  productCount: number;
  categories: string[];
  discount?: string;
  logo?: string;
};

// Featured stores data
const featuredStoresData: Store[] = [
  {
    id: "1",
    name: "Organic Delights",
    slug: "organic-delights",
    image: "/images/stores/berry.png",
    rating: 4.8,
    productCount: 347,
    categories: ["Organic", "Groceries", "Health Foods"],
    logo: "/images/categories/store-placeholder.png"
  },
  {
    id: "2",
    name: "Tech Haven",
    slug: "tech-haven",
    image: "/images/stores/headphone.png",
    rating: 4.6,
    productCount: 165,
    categories: ["Electronics", "Gadgets", "Smart Home"],
    logo: "/images/categories/store-placeholder.png"
  },
  {
    id: "3",
    name: "Style Avenue",
    slug: "style-avenue",
    image: "/images/stores/berry.png",
    rating: 4.7,
    productCount: 583,
    categories: ["Fashion", "Apparel", "Accessories"],
    logo: "/images/categories/store-placeholder.png"
  },
  {
    id: "4",
    name: "Home Essentials",
    slug: "home-essentials",
    image: "/images/stores/headphone.png",
    rating: 4.5,
    productCount: 429,
    categories: ["Home & Kitchen", "Furniture", "Decor"],
    discount: "50%",
    logo: "/images/categories/store-placeholder.png"
  },
  {
    id: "5",
    name: "Fitness World",
    slug: "fitness-world",
    image: "/images/stores/berry.png",
    rating: 4.9,
    productCount: 278,
    categories: ["Fitness", "Sports", "Outdoor"],
    logo: "/images/categories/store-placeholder.png"
  },
  {
    id: "6",
    name: "Beauty Boutique",
    slug: "beauty-boutique",
    image: "/images/stores/headphone.png",
    rating: 4.7,
    productCount: 412,
    categories: ["Beauty", "Skincare", "Makeup"],
    discount: "30%",
    logo: "/images/categories/store-placeholder.png"
  },
  {
    id: "7",
    name: "Kids Corner",
    slug: "kids-corner",
    image: "/images/stores/berry.png",
    rating: 4.6,
    productCount: 321,
    categories: ["Kids", "Toys", "Education"],
    logo: "/images/categories/store-placeholder.png"
  },
  {
    id: "8",
    name: "Pet Paradise",
    slug: "pet-paradise",
    image: "/images/stores/headphone.png",
    rating: 4.8,
    productCount: 198,
    categories: ["Pets", "Pet Food", "Pet Accessories"],
    discount: "20%",
    logo: "/images/categories/store-placeholder.png"
  },
  {
    id: "9",
    name: "Book Nook",
    slug: "book-nook",
    image: "/images/stores/berry.png",
    rating: 4.9,
    productCount: 512,
    categories: ["Books", "Stationery", "Gifts"],
    logo: "/images/categories/store-placeholder.png"
  },
  {
    id: "10",
    name: "Outdoor Adventure",
    slug: "outdoor-adventure",
    image: "/images/stores/headphone.png",
    rating: 4.7,
    productCount: 245,
    categories: ["Outdoor", "Camping", "Hiking"],
    discount: "25%",
    logo: "/images/categories/store-placeholder.png"
  },
  {
    id: "11",
    name: "Gourmet Kitchen",
    slug: "gourmet-kitchen",
    image: "/images/stores/berry.png",
    rating: 4.8,
    productCount: 367,
    categories: ["Food", "Kitchen", "Cooking"],
    logo: "/images/categories/store-placeholder.png"
  },
  {
    id: "12",
    name: "Tech Gadgets",
    slug: "tech-gadgets",
    image: "/images/stores/headphone.png",
    rating: 4.5,
    productCount: 289,
    categories: ["Electronics", "Gadgets", "Accessories"],
    discount: "15%",
    logo: "/images/categories/store-placeholder.png"
  },
  {
    id: "13",
    name: "Fashion Forward",
    slug: "fashion-forward",
    image: "/images/stores/berry.png",
    rating: 4.6,
    productCount: 478,
    categories: ["Fashion", "Clothing", "Accessories"],
    logo: "/images/categories/store-placeholder.png"
  },
  {
    id: "14",
    name: "Home Decor",
    slug: "home-decor",
    image: "/images/stores/headphone.png",
    rating: 4.7,
    productCount: 356,
    categories: ["Home", "Decor", "Furniture"],
    discount: "10%",
    logo: "/images/categories/store-placeholder.png"
  },
  {
    id: "15",
    name: "Sports Gear",
    slug: "sports-gear",
    image: "/images/stores/berry.png",
    rating: 4.8,
    productCount: 312,
    categories: ["Sports", "Fitness", "Outdoor"],
    logo: "/images/categories/store-placeholder.png"
  }
];

// Store categories with icons
const storeCategories = [
  {
    name: "All Stores",
    slug: "all-stores",
    icon: Store
  },
  {
    name: "Art & Crafts",
    slug: "art-crafts",
    icon: Palette
  },
  {
    name: "Automotive",
    slug: "automotive",
    icon: Car
  },
  {
    name: "Books",
    slug: "books",
    icon: BookOpen
  },
  {
    name: "Electronics",
    slug: "electronics",
    icon: Cpu
  },
  {
    name: "Fashion",
    slug: "fashion",
    icon: Shirt
  },
  {
    name: "Gadgets",
    slug: "gadgets",
    icon: Smartphone
  },
  {
    name: "Gaming",
    slug: "gaming",
    icon: Gamepad
  },
  {
    name: "Gifts",
    slug: "gifts",
    icon: Gift
  },
  {
    name: "Groceries",
    slug: "groceries",
    icon: Apple
  },
  {
    name: "Health",
    slug: "health",
    icon: Stethoscope
  },
  {
    name: "Home",
    slug: "home",
    icon: Home
  },
  {
    name: "Laptops",
    slug: "laptops",
    icon: Laptop
  },
  {
    name: "Mobile",
    slug: "mobile",
    icon: TabletSmartphone
  },
  {
    name: "Music",
    slug: "music",
    icon: Music
  },
  {
    name: "Watches",
    slug: "watches",
    icon: Watch
  },
  {
    name: "Fitness",
    slug: "fitness",
    icon: Dumbbell
  },
  {
    name: "Stationery",
    slug: "stationery",
    icon: PenTool
  },
  {
    name: "Tools",
    slug: "tools",
    icon: Wrench
  }
];



export default function StoresPage() {
  // State for filtered stores
  const [filteredStores, setFilteredStores] = useState<Store[]>(featuredStoresData);

  // State for active category
  const [activeCategory, setActiveCategory] = useState("All Stores");

  // State for active filters
  const [activeFilters, setActiveFilters] = useState({
    categories: [] as string[],
    ratings: null as number | null,
    sortBy: "popularity" // default sort
  });

  // State for scroll position in categories bar
  const [scrollPosition, setScrollPosition] = useState(0);
  const maxScroll = storeCategories.length * 100 - 1000; // Approximate width calculation

  // Handle category change
  const handleCategoryChange = (category: { name: string; slug: string }) => {
    setActiveCategory(category.name);

    if (category.name === "All Stores") {
      setFilteredStores(featuredStoresData);
    } else {
      const filtered = featuredStoresData.filter(store =>
        store.categories.some(cat => cat.toLowerCase().includes(category.name.toLowerCase()))
      );
      setFilteredStores(filtered);
    }
  };

  // Scroll controls for category bar
  const scrollLeft = () => {
    setScrollPosition(Math.max(0, scrollPosition - 300));
  };

  const scrollRight = () => {
    setScrollPosition(Math.min(maxScroll, scrollPosition + 300));
  };

  // Handle sort change
  const handleSortChange = (sortValue: string) => {
    setActiveFilters(prev => ({
      ...prev,
      sortBy: sortValue
    }));

    const sorted = [...filteredStores];

    switch (sortValue) {
      case "name_asc":
        sorted.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case "name_desc":
        sorted.sort((a, b) => b.name.localeCompare(a.name));
        break;
      case "rating":
        sorted.sort((a, b) => b.rating - a.rating);
        break;
      case "products":
        sorted.sort((a, b) => b.productCount - a.productCount);
        break;
    }

    setFilteredStores(sorted);
  };

  return (
    <div className="bg-background dark:bg-background min-h-screen pb-8">
      {/* Breadcrumb */}
      <CustomBreadcrumb
        items={[
          { label: "Home", href: "/" },
          { label: "Stores" }
        ]}
        backgroundColor="bg-[#E8F7F6]"
      />

      {/* Categories Section */}
      <div className="bg-[#F7F7F9] dark:bg-primary/10 mb-6">
        <Container>
          <div className="relative overflow-hidden">
            {/* Left scroll button */}
            {scrollPosition > 0 && (
              <button
                onClick={scrollLeft}
                className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-gradient-to-r from-[#F7F7F9] via-[#F7F7F9]/95 to-transparent dark:from-primary/10 dark:via-primary/10 pl-1 pr-3 py-1 text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-accent transition-colors"
              >
                <ChevronLeft className="h-4 w-4" />
              </button>
            )}

            {/* Categories scroll container */}
            <div className="overflow-x-hidden">
              <div
                className="flex space-x-3 py-3 transition-transform duration-300 ease-out"
                style={{ transform: `translateX(-${scrollPosition}px)` }}
              >
                {storeCategories.map((category) => (
                  <button
                    key={category.slug}
                    onClick={() => handleCategoryChange(category)}
                    className={`flex items-center gap-1.5 whitespace-nowrap px-3 py-1.5 rounded-full border ${
                      activeCategory === category.name
                        ? "bg-gradient-primary text-white border-transparent"
                        : "border-gray-100 dark:border-gray-700 hover:border-primary/30 dark:hover:border-accent/30 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-accent hover:bg-primary/5 dark:hover:bg-accent/10"
                    } transition-colors flex-shrink-0`}
                  >
                    <category.icon className={`h-3.5 w-3.5 ${
                      activeCategory === category.name ? "text-white" : "text-primary dark:text-accent"
                    }`} />
                    <span className={`text-xs font-medium ${
                      activeCategory === category.name ? "text-white" : "text-primary dark:text-accent"
                    }`}>{category.name}</span>
                  </button>
                ))}
              </div>
            </div>

            {/* Right scroll button */}
            {scrollPosition < maxScroll && (
              <button
                onClick={scrollRight}
                className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-gradient-to-l from-[#F7F7F9] via-[#F7F7F9]/95 to-transparent dark:from-primary/10 dark:via-primary/10 pr-1 pl-3 py-1 text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-accent transition-colors"
              >
                <ChevronRight className="h-4 w-4" />
              </button>
            )}
          </div>
        </Container>
      </div>

      <Container className="mt-8">
        {/* Sorting and Filters */}
        <div className="flex flex-wrap items-center justify-between mb-6 gap-4">
          <div className="flex items-center">
            <span className="text-sm text-gray-500 dark:text-gray-400">
              Showing <span className="font-medium text-gray-900 dark:text-gray-200">{filteredStores.length}</span> stores
            </span>
          </div>

          <div className="flex items-center gap-3">
            <div className="relative">
              <select
                className="appearance-none bg-white dark:bg-card border border-gray-300 dark:border-gray-700 text-gray-700 dark:text-gray-300 py-2 px-4 pr-8 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-accent focus:border-primary dark:focus:border-accent text-sm hover:border-primary dark:hover:border-accent transition-colors"
                value={activeFilters.sortBy}
                onChange={(e) => handleSortChange(e.target.value)}
              >
                <option value="popularity">Sort by: Popularity</option>
                <option value="name_asc">Sort by: Name (A-Z)</option>
                <option value="name_desc">Sort by: Name (Z-A)</option>
                <option value="rating">Sort by: Rating</option>
                <option value="products">Sort by: Products</option>
              </select>
              <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-400">
                <SlidersHorizontal className="h-4 w-4" />
              </div>
            </div>
          </div>
        </div>

        {/* Stores Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
          {filteredStores.map((store) => (
            <Link href={`/store/${store.slug}`} key={store.id} className="block group">
              <div className="relative rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700 bg-white dark:bg-card shadow-[0_2px_8px_rgba(0,0,0,0.08)] dark:shadow-[0_2px_8px_rgba(0,0,0,0.2)] hover:shadow-[0_4px_12px_rgba(0,0,0,0.12)] dark:hover:shadow-[0_4px_12px_rgba(0,0,0,0.3)] transition-all duration-300 h-[280px] group-hover:translate-y-[-2px]">
                {/* Store Banner Image */}
                <div className="relative h-[140px] w-full overflow-hidden border-b border-gray-100 dark:border-gray-700">
                  {/* Featured Badge */}
                  <div className="absolute top-2 left-2 z-10 bg-gradient-primary text-white text-xs font-medium px-2 py-0.5 rounded-full">
                    Featured
                  </div>

                  {/* Discount Badge (if applicable) */}
                  {store.discount && (
                    <div className="absolute top-2 right-2 z-10 bg-gradient-secondary text-white text-xs font-medium px-2 py-0.5 rounded-full">
                      {store.discount}
                    </div>
                  )}

                  {/* Banner Image */}
                  <Image
                    src={store.image}
                    alt={store.name}
                    fill
                    className="object-cover transition-transform duration-300 group-hover:scale-105"
                  />

                  {/* Store Logo */}
                  {store.logo && (
                    <div className="absolute -bottom-6 left-4 h-12 w-12 rounded-full border-2 border-white dark:border-gray-700 bg-white dark:bg-card overflow-hidden shadow-md">
                      <Image
                        src={store.logo}
                        alt={`${store.name} logo`}
                        fill
                        className="object-cover"
                      />
                    </div>
                  )}
                </div>

                {/* Store Details */}
                <div className="p-4 pt-8">
                  <h3 className="font-semibold text-gray-900 dark:text-accent mb-1">{store.name}</h3>

                  {/* Rating */}
                  <div className="flex items-center mb-2">
                    <div className="flex items-center">
                      {Array.from({ length: 5 }).map((_, i) => (
                        <Star
                          key={i}
                          className={`h-3.5 w-3.5 ${
                            i < Math.floor(store.rating)
                              ? "fill-yellow-400 text-yellow-400"
                              : "text-gray-300 dark:text-gray-600"
                          }`}
                        />
                      ))}
                    </div>
                    <span className="text-xs text-gray-600 dark:text-gray-400 ml-1">{store.rating}</span>
                  </div>

                  {/* Product Count */}
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {store.productCount} Products
                  </div>

                  {/* Categories */}
                  <div className="mt-2 flex flex-wrap gap-1">
                    {store.categories.slice(0, 2).map((category, index) => (
                      <span
                        key={index}
                        className="inline-block bg-gray-100 dark:bg-primary/20 text-gray-800 dark:text-accent text-[10px] px-2 py-0.5 rounded-full"
                      >
                        {category}
                      </span>
                    ))}
                    {store.categories.length > 2 && (
                      <span className="inline-block text-[10px] text-gray-500 dark:text-gray-400">
                        +{store.categories.length - 2} more
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </Container>
    </div>
  );
}
