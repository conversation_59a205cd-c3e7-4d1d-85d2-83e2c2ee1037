"use client"

import * as React from "react"
import { DashboardSidebar } from "@/components/dashboard/sidebar"
import { superadminNavData } from "@/data/superadmin-nav"
import { Sidebar } from "@/components/ui/sidebar"
import { DashboardUserInfo } from "@/types/navigation"

/**
 * SuperAdmin sidebar component
 *
 * This component uses the shared DashboardSidebar component with superadmin-specific
 * navigation data and user information.
 */
export function SuperAdminSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  // SuperAdmin user information
  const superAdminUserInfo: DashboardUserInfo = {
    displayName: "Super Admin",
    email: "<EMAIL>",
    avatarSrc: "/avatars/admin.jpg",
    avatarFallback: "SA",
    logoutUrl: "/superadmin/logout"
  };

  return (
    <DashboardSidebar
      navData={superadminNavData}
      logoText="Multi-Tenant"
      logoInitials="ME"
      userInfo={superAdminUserInfo}
      {...props}
    />
  )
}
