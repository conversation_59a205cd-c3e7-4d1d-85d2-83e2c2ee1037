"use client"

import React from "react"
import { Bell, MessageSquare, Search } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ThemeToggle } from "@/components/theme/theme-toggle"

/**
 * Vendor header component
 *
 * This component renders the header for the vendor dashboard,
 * including a search input, messages button, notifications button, and theme toggle.
 */
export function VendorHeader() {
  return (
    <div className="flex w-full items-center justify-between">
      <div className="relative w-full max-w-md">
        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          type="search"
          placeholder="Search orders, products..."
          className="w-full bg-background pl-8 md:w-[240px] lg:w-[320px]"
          aria-label="Search orders and products"
        />
      </div>
      <div className="flex items-center gap-2">
        <Button
          variant="ghost"
          size="icon"
          className="relative hover:bg-accent/10 hover:text-accent-foreground"
          aria-label="Messages"
        >
          <MessageSquare className="h-5 w-5" />
          <span className="absolute right-1.5 top-1.5 flex h-2 w-2 rounded-full bg-primary"></span>
          <span className="sr-only">Messages</span>
        </Button>
        <Button
          variant="ghost"
          size="icon"
          className="relative hover:bg-accent/10 hover:text-accent-foreground"
          aria-label="Notifications"
        >
          <Bell className="h-5 w-5" />
          <span className="absolute right-1.5 top-1.5 flex h-2 w-2 rounded-full bg-primary"></span>
          <span className="sr-only">Notifications</span>
        </Button>
        <ThemeToggle />
      </div>
    </div>
  )
}
