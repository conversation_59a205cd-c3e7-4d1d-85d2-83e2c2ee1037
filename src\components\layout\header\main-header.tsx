"use client";

import React, { useState, useEffect, useRef } from "react";
import Image from "next/image";
import Link from "next/link";
import {
  Search,
  User,
  Heart,
  ShoppingCart,
  ChevronDown,
  Smartphone,
  Shirt,
  Sofa,
  Sparkles,
  ChevronRight,
  Bell,
  BookOpen,
  <PERSON><PERSON><PERSON>
} from "lucide-react";
import { Container } from "@/components/ui/container";

import { SearchSuggestions } from "./search-suggestions";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";

interface MainHeaderProps {
  onCartClick: () => void;
}

export function MainHeader({ onCartClick }: MainHeaderProps) {
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [notifications, setNotifications] = useState(2);
  const searchRef = useRef<HTMLDivElement>(null);

  // Handle click outside search suggestions
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsSearchFocused(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);
  return (
    <div className="w-full py-3 sm:py-4">
      <Container>
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center">
            <Image
              src="/logo.png"
              alt="E-Come Logo"
              width={120}
              height={35}
              className="w-auto h-7 sm:h-8 md:h-10"
              priority
            />
          </Link>

          {/* Search Bar - Hidden on mobile, visible on tablets and up */}
          <div className="flex-1 max-w-xl mx-2 xs:mx-3 sm:mx-4 md:mx-6 hidden sm:block" ref={searchRef}>
            <div className={`relative flex h-9 sm:h-10 md:h-11 rounded-full border ${isSearchFocused ? 'border-primary/40 dark:border-primary/60 shadow-md' : 'border-border shadow-sm'} overflow-hidden hover:shadow-md dark:hover:shadow-none dark:hover:border-primary/40 transition-all duration-300 focus-within:border-primary/40 dark:focus-within:border-primary/60 focus-within:shadow-md dark:focus-within:shadow-none group bg-background`}>
              {/* Search Input */}
              <div className="relative flex-1 flex items-center">
                <Search className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-muted-foreground absolute left-3 sm:left-4" />
                <input
                  type="text"
                  placeholder="I'm shopping for..."
                  className="flex-1 h-full border-none focus:outline-none focus:ring-0 pl-8 sm:pl-10 pr-3 sm:pr-5 text-xs sm:text-sm bg-background text-foreground"
                  onFocus={() => setIsSearchFocused(true)}
                />
              </div>

              {/* Divider */}
              <div className="w-px h-5 sm:h-6 self-center bg-border/50"></div>

              {/* Search Suggestions */}
              <SearchSuggestions
                isOpen={isSearchFocused}
                onClose={() => setIsSearchFocused(false)}
              />

              {/* Categories Dropdown */}
              <div className="h-full flex items-center">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <button className="h-full flex items-center gap-1 px-2 sm:px-3 text-xs font-medium text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-colors duration-200 focus:outline-none">
                      <span className="hidden xs:inline">All Categories</span>
                      <span className="inline xs:hidden">Categories</span>
                      <ChevronDown className="h-3 w-3 sm:h-3.5 sm:w-3.5 text-gray-400 dark:text-gray-500 transition-colors duration-200" />
                    </button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-[280px] xs:w-[300px] sm:w-[320px] p-0 rounded-xl shadow-lg dark:shadow-none border bg-card border-border">
                    {/* Search within categories */}
                    <div className="p-2 sm:p-3 border-b border-border/50">
                      <div className="relative">
                        <input
                          type="text"
                          placeholder="Search in categories..."
                          className="w-full h-8 sm:h-9 px-3 pl-8 sm:pl-9 text-xs rounded-lg border border-input bg-background text-foreground focus:outline-none focus:ring-1 focus:ring-primary/30 dark:focus:ring-primary/50 focus:border-primary/30 dark:focus:border-primary/50"
                        />
                        <Search className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-muted-foreground absolute left-3 top-1/2 -translate-y-1/2" />
                      </div>
                    </div>

                    <div className="p-3">
                      {/* Popular Categories */}
                      <DropdownMenuLabel className="font-semibold text-xs text-muted-foreground uppercase tracking-wider mb-2">
                        Popular Categories
                      </DropdownMenuLabel>
                      <div className="grid grid-cols-2 gap-2 mb-4">
                        <Link href="/category/electronics" className="flex flex-col items-center gap-1 p-2 rounded-lg hover:bg-accent/10 text-foreground hover:text-primary transition-colors group">
                          <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                            <Smartphone className="h-5 w-5 text-primary" />
                          </div>
                          <span className="text-xs font-medium text-center">Electronics</span>
                        </Link>
                        <Link href="/category/fashion" className="flex flex-col items-center gap-1 p-2 rounded-lg hover:bg-accent/10 text-foreground hover:text-primary transition-colors group">
                          <div className="h-10 w-10 rounded-full bg-secondary/10 flex items-center justify-center group-hover:bg-secondary/20 transition-colors">
                            <Shirt className="h-5 w-5 text-secondary" />
                          </div>
                          <span className="text-xs font-medium text-center">Fashion</span>
                        </Link>
                        <Link href="/category/home" className="flex flex-col items-center gap-1 p-2 rounded-lg hover:bg-accent/10 text-foreground hover:text-primary transition-colors group">
                          <div className="h-10 w-10 rounded-full bg-accent/30 flex items-center justify-center group-hover:bg-accent/40 transition-colors">
                            <Sofa className="h-5 w-5 text-secondary" />
                          </div>
                          <span className="text-xs font-medium text-center">Home</span>
                        </Link>
                        <Link href="/category/beauty" className="flex flex-col items-center gap-1 p-2 rounded-lg hover:bg-accent/10 text-foreground hover:text-primary transition-colors group">
                          <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                            <Sparkles className="h-5 w-5 text-primary" />
                          </div>
                          <span className="text-xs font-medium text-center">Beauty</span>
                        </Link>
                      </div>

                      {/* All Categories List */}
                      <DropdownMenuLabel className="font-semibold text-xs text-muted-foreground uppercase tracking-wider mb-2">
                        All Categories
                      </DropdownMenuLabel>
                      <div className="space-y-1">
                        <DropdownMenuItem asChild>
                          <Link href="/category/electronics" className="flex items-center gap-2 rounded-lg text-foreground hover:text-primary">
                            <Smartphone className="h-4 w-4 text-primary" />
                            <span className="text-xs">Electronics</span>
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href="/category/fashion" className="flex items-center gap-2 rounded-lg text-foreground hover:text-primary">
                            <Shirt className="h-4 w-4 text-secondary" />
                            <span className="text-xs">Fashion</span>
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href="/category/home" className="flex items-center gap-2 rounded-lg text-foreground hover:text-primary">
                            <Sofa className="h-4 w-4 text-accent" />
                            <span className="text-xs">Home & Kitchen</span>
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href="/category/beauty" className="flex items-center gap-2 rounded-lg text-foreground hover:text-primary">
                            <Sparkles className="h-4 w-4 text-primary" />
                            <span className="text-xs">Beauty & Health</span>
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href="/category/books" className="flex items-center gap-2 rounded-lg text-foreground hover:text-primary">
                            <BookOpen className="h-4 w-4 text-accent" />
                            <span className="text-xs">Books & Media</span>
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href="/category/sports" className="flex items-center gap-2 rounded-lg text-foreground hover:text-primary">
                            <Dumbbell className="h-4 w-4 text-secondary" />
                            <span className="text-xs">Sports & Fitness</span>
                          </Link>
                        </DropdownMenuItem>
                      </div>
                    </div>

                    {/* View All Button */}
                    <div className="p-3 border-t border-border/50">
                      <Link href="/categories" className="flex items-center justify-center gap-1 bg-primary text-white py-2 rounded-lg text-xs font-medium hover:opacity-90 transition-opacity">
                        <span>Browse All Categories</span>
                        <ChevronRight className="h-3 w-3" />
                      </Link>
                    </div>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              {/* Divider */}
              <div className="w-px h-6 self-center bg-border/50"></div>

              {/* Search Button */}
              <button
                type="submit"
                className="h-full px-2 sm:px-2 flex items-center justify-center hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors duration-200"
              >
                <div className="h-7 w-7 sm:h-8 sm:w-8 rounded-full bg-primary hover:opacity-90 flex items-center justify-center transition-all duration-300 shadow-sm dark:shadow-none">
                  <Search className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-white" />
                </div>
              </button>
            </div>
          </div>

          {/* Contact & Icons */}
          <div className="flex items-center space-x-1 xs:space-x-2 sm:space-x-3 md:space-x-4 lg:space-x-6">
            {/* Call Us - Only visible on large screens */}
            <div className="hidden lg:flex flex-col items-end">
              <span className="text-xs text-gray-500">Call Us Free</span>
              <span className="text-sm font-medium">(+123) 456 789</span>
            </div>

            <div className="flex items-center space-x-1 xs:space-x-2 sm:space-x-3 md:space-x-4">
              {/* User Account */}
              <Link href="/account" className="hover:text-primary transition-colors duration-200 p-1 sm:p-1.5 rounded-full hover:bg-accent/10">
                <User className="h-4 w-4 sm:h-5 sm:w-5" />
              </Link>

              {/* Wishlist - Hidden on mobile, visible on small tablets and up */}
              <Link href="/wishlist" className="relative hover:text-primary transition-colors duration-200 p-1 sm:p-1.5 rounded-full hover:bg-accent/10 hidden xs:block">
                <Heart className="h-4 w-4 sm:h-5 sm:w-5" />
                <span className="absolute -top-1 -right-1 flex items-center justify-center w-3.5 h-3.5 sm:w-4 sm:h-4 bg-secondary text-white text-[9px] sm:text-[10px] rounded-full shadow-sm font-medium">
                  2
                </span>
              </Link>

              {/* Notifications - Hidden on mobile and small tablets, visible on medium tablets and up */}
              <button
                className="relative hover:text-primary transition-colors duration-200 p-1 sm:p-1.5 rounded-full hover:bg-accent/10 hidden md:block"
                onClick={() => setNotifications(0)}
              >
                <Bell className="h-4 w-4 sm:h-5 sm:w-5" />
                {notifications > 0 && (
                  <span className="absolute -top-1 -right-1 flex items-center justify-center w-3.5 h-3.5 sm:w-4 sm:h-4 bg-accent text-secondary text-[9px] sm:text-[10px] rounded-full shadow-sm font-medium">
                    {notifications}
                  </span>
                )}
              </button>

              {/* Cart */}
              <div className="relative">
                <button
                  className="relative hover:text-primary transition-colors duration-200 p-1 sm:p-1.5 rounded-full hover:bg-accent/10"
                  onClick={onCartClick}
                >
                  <ShoppingCart className="h-4 w-4 sm:h-5 sm:w-5" />
                  <span className="absolute -top-1 -right-1 flex items-center justify-center w-3.5 h-3.5 sm:w-4 sm:h-4 bg-primary text-white text-[9px] sm:text-[10px] rounded-full shadow-sm font-medium">
                    3
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Mobile Search - Only visible on small screens */}
        <div className="mt-2 sm:hidden">
          <div className="relative flex h-9 rounded-full border border-border overflow-hidden shadow-sm hover:shadow-md dark:hover:shadow-none dark:hover:border-primary/40 transition-all duration-300 focus-within:border-primary/40 dark:focus-within:border-primary/60 focus-within:shadow-md dark:focus-within:shadow-none group bg-background">
            {/* Search Input */}
            <div className="relative flex-1 flex items-center">
              <Search className="h-3 w-3 text-muted-foreground absolute left-3" />
              <input
                type="text"
                placeholder="I'm shopping for..."
                className="flex-1 h-full border-none focus:outline-none focus:ring-0 pl-7 pr-3 text-xs bg-background text-foreground"
              />
            </div>

            {/* Divider */}
            <div className="w-px h-4 self-center bg-border/50"></div>

            {/* Categories Dropdown */}
            <div className="h-full flex items-center">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <button className="h-full flex items-center gap-1 px-2 text-xs font-medium text-gray-600 hover:text-primary transition-colors duration-200 focus:outline-none">
                    <span>All</span>
                    <ChevronDown className="h-2.5 w-2.5 text-gray-400 transition-colors duration-200" />
                  </button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-[220px] p-2 rounded-xl shadow-lg border border-border bg-card">
                  {/* Popular Categories */}
                  <div className="mb-2">
                    <h3 className="font-semibold text-[10px] mb-1.5 text-muted-foreground uppercase tracking-wider">Popular</h3>
                    <div className="grid grid-cols-2 gap-1">
                      <Link href="/category/electronics" className="flex flex-col items-center gap-0.5 p-1 rounded-lg hover:bg-accent/10 text-foreground hover:text-primary transition-colors group">
                        <div className="h-7 w-7 rounded-full bg-primary/10 flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                          <Smartphone className="h-3.5 w-3.5 text-primary" />
                        </div>
                        <span className="text-[9px] font-medium text-center">Electronics</span>
                      </Link>
                      <Link href="/category/fashion" className="flex flex-col items-center gap-0.5 p-1 rounded-lg hover:bg-accent/10 text-foreground hover:text-primary transition-colors group">
                        <div className="h-7 w-7 rounded-full bg-secondary/10 flex items-center justify-center group-hover:bg-secondary/20 transition-colors">
                          <Shirt className="h-3.5 w-3.5 text-secondary" />
                        </div>
                        <span className="text-[9px] font-medium text-center">Fashion</span>
                      </Link>
                      <Link href="/category/home" className="flex flex-col items-center gap-0.5 p-1 rounded-lg hover:bg-accent/10 text-foreground hover:text-primary transition-colors group">
                        <div className="h-7 w-7 rounded-full bg-accent/10 flex items-center justify-center group-hover:bg-accent/20 transition-colors">
                          <Sofa className="h-3.5 w-3.5 text-accent" />
                        </div>
                        <span className="text-[9px] font-medium text-center">Home</span>
                      </Link>
                      <Link href="/category/beauty" className="flex flex-col items-center gap-0.5 p-1 rounded-lg hover:bg-accent/10 text-foreground hover:text-primary transition-colors group">
                        <div className="h-7 w-7 rounded-full bg-primary/10 flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                          <Sparkles className="h-3.5 w-3.5 text-primary" />
                        </div>
                        <span className="text-[9px] font-medium text-center">Beauty</span>
                      </Link>
                    </div>
                  </div>

                  {/* All Categories */}
                  <div className="mb-2">
                    <h3 className="font-semibold text-[10px] mb-1 text-muted-foreground uppercase tracking-wider">All Categories</h3>
                    <div className="grid grid-cols-1 gap-0.5">
                      <Link href="/category/electronics" className="flex items-center gap-1.5 px-2 py-1 rounded-lg hover:bg-accent/10 text-foreground hover:text-primary transition-colors">
                        <Smartphone className="h-3 w-3 text-primary" />
                        <span className="text-[10px]">Electronics</span>
                      </Link>
                      <Link href="/category/fashion" className="flex items-center gap-1.5 px-2 py-1 rounded-lg hover:bg-accent/10 text-foreground hover:text-primary transition-colors">
                        <Shirt className="h-3 w-3 text-secondary" />
                        <span className="text-[10px]">Fashion</span>
                      </Link>
                      <Link href="/category/home" className="flex items-center gap-1.5 px-2 py-1 rounded-lg hover:bg-accent/10 text-foreground hover:text-primary transition-colors">
                        <Sofa className="h-3 w-3 text-accent" />
                        <span className="text-[10px]">Home & Kitchen</span>
                      </Link>
                    </div>
                  </div>

                  {/* View All Button */}
                  <div className="mt-1.5 pt-1.5 border-t border-border/50">
                    <Link href="/categories" className="flex items-center justify-center gap-1 bg-primary text-white py-1 rounded-lg text-[10px] font-medium hover:opacity-90 transition-opacity">
                      <span>Browse All Categories</span>
                      <ChevronRight className="h-2.5 w-2.5" />
                    </Link>
                  </div>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {/* Divider */}
            <div className="w-px h-4 self-center bg-border/50"></div>

            {/* Search Button */}
            <button
              type="submit"
              className="h-full px-2 flex items-center justify-center hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors duration-200"
            >
              <div className="h-6 w-6 rounded-full bg-primary hover:opacity-90 flex items-center justify-center transition-all duration-300 shadow-sm dark:shadow-none">
                <Search className="h-3 w-3 text-white" />
              </div>
            </button>
          </div>
        </div>
      </Container>
    </div>
  );
}
