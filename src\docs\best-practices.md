# Best Practices

This document provides an overview of the best practices for the multi-tenant ecommerce application.

## Code Organization

### Directory Structure

- Keep related files together in the same directory
- Use a consistent naming convention for files
- Organize components by feature or page

```
src/
├── app/                    # Next.js app router pages
│   ├── (auth)/             # Authentication pages (customer)
│   ├── superadmin/         # Superadmin pages
│   │   ├── dashboard/      # Superadmin dashboard
│   │   └── login/          # Superadmin login
│   ├── vendor/             # Vendor pages
│   │   ├── (auth)/         # Vendor authentication pages
│   │   └── dashboard/      # Vendor dashboard
│   └── ...                 # Other customer-facing pages
├── components/             # Reusable components
│   ├── dashboard/          # Shared dashboard components
│   │   ├── layout.tsx      # Base dashboard layout
│   │   └── sidebar.tsx     # Base dashboard sidebar
│   ├── layout/             # Layout components
│   │   ├── customer/       # Customer layout components
│   │   ├── superadmin/     # Superadmin layout components
│   │   ├── vendor/         # Vendor layout components
│   │   └── layout-provider.tsx # Layout provider component
│   ├── ui/                 # UI components (shadcn)
│   └── ...                 # Other components
├── data/                   # Data files
│   ├── superadmin-nav.ts   # Superadmin navigation data
│   └── vendor-nav.ts       # Vendor navigation data
├── docs/                   # Documentation
├── hooks/                  # Custom hooks
├── lib/                    # Utility functions
├── providers/              # Context providers
└── types/                  # TypeScript type definitions
```

### Component Organization

- Keep components small and focused on a single responsibility
- Use composition to build complex components
- Extract reusable logic into custom hooks
- Use TypeScript interfaces for component props

```tsx
// Bad
function MyComponent() {
  // Too many responsibilities in one component
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    setLoading(true);
    fetch('/api/data')
      .then(res => res.json())
      .then(data => {
        setData(data);
        setLoading(false);
      })
      .catch(err => {
        setError(err);
        setLoading(false);
      });
  }, []);

  return (
    <div>
      {loading && <Spinner />}
      {error && <Error message={error.message} />}
      {!loading && !error && (
        <ul>
          {data.map(item => (
            <li key={item.id}>{item.name}</li>
          ))}
        </ul>
      )}
    </div>
  );
}

// Good
function useData() {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    setLoading(true);
    fetch('/api/data')
      .then(res => res.json())
      .then(data => {
        setData(data);
        setLoading(false);
      })
      .catch(err => {
        setError(err);
        setLoading(false);
      });
  }, []);

  return { data, loading, error };
}

function DataList({ data }) {
  return (
    <ul>
      {data.map(item => (
        <li key={item.id}>{item.name}</li>
      ))}
    </ul>
  );
}

function MyComponent() {
  const { data, loading, error } = useData();

  if (loading) return <Spinner />;
  if (error) return <Error message={error.message} />;

  return <DataList data={data} />;
}
```

## TypeScript

### Type Definitions

- Define proper types for all components and functions
- Use interfaces for object shapes and types for unions
- Avoid using `any` type
- Use TypeScript's utility types when appropriate

```tsx
// Bad
function MyComponent(props: any) {
  return <div>{props.title}</div>;
}

// Good
interface MyComponentProps {
  title: string;
  description?: string;
}

function MyComponent({ title, description }: MyComponentProps) {
  return (
    <div>
      <h1>{title}</h1>
      {description && <p>{description}</p>}
    </div>
  );
}
```

### Type Safety

- Use TypeScript's strict mode
- Avoid type assertions unless necessary
- Use type guards to narrow types
- Use generics for reusable components and functions

```tsx
// Bad
function getProperty(obj: any, key: string) {
  return obj[key];
}

// Good
function getProperty<T, K extends keyof T>(obj: T, key: K): T[K] {
  return obj[key];
}
```

## State Management

### React Hooks

- Use React hooks for local state
- Use context for shared state
- Keep state as close as possible to where it's used
- Use reducers for complex state logic

```tsx
// Bad
const GlobalState = createContext(null);

function App() {
  const [user, setUser] = useState(null);
  const [cart, setCart] = useState([]);
  const [products, setProducts] = useState([]);

  return (
    <GlobalState.Provider value={{ user, setUser, cart, setCart, products, setProducts }}>
      <AppContent />
    </GlobalState.Provider>
  );
}

// Good
const UserContext = createContext(null);
const CartContext = createContext(null);
const ProductsContext = createContext(null);

function App() {
  return (
    <UserProvider>
      <CartProvider>
        <ProductsProvider>
          <AppContent />
        </ProductsProvider>
      </CartProvider>
    </UserProvider>
  );
}
```

### State Updates

- Use functional updates for state that depends on previous state
- Avoid direct mutation of state
- Use immutable update patterns

```tsx
// Bad
function Counter() {
  const [count, setCount] = useState(0);

  const increment = () => {
    setCount(count + 1);
  };

  return (
    <button onClick={increment}>
      Count: {count}
    </button>
  );
}

// Good
function Counter() {
  const [count, setCount] = useState(0);

  const increment = () => {
    setCount(prevCount => prevCount + 1);
  };

  return (
    <button onClick={increment}>
      Count: {count}
    </button>
  );
}
```

## Performance

### Memoization

- Use `useMemo` for expensive calculations
- Use `useCallback` for functions passed to child components
- Use `React.memo` for components that render often with the same props

```tsx
// Bad
function MyComponent({ data }) {
  const sortedData = data.sort((a, b) => a.name.localeCompare(b.name));

  return (
    <ul>
      {sortedData.map(item => (
        <li key={item.id}>{item.name}</li>
      ))}
    </ul>
  );
}

// Good
function MyComponent({ data }) {
  const sortedData = useMemo(() => {
    return [...data].sort((a, b) => a.name.localeCompare(b.name));
  }, [data]);

  return (
    <ul>
      {sortedData.map(item => (
        <li key={item.id}>{item.name}</li>
      ))}
    </ul>
  );
}
```

### Rendering Optimization

- Avoid unnecessary re-renders
- Use proper keys for lists
- Avoid inline functions in render
- Use lazy loading for components and routes

```tsx
// Bad
function MyList({ items }) {
  return (
    <ul>
      {items.map((item, index) => (
        <li key={index} onClick={() => handleClick(item)}>
          {item.name}
        </li>
      ))}
    </ul>
  );
}

// Good
function MyList({ items }) {
  const handleItemClick = useCallback((item) => {
    // Handle click
  }, []);

  return (
    <ul>
      {items.map(item => (
        <li key={item.id}>
          <ListItem item={item} onClick={handleItemClick} />
        </li>
      ))}
    </ul>
  );
}
```

## UI Components

### shadcn UI Components

- Use shadcn UI components for consistent UI
- Follow the shadcn UI component patterns
- Customize components using the provided APIs
- Ensure proper dark mode support

```tsx
// Bad
<button
  className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
  onClick={handleClick}
>
  Click me
</button>

// Good
import { Button } from "@/components/ui/button";

<Button
  variant="default"
  onClick={handleClick}
>
  Click me
</Button>
```

### Component Customization

- Customize shadcn UI components using the provided APIs
- Extend components with additional functionality
- Maintain consistent styling across customized components
- Follow the component documentation

```tsx
// Example: Custom Button with gradient background
import { Button, ButtonProps } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface GradientButtonProps extends ButtonProps {}

export function GradientButton({ className, ...props }: GradientButtonProps) {
  return (
    <Button
      className={cn(
        "bg-gradient-to-r from-primary to-secondary hover:opacity-90",
        className
      )}
      {...props}
    />
  );
}
```

## Accessibility

### ARIA Attributes

- Use proper ARIA attributes
- Use semantic HTML elements
- Ensure keyboard navigation works correctly
- Provide proper labels for all interactive elements

```tsx
// Bad
<div onClick={handleClick}>Click me</div>

// Good
<button onClick={handleClick} aria-label="Click me">Click me</button>
```

### Focus Management

- Ensure proper focus management
- Use focus traps for modals and dialogs
- Provide visible focus indicators
- Ensure proper tab order

```tsx
// Bad
function Modal({ isOpen, onClose, children }) {
  if (!isOpen) return null;

  return (
    <div className="modal">
      <div className="modal-content">
        {children}
        <button onClick={onClose}>Close</button>
      </div>
    </div>
  );
}

// Good
function Modal({ isOpen, onClose, children }) {
  const modalRef = useRef(null);

  useEffect(() => {
    if (isOpen) {
      modalRef.current?.focus();
    }
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div
      className="modal"
      role="dialog"
      aria-modal="true"
      tabIndex={-1}
      ref={modalRef}
    >
      <div className="modal-content">
        {children}
        <button onClick={onClose}>Close</button>
      </div>
    </div>
  );
}
```

## Testing

### Unit Tests

- Write unit tests for all components and functions
- Use Jest and React Testing Library
- Test behavior, not implementation details
- Use mock data and mock functions

```tsx
// Component
function Counter() {
  const [count, setCount] = useState(0);

  const increment = () => {
    setCount(prevCount => prevCount + 1);
  };

  return (
    <div>
      <span data-testid="count">{count}</span>
      <button onClick={increment}>Increment</button>
    </div>
  );
}

// Test
import { render, screen, fireEvent } from '@testing-library/react';

test('increments count when button is clicked', () => {
  render(<Counter />);

  expect(screen.getByTestId('count')).toHaveTextContent('0');

  fireEvent.click(screen.getByText('Increment'));

  expect(screen.getByTestId('count')).toHaveTextContent('1');
});
```

### Integration Tests

- Write integration tests for complex components
- Test component interactions
- Use mock API responses
- Test error handling

```tsx
// Component
function UserList() {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    setLoading(true);
    fetch('/api/users')
      .then(res => res.json())
      .then(data => {
        setUsers(data);
        setLoading(false);
      })
      .catch(err => {
        setError(err);
        setLoading(false);
      });
  }, []);

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <ul>
      {users.map(user => (
        <li key={user.id}>{user.name}</li>
      ))}
    </ul>
  );
}

// Test
import { render, screen, waitFor } from '@testing-library/react';
import { rest } from 'msw';
import { setupServer } from 'msw/node';

const server = setupServer(
  rest.get('/api/users', (req, res, ctx) => {
    return res(
      ctx.json([
        { id: 1, name: 'John' },
        { id: 2, name: 'Jane' },
      ])
    );
  })
);

beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());

test('renders user list', async () => {
  render(<UserList />);

  expect(screen.getByText('Loading...')).toBeInTheDocument();

  await waitFor(() => {
    expect(screen.getByText('John')).toBeInTheDocument();
    expect(screen.getByText('Jane')).toBeInTheDocument();
  });
});
```
