"use client";

import React, { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import { ProductGrid } from "@/components/sections/product-grid";
import { Container } from "@/components/ui/container";

import { ShopCategoriesBar } from "@/components/shop/ShopCategoriesBar";
import { ShopFilter } from "@/components/shop/ShopFilter";
import { ProductSorting, ActiveFilter } from "@/components/shop/ProductSorting";
import { CustomBreadcrumb } from "@/components/ui/custom-breadcrumb";

// Sample products data for the shop page
const shopProducts = [
  {
    id: "wireless-headphones",
    name: "Wireless Noise Cancelling Headphones",
    category: "Electronics",
    price: 129.99,
    oldPrice: 249.99,
    discount: true,
    discountPercentage: 48,
    rating: 4.5,
    image: "/images/products/headphone.png"
  },
  {
    id: "organic-juice",
    name: "Organic Cold-Pressed Juice Set",
    category: "Food & Beverages",
    price: 34.99,
    discount: false,
    rating: 4.4,
    image: "/images/products/berry.png"
  },
  {
    id: "leather-wallet",
    name: "Premium Leather Minimalist Wallet",
    category: "Accessories",
    price: 79.99,
    oldPrice: 99.99,
    discount: true,
    discountPercentage: 20,
    rating: 4.7,
    image: "/images/products/headphone.png"
  },
  {
    id: "security-camera",
    name: "Smart Home Security Camera",
    category: "Electronics",
    price: 129.99,
    discount: false,
    rating: 4.8,
    image: "/images/products/berry.png"
  },
  {
    id: "fitness-tracker",
    name: "Fitness Tracker Watch",
    category: "Electronics",
    price: 59.99,
    oldPrice: 79.99,
    discount: true,
    discountPercentage: 25,
    rating: 4.3,
    image: "/images/products/headphone.png"
  },
  {
    id: "bluetooth-speaker",
    name: "Portable Bluetooth Speaker",
    category: "Electronics",
    price: 49.99,
    discount: false,
    rating: 4.6,
    image: "/images/products/berry.png"
  },
  {
    id: "water-bottle",
    name: "Stainless Steel Water Bottle",
    category: "Home & Kitchen",
    price: 24.99,
    oldPrice: 34.99,
    discount: true,
    discountPercentage: 30,
    rating: 4.9,
    image: "/images/products/headphone.png"
  },
  {
    id: "charging-pad",
    name: "Wireless Charging Pad",
    category: "Electronics",
    price: 29.99,
    discount: false,
    rating: 4.2,
    image: "/images/products/berry.png"
  },
  {
    id: "office-chair",
    name: "Ergonomic Office Chair",
    category: "Furniture",
    price: 199.99,
    oldPrice: 299.99,
    discount: true,
    discountPercentage: 33,
    rating: 4.7,
    image: "/images/products/headphone.png"
  },
  {
    id: "smart-plant-pot",
    name: "Smart Plant Pot",
    category: "Home & Garden",
    price: 39.99,
    discount: false,
    rating: 4.5,
    image: "/images/products/berry.png"
  }
];

export default function ShopPage() {
  const pathname = usePathname();
  const isShopPage = pathname === "/shop";

  // State for filtered products
  const [filteredProducts, setFilteredProducts] = useState(shopProducts);

  // State for active filters
  const [activeFilters, setActiveFilters] = useState({
    priceRange: { min: "", max: "" },
    brands: [] as string[],
    discounts: [] as string[],
    sizes: [] as string[],
    colors: [] as string[],
    ratings: null as number | null,
    fabrics: [] as string[],
    patterns: [] as string[],
    occasions: [] as string[],
    categories: [] as string[], // Array for multiple categories
    sortBy: "popularity" // default sort
  });

  // State for displaying active filter badges
  const [displayFilters, setDisplayFilters] = useState<ActiveFilter[]>([]);

  // Update display filters whenever active filters change
  useEffect(() => {
    const newDisplayFilters: ActiveFilter[] = [];

    // Price range filter
    if (activeFilters.priceRange.min && activeFilters.priceRange.max) {
      newDisplayFilters.push({
        type: 'priceRange',
        value: `${activeFilters.priceRange.min}-${activeFilters.priceRange.max}`,
        label: `Price: ${activeFilters.priceRange.min}-${activeFilters.priceRange.max}`
      });
    } else if (activeFilters.priceRange.min) {
      newDisplayFilters.push({
        type: 'priceRange',
        value: `min-${activeFilters.priceRange.min}`,
        label: `Price: ${activeFilters.priceRange.min}+`
      });
    } else if (activeFilters.priceRange.max) {
      newDisplayFilters.push({
        type: 'priceRange',
        value: `max-${activeFilters.priceRange.max}`,
        label: `Price: <${activeFilters.priceRange.max}`
      });
    }

    // Brand filters
    activeFilters.brands.forEach(brand => {
      newDisplayFilters.push({
        type: 'brand',
        value: brand,
        label: brand
      });
    });

    // Discount filters
    activeFilters.discounts.forEach(discount => {
      newDisplayFilters.push({
        type: 'discount',
        value: discount,
        label: discount
      });
    });

    // Size filters
    activeFilters.sizes.forEach(size => {
      newDisplayFilters.push({
        type: 'size',
        value: size,
        label: size
      });
    });

    // Color filters
    activeFilters.colors.forEach(color => {
      newDisplayFilters.push({
        type: 'color',
        value: color,
        label: `${color}`
      });
    });

    // Rating filter
    if (activeFilters.ratings) {
      newDisplayFilters.push({
        type: 'rating',
        value: activeFilters.ratings.toString(),
        label: `${activeFilters.ratings}★ & above`
      });
    }

    // Fabric filters
    activeFilters.fabrics.forEach(fabric => {
      newDisplayFilters.push({
        type: 'fabric',
        value: fabric,
        label: fabric
      });
    });

    // Pattern filters
    activeFilters.patterns.forEach(pattern => {
      newDisplayFilters.push({
        type: 'pattern',
        value: pattern,
        label: pattern
      });
    });

    // Occasion filters
    activeFilters.occasions.forEach(occasion => {
      newDisplayFilters.push({
        type: 'occasion',
        value: occasion,
        label: occasion
      });
    });

    // Category filters
    activeFilters.categories.forEach(category => {
      newDisplayFilters.push({
        type: 'category',
        value: category,
        label: `Category: ${category}`
      });
    });

    setDisplayFilters(newDisplayFilters);
  }, [activeFilters]);

  // Apply filters to products
  const applyFilters = (products: typeof shopProducts, filters: typeof activeFilters) => {
    return products.filter(product => {
      // Price filter
      if (filters.priceRange.min && parseFloat(filters.priceRange.min) > product.price) {
        return false;
      }
      if (filters.priceRange.max && parseFloat(filters.priceRange.max) < product.price) {
        return false;
      }

      // Category filter - check if any categories are selected
      if (filters.categories && filters.categories.length > 0) {
        // Check if product's category is in the selected categories
        if (!filters.categories.includes(product.category)) {
          return false;
        }
      }

      // Brand filter
      if (filters.brands && filters.brands.length > 0) {
        // In a real app, you would check if product.brand is in filters.brands
        // For now, we'll just return true since our sample data doesn't have brand property
      }

      // Rating filter
      if (filters.ratings && product.rating < filters.ratings) {
        return false;
      }

      // Discount filter
      if (filters.discounts && filters.discounts.length > 0) {
        if (!product.discount) return false;

        // Check if product meets any of the discount thresholds
        const hasValidDiscount = filters.discounts.some((discountFilter: string) => {
          const percentage = parseInt(discountFilter.split('%')[0]);
          return product.discountPercentage && product.discountPercentage >= percentage;
        });

        if (!hasValidDiscount) return false;
      }

      // Size filter (would need product.sizes in real data)
      // Color filter (would need product.colors in real data)
      // Fabric filter (would need product.fabric in real data)
      // Pattern filter (would need product.pattern in real data)
      // Occasion filter (would need product.occasion in real data)

      return true;
    });
  };

  // Handle filter change
  const handleFilterChange = (filterState: Partial<typeof activeFilters>) => {
    setActiveFilters(prevFilters => ({
      ...prevFilters,
      ...filterState
    }));

    // Apply filters first
    const filtered = applyFilters(shopProducts, {...activeFilters, ...filterState});

    // Then apply current sort
    const sortValue = activeFilters.sortBy;

    switch (sortValue) {
      case "price_asc":
        filtered.sort((a, b) => a.price - b.price);
        break;
      case "price_desc":
        filtered.sort((a, b) => b.price - a.price);
        break;
      case "newest":
        filtered.reverse();
        break;
      case "discount":
        filtered.sort((a, b) => {
          const discountA = a.discount && a.discountPercentage ? a.discountPercentage : 0;
          const discountB = b.discount && b.discountPercentage ? b.discountPercentage : 0;
          return discountB - discountA;
        });
        break;
    }

    setFilteredProducts(filtered);
  };

  // Handle removing a single filter
  const handleRemoveFilter = (filter: ActiveFilter) => {
    const newFilters = { ...activeFilters };

    switch (filter.type) {
      case 'priceRange':
        if (filter.value.startsWith('min-')) {
          newFilters.priceRange.min = "";
        } else if (filter.value.startsWith('max-')) {
          newFilters.priceRange.max = "";
        } else {
          newFilters.priceRange = { min: "", max: "" };
        }
        break;
      case 'brand':
        newFilters.brands = newFilters.brands.filter(b => b !== filter.value);
        break;
      case 'discount':
        newFilters.discounts = newFilters.discounts.filter(d => d !== filter.value);
        break;
      case 'size':
        newFilters.sizes = newFilters.sizes.filter(s => s !== filter.value);
        break;
      case 'color':
        newFilters.colors = newFilters.colors.filter(c => c !== filter.value);
        break;
      case 'rating':
        newFilters.ratings = null;
        break;
      case 'fabric':
        newFilters.fabrics = newFilters.fabrics.filter(f => f !== filter.value);
        break;
      case 'pattern':
        newFilters.patterns = newFilters.patterns.filter(p => p !== filter.value);
        break;
      case 'occasion':
        newFilters.occasions = newFilters.occasions.filter(o => o !== filter.value);
        break;
      case 'category':
        // Remove the specific category from the categories array
        newFilters.categories = newFilters.categories.filter(c => c !== filter.value);
        // Remove from display filters
        setDisplayFilters(prev => prev.filter(f => f.type !== 'category' || f.value !== filter.value));

        // If there are still categories selected, filter products accordingly
        if (newFilters.categories.length > 0) {
          // Apply all filters including the updated categories
          const filtered = applyFilters(shopProducts, newFilters);
          setFilteredProducts(filtered);

          // Update display filters to show remaining categories
          const categoryFilters = newFilters.categories.map(cat => ({
            type: 'category' as const,
            value: cat,
            label: `Category: ${cat}`
          }));

          setDisplayFilters(prev => {
            const nonCategoryFilters = prev.filter(f => f.type !== 'category');
            return [...nonCategoryFilters, ...categoryFilters];
          });
        } else {
          // Reset to all products if no categories are selected
          setFilteredProducts(shopProducts);
        }
        return;
    }

    setActiveFilters(newFilters);

    // Apply updated filters
    const filtered = applyFilters(shopProducts, newFilters);
    setFilteredProducts(filtered);
  };

  // Clear all filters
  const handleClearAllFilters = () => {
    const emptyFilters = {
      priceRange: { min: "", max: "" },
      brands: [] as string[],
      discounts: [] as string[],
      sizes: [] as string[],
      colors: [] as string[],
      ratings: null as number | null,
      fabrics: [] as string[],
      patterns: [] as string[],
      occasions: [] as string[],
      categories: [] as string[], // Reset categories array
      sortBy: activeFilters.sortBy // Keep the current sort option
    };

    setActiveFilters(emptyFilters);
    setFilteredProducts(shopProducts);

    // Clear category filters from display
    setDisplayFilters(prev => prev.filter(f => f.type !== 'category'));
  };

  // Handle sort change
  const handleSortChange = (sortValue: string) => {
    setActiveFilters({
      ...activeFilters,
      sortBy: sortValue
    });

    // Sort products based on selected option
    const sorted = [...filteredProducts];

    switch (sortValue) {
      case "price_asc":
        sorted.sort((a, b) => a.price - b.price);
        break;
      case "price_desc":
        sorted.sort((a, b) => b.price - a.price);
        break;
      case "newest":
        // In a real app, you would sort by date
        // For now, we'll just reverse the array to simulate "newest"
        sorted.reverse();
        break;
      case "discount":
        // Sort by discount percentage (if available)
        sorted.sort((a, b) => {
          const discountA = a.discount && a.discountPercentage ? a.discountPercentage : 0;
          const discountB = b.discount && b.discountPercentage ? b.discountPercentage : 0;
          return discountB - discountA;
        });
        break;
      default: // popularity
        // Reset to original filtered products
        return setFilteredProducts(applyFilters(shopProducts, activeFilters));
    }

    setFilteredProducts(sorted);
  };

  // Handle category change
  const handleCategoryChange = (categoryName: string) => {
    // Get current categories
    const currentCategories = [...activeFilters.categories];

    // Check if category is already selected
    const categoryIndex = currentCategories.indexOf(categoryName);

    // If category is already selected, remove it
    // If not, add it to the array
    if (categoryIndex !== -1) {
      currentCategories.splice(categoryIndex, 1);
    } else {
      currentCategories.push(categoryName);
    }

    // Update active filters with new categories array
    const newFilters = {
      ...activeFilters,
      categories: currentCategories
    };

    setActiveFilters(newFilters);

    // Filter products based on selected categories and other active filters
    if (currentCategories.length > 0) {
      // Apply all filters including the updated categories
      const filtered = applyFilters(shopProducts, newFilters);
      setFilteredProducts(filtered);

      // Update display filters to show all selected categories
      const categoryFilters = currentCategories.map(cat => ({
        type: 'category' as const,
        value: cat,
        label: `Category: ${cat}`
      }));

      // Remove any existing category filters and add new ones
      setDisplayFilters(prev => {
        const nonCategoryFilters = prev.filter(f => f.type !== 'category');
        return [...nonCategoryFilters, ...categoryFilters];
      });
    } else {
      // Reset to all products if no categories are selected
      setFilteredProducts(shopProducts);
      setDisplayFilters(prev => prev.filter(f => f.type !== 'category'));
    }
  };

  return (
    <div className="bg-background min-h-screen pb-8">
      {/* Breadcrumb */}
      <CustomBreadcrumb
        items={[
          { label: "Home", href: "/" },
          { label: "Shop" }
        ]}
        backgroundColor="bg-[#E8F7F6]"
      />

      {/* Categories Section */}
      <ShopCategoriesBar
        activeCategories={activeFilters.categories}
        onCategoryChange={handleCategoryChange}
      />

      {/* Shop Content with Filters and Products */}
      <Container>
        <div className="flex flex-col lg:flex-row gap-6">
          {/* Filter Sidebar */}
          <div className="w-full lg:w-1/4 xl:w-1/5">
            <ShopFilter onFilterChange={handleFilterChange} />
          </div>

          {/* Products Grid */}
          <div className="w-full lg:w-3/4 xl:w-4/5">
            {/* Product Sorting */}
            <ProductSorting
              totalProducts={filteredProducts.length}
              activeSortOption={activeFilters.sortBy}
              onSortChange={handleSortChange}
              activeFilters={displayFilters}
              onRemoveFilter={handleRemoveFilter}
              onClearAllFilters={handleClearAllFilters}
            />

            <ProductGrid
              title="All Products"
              products={filteredProducts}
              shopMoreLink="/products"
              showTitle={!isShopPage}
              useContainer={false}
            />
          </div>
        </div>
      </Container>
    </div>
  );
}
