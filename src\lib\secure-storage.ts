/**
 * Secure storage utilities for handling sensitive data like tokens
 */

// Simple encryption/decryption using base64 (for basic obfuscation)
// In production, consider using a more robust encryption library
const ENCRYPTION_KEY = 'multi-tenant-ecommerce-2024';

/**
 * Simple encryption function
 */
function encrypt(text: string): string {
  try {
    // Simple XOR encryption with base64 encoding
    const encrypted = text
      .split('')
      .map((char, i) => 
        String.fromCharCode(char.charCodeAt(0) ^ ENCRYPTION_KEY.charCodeAt(i % ENCRYPTION_KEY.length))
      )
      .join('');
    
    return btoa(encrypted);
  } catch {
    return text; // Fallback to plain text if encryption fails
  }
}

/**
 * Simple decryption function
 */
function decrypt(encryptedText: string): string {
  try {
    const decoded = atob(encryptedText);
    const decrypted = decoded
      .split('')
      .map((char, i) => 
        String.fromCharCode(char.charCodeAt(0) ^ ENCRYPTION_KEY.charCodeAt(i % ENCRYPTION_KEY.length))
      )
      .join('');
    
    return decrypted;
  } catch {
    return encryptedText; // Fallback to return as-is if decryption fails
  }
}

/**
 * Secure storage interface
 */
export class SecureStorage {
  /**
   * Store encrypted data in localStorage
   */
  static setItem(key: string, value: string): void {
    if (typeof window === 'undefined') return;
    
    try {
      const encrypted = encrypt(value);
      localStorage.setItem(key, encrypted);
    } catch (error) {
      console.warn('Failed to store encrypted data:', error);
      // Fallback to plain storage
      localStorage.setItem(key, value);
    }
  }

  /**
   * Retrieve and decrypt data from localStorage
   */
  static getItem(key: string): string | null {
    if (typeof window === 'undefined') return null;
    
    try {
      const encrypted = localStorage.getItem(key);
      if (!encrypted) return null;
      
      return decrypt(encrypted);
    } catch (error) {
      console.warn('Failed to decrypt data:', error);
      // Fallback to plain retrieval
      return localStorage.getItem(key);
    }
  }

  /**
   * Remove item from localStorage
   */
  static removeItem(key: string): void {
    if (typeof window === 'undefined') return;
    localStorage.removeItem(key);
  }

  /**
   * Clear all items from localStorage
   */
  static clear(): void {
    if (typeof window === 'undefined') return;
    localStorage.clear();
  }

  /**
   * Store token with expiration
   */
  static setToken(key: string, token: string, expiresIn?: number): void {
    const tokenData = {
      token,
      timestamp: Date.now(),
      expiresIn: expiresIn || 7 * 24 * 60 * 60 * 1000, // Default 7 days
    };
    
    this.setItem(key, JSON.stringify(tokenData));
  }

  /**
   * Get token and check if it's expired
   */
  static getToken(key: string): string | null {
    try {
      const tokenDataStr = this.getItem(key);
      if (!tokenDataStr) return null;
      
      const tokenData = JSON.parse(tokenDataStr);
      const now = Date.now();
      
      // Check if token is expired
      if (now - tokenData.timestamp > tokenData.expiresIn) {
        this.removeItem(key);
        return null;
      }
      
      return tokenData.token;
    } catch (error) {
      console.warn('Failed to parse token data:', error);
      this.removeItem(key);
      return null;
    }
  }

  /**
   * Check if token exists and is valid
   */
  static hasValidToken(key: string): boolean {
    return this.getToken(key) !== null;
  }
}

/**
 * Cookie utilities for secure cookie management
 */
export class SecureCookies {
  /**
   * Set secure cookie
   */
  static setCookie(
    name: string, 
    value: string, 
    options: {
      maxAge?: number;
      secure?: boolean;
      httpOnly?: boolean;
      sameSite?: 'Strict' | 'Lax' | 'None';
      path?: string;
    } = {}
  ): void {
    if (typeof window === 'undefined') return;
    
    const {
      maxAge = 7 * 24 * 60 * 60, // 7 days in seconds
      secure = true,
      sameSite = 'Strict',
      path = '/',
    } = options;
    
    let cookieString = `${name}=${encodeURIComponent(value)}`;
    cookieString += `; path=${path}`;
    cookieString += `; max-age=${maxAge}`;
    cookieString += `; SameSite=${sameSite}`;
    
    if (secure && window.location.protocol === 'https:') {
      cookieString += '; Secure';
    }
    
    document.cookie = cookieString;
  }

  /**
   * Get cookie value
   */
  static getCookie(name: string): string | null {
    if (typeof window === 'undefined') return null;
    
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    
    if (parts.length === 2) {
      const cookieValue = parts.pop()?.split(';').shift();
      return cookieValue ? decodeURIComponent(cookieValue) : null;
    }
    
    return null;
  }

  /**
   * Remove cookie
   */
  static removeCookie(name: string, path: string = '/'): void {
    if (typeof window === 'undefined') return;
    document.cookie = `${name}=; path=${path}; max-age=0; SameSite=Strict`;
  }
}

/**
 * Session timeout management
 */
export class SessionManager {
  private static timeoutId: NodeJS.Timeout | null = null;
  private static warningTimeoutId: NodeJS.Timeout | null = null;
  
  /**
   * Start session timeout
   */
  static startTimeout(
    timeoutMs: number = 30 * 60 * 1000, // 30 minutes
    warningMs: number = 5 * 60 * 1000, // 5 minutes before timeout
    onWarning?: () => void,
    onTimeout?: () => void
  ): void {
    this.clearTimeout();
    
    // Set warning timeout
    this.warningTimeoutId = setTimeout(() => {
      onWarning?.();
    }, timeoutMs - warningMs);
    
    // Set main timeout
    this.timeoutId = setTimeout(() => {
      onTimeout?.();
      this.clearTimeout();
    }, timeoutMs);
  }

  /**
   * Reset session timeout
   */
  static resetTimeout(): void {
    if (this.timeoutId || this.warningTimeoutId) {
      this.clearTimeout();
      // Restart with same settings would require storing the original parameters
      // For now, just clear - the calling code should restart as needed
    }
  }

  /**
   * Clear session timeout
   */
  static clearTimeout(): void {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
    if (this.warningTimeoutId) {
      clearTimeout(this.warningTimeoutId);
      this.warningTimeoutId = null;
    }
  }
}
